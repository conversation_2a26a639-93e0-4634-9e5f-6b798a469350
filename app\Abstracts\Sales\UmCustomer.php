<?php
namespace App\Abstracts\Sales;
use Illuminate\Http\Request;
use App\Model\{TypeTransaction,UmCustomerDetail,Journal,JournalDetail,Contact,UmCustomerPaid,Account,AccountDefault,CekGiro,CashTransaction,CashTransactionDetail};
use App\Model\UmCustomer as UC;
use App\Utils\TransactionCode;
use Response;
use DB;
use Exception;

class UmCustomer 
{
  protected $guarded = ['id'];
  public static function store(Request $request){
    $request->validate([
      'company_id' => 'required',
      'date_transaction' => 'required',
      'amount' => 'required|integer',
      'contact_id' => 'required',
    ]);
    $code = new TransactionCode($request->company_id, "depositCustomer");
    $code->setCode();
    $trx_code = $code->getCode();
    $contact=Contact::find($request->contact_id);
    $tptrx=TypeTransaction::where('slug','depositCustomer')->first();
    $data=[
      'company_id' => $request->company_id,
      'contact_id' => $request->contact_id,
      'type_transaction_id' => $tptrx->id,
      'created_by' => auth()->id(),
      'code' => $trx_code,
      'date_transaction' => dateDB($request->date_transaction),
      'description' => $request->description,
      'debet' => 0,
      'credit' => $request->amount,
    ];
    if(isset($request->sales_order_id)){
      $data['sales_order_id']=$request->sales_order_id;
    }
    $um=UC::create($data);
    $d=UmCustomerDetail::create([
      'header_id' => $um->id,
      'type_transaction_id' => $tptrx->id,
      'code' => $trx_code,
      'date_transaction' => dateDB($request->date_transaction),
      'debet' => 0,
      'credit' => $request->amount,
      'description' => $request->description
    ]);
    $cek_giro_amount=0;
    $cash_amount=0;
    $account_default=AccountDefault::first();
    $akun_kas_list=[];
    $akun_kas_amount=[];
    foreach ($request->detail as $key => $value) {
      if (empty($value)) {
        continue;
      }
      $acc=Account::find($value['cash_account_id']);
      UmCustomerPaid::create([
        'header_id' => $um->id,
        'type_paid' => $value['type'],
        'type_transaction_id' => $tptrx->id,
        'reff_id' => ($value['type']==1?$value['cash_account_id']:$value['cek_giro_id']),
        'code' => ($value['type']==1?$acc->name:$value['cek_giro_name']),
        'amount' => $value['amount']
      ]);
      if (UC::isGiro($value['type'])) {
        //jika menggunakan cek/giro
        CekGiro::find($value['cek_giro_id'])->update([
          'is_used' => 1,
          'reff_no' => $trx_code
        ]);
        $cek_giro_amount+=$value['amount'];
      } else {
        if (!in_array($value['cash_account_id'],$akun_kas_list)) {
          $akun_kas_list[]=$value['cash_account_id'];
        }
        $akun_kas_amount[$value['cash_account_id']][]=$value['amount'];
        $cash_amount+=$value['amount'];
        //jika menggunakan kas
      }
    }
    //
    if ($request->is_lebih_bayar == 1) {
      foreach ($akun_kas_list as $val) {
        $cekCC=cekCashCount($request->company_id,$val);
        if ($cekCC) {
          return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
        }
        $acc=Account::find($val);
        $ct=CashTransaction::create([
          'company_id' => $request->company_id,
          'type_transaction_id' => $tptrx->id,
          'code' => $trx_code,
          'reff' => $trx_code,
          'jenis' => 2,
          'type' => $acc->no_cash_bank,
          'description' => $request->description,
          'total' => $cash_amount,//array_sum($akun_kas_amount[$val]),
          'account_id' => $val,
          'date_transaction' => dateDB($request->date_transaction),
          'status_cost' => 3
        ]);
        CashTransactionDetail::create([
          'header_id' => $ct->id,
          'account_id' => $contact->akun_um_customer,
          'contact_id' => $contact->id,
          'amount' => $cash_amount,//array_sum($akun_kas_amount[$val]),
        ]);
      }
    }
    //jika bukan lebih bayar
    if (!$request->is_lebih_bayar || $request->is_lebih_bayar == 0) {
      $j=Journal::create([
        'company_id' => $request->company_id,
        'type_transaction_id' => $tptrx->id,
        'date_transaction' => dateDB($request->date_transaction),
        'created_by' => auth()->id(),
        'code' => $trx_code,
        'description' => $request->description,
        'status' => 2
      ]);
      if (empty($contact->akun_um_customer)) {
        if (empty($account_default->deposit_customer)){
          throw new Exception('Akun Default Deposit Customer belum ditentukan');
        }
      }
      $cekCC=cekCashCount($request->company_id,$contact->akun_um_customer);
      if ($cekCC) {
        throw new Exception('Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas');
      }
      $jcr=JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $contact->akun_um_customer ?? $account_default->deposit_customer,
        'debet' => 0,
        'credit' => $request->amount,//total deposit
      ]);
      if ($cek_giro_amount>0) {
        if (empty($account_default->cek_giro_masuk)) {
          throw new Exception('Akun Default Giro Masuk belum ditentukan');
        }
        $cekCC=cekCashCount($request->company_id,$account_default->cek_giro_masuk);
        if ($cekCC) {
          throw new Exception('Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas');
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $account_default->cek_giro_masuk,
          'debet' => $cek_giro_amount,
          'credit' => 0,
        ]);
      }
      if ($cash_amount>0) {
        foreach ($akun_kas_list as $val) {
          $cekCC=cekCashCount($request->company_id,$val);
          if ($cekCC) {
            throw new Exception('Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas');
          }
          $jdb=JournalDetail::create([
            'header_id' => $j->id,
            'account_id' => $val,
            'debet' => $cash_amount,//array_sum($akun_kas_amount[$val]),
            'credit' => 0,
          ]);
          $acc=Account::find($val);
          $ct=CashTransaction::create([
            'company_id' => $request->company_id,
            'type_transaction_id' => $tptrx->id,
            'code' => $trx_code,
            'reff' => $trx_code,
            'jenis' => 2,
            'type' => $acc->no_cash_bank,
            'description' => $request->description,
            'total' => $cash_amount,//array_sum($akun_kas_amount[$val]),
            'account_id' => $val,
            'date_transaction' => dateDB($request->date_transaction),
            'status_cost' => 3
          ]);
          CashTransactionDetail::create([
            'header_id' => $ct->id,
            'account_id' => $contact->akun_um_customer ?? $account_default->deposit_customer,
            'contact_id' => $contact->id,
            'amount' => $cash_amount,//array_sum($akun_kas_amount[$val]),
          ]);
        }
      }
      if ($request->lebih_bayar>0) {
        if (empty($account_default->lebih_bayar_hutang)) {
          throw new Exception('Akun Default Lebih Bayar Hutang belum ditentukan');
        }
        $cekCC=cekCashCount($request->company_id,$account_default->lebih_bayar_hutang);
        if ($cekCC) {
          throw new Exception('Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas');
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $account_default->lebih_bayar_hutang,
          'debet' => 0,
          'credit' => $request->lebih_bayar,
        ]);
      }
      UC::find($um->id)->update(['journal_id' => $j->id]);
      $jdfind = JournalDetail::where('header_id', $j->id)->selectRaw('header_id, SUM(journal_details.debet) as total_debet, SUM(journal_details.credit) as total_credit')->first();
      $j->update(['debet' => $jdfind->total_debet, 'credit' => $jdfind->total_credit]);
    }
  }
}
