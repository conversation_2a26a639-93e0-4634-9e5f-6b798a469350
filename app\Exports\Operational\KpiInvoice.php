<?php
namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class KpiInvoice implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        return $report_controller->getInvoiceReport($this->request);
    }
    public function headings(): array
    {
        return [
            'No JO',
            'Tanggal Pembuatan JO',
            'No. Invoice',
            'Pembuat JO',
            'Tanggal Penyelesaian',
            'Tanggal Target Pembuatan Invoice',
            'Tanggal Pembuatan Invoice'
        ];
    }
    public function map($invoice): array
    {
        return [
            $invoice->job_order_code ?? '-',
            $invoice->created_at ?? '-',
            $invoice->invoice_code ?? '-',
            $invoice->creator ?? '-',
            $invoice->finished_date ?? '-',
            $invoice->planning_date ?? '-',
            $invoice->date_invoice ?? '-'
        ];
    }
}
