<?php

namespace App\Abstracts\Operational;

use DB;
use Carbon\Carbon;
use Exception;

class Invoice
{
    protected static $table = 'invoices';

    /*
      Date : 21-04-2021
      Description : Memvalidasi keberadaan data
      Developer : Didin
      Status : Create
    */
    public static function query($params =  []) {
        $request = self::fetchFilter($params);
        $item = DB::table(self::$table)
        ->join('contacts', 'contacts.id', self::$table . '.customer_id')
        ->join('companies', 'companies.id', self::$table . '.company_id')
        ->leftjoin(
          DB::raw('(select 
          GROUP_CONCAT(distinct job_orders.aju_number SEPARATOR ", ") as aju,
          GROUP_CONCAT(distinct job_orders.no_bl SEPARATOR ", ") as bl, 
          GROUP_CONCAT(distinct work_orders.item_name SEPARATOR ", ") as item_name, 
          GROUP_CONCAT(distinct vessels.name SEPARATOR ", ") as vessel_name, 
          header_id from invoice_details 
          left join job_orders on job_orders.id = invoice_details.job_order_id 
          left join work_orders on work_orders.id = job_orders.work_order_id 
          left join vessels on vessels.id = work_orders.vessel_id 
          group by invoice_details.header_id) as Y'),'Y.header_id','invoices.id')
        ->leftjoin('receivables', 'receivables.id', self::$table . '.receivable_id');


        // Filter customer, wilayah, status, dan periode
        $tgl_awal = $request['tgl_awal'];
        $tgl_awal = $tgl_awal != null ? preg_replace('/([0-9]+)-([0-9]+)-([0-9]+)/', '$3-$2-$1', $tgl_awal) : '';
        $tgl_akhir = $request['tgl_akhir'];
        $tgl_akhir = $tgl_akhir != null ? preg_replace('/([0-9]+)-([0-9]+)-([0-9]+)/', '$3-$2-$1', $tgl_akhir) : '';
        $item = $tgl_awal != '' && $tgl_akhir != '' ? $item->whereBetween('date_invoice', [$tgl_awal, $tgl_akhir]) : $item;

        $company_id = $request['company_id'];
        $company_id = $company_id != null ? $company_id : '';
        $item = $company_id != '' ? $item->where('company_id', $company_id) : $item;
        $customer_id = $request['customer_id'];
        $customer_id = $customer_id != null ? $customer_id : '';
        $item = $customer_id != '' ? $item->where('customer_id', $customer_id) : $item;
        $status = $request['status'];
        $status = $status != null ? $status : '';
        $item = $status != '' ? $item->where('status', $status) : $item;
        if (isset($request['is_other'])) {
          $item = $item->where('is_other', $request['is_other']);
        }

        if($request['is_sales_order'] == 1) {
            $so = DB::table('invoice_details');
            $so = $so->whereNotNull('job_order_id');
            $so = $so->whereRaw('job_order_id IN (SELECT job_order_id FROM sales_orders)');
            $so = $so->select('invoice_details.header_id');
            $so = $so->toSql();
            $item = $item->whereRaw(self::$table . ".id IN ($so)");
        }

        if($request['is_operational'] == 1) {
            $so = DB::table('invoice_details');
            $so = $so->whereNotNull('job_order_id');
            $so = $so->whereRaw('(cost_type_id IS NULL AND job_order_id IS NULL) OR job_order_id IN (SELECT job_order_id FROM sales_orders)');
            $so = $so->select('invoice_details.header_id');
            $so = $so->toSql();
            $item = $item->whereRaw(self::$table . ".id NOT IN ($so)");
        }

        if (isset($request['customer_id_array'])) {
          $wr = "";
          $arr = explode(",", $request['customer_id_array']);
          $wr.=' invoices.customer_id IN (';
          foreach ($arr as $key => $value) {
            if (empty($value)) {
              continue;
            }
            if (end($arr) == $value) {
              $wr.="'".$value."'";
            } else {
              $wr.="'".$value."'".',';
            }
          }
          $wr.=")";
          $item = $item->whereRaw($wr);
        }

        if (isset($request['not_customer_id_array'])) {
          $wr = "";
          $arr = explode(",", $request['not_customer_id_array']);
          $wr.=' invoices.customer_id NOT IN (';
          foreach ($arr as $key => $value) {
            if (empty($value)) {
              continue;
            }
            if (end($arr) == $value) {
              $wr.="'".$value."'";
            } else {
              $wr.="'".$value."'".',';
            }
          }
          $wr.=")";
          $item = $item->whereRaw($wr);
        }

        if (isset($request['status_array'])) {
          $wr = "";
          $arr = explode(",", $request['status_array']);
          $wr.=' invoices.status IN (';
          foreach ($arr as $key => $value) {
            if (empty($value)) {
              continue;
            }
            if (end($arr) == $value) {
              $wr.="'".$value."'";
            } else {
              $wr.="'".$value."'".',';
            }
          }
          $wr.=")";
          $item = $item->whereRaw($wr);
        }

        if (isset($request['not_status_array'])) {
          $wr = "";
          $arr = explode(",", $request['not_status_array']);
          $wr.=' invoices.status NOT IN (';
          foreach ($arr as $key => $value) {
            if (empty($value)) {
              continue;
            }
            if (end($arr) == $value) {
              $wr.="'".$value."'";
            } else {
              $wr.="'".$value."'".',';
            }
          }
          $wr.=")";
          $item = $item->whereRaw($wr);
        }


        $item = $item
        ->select(
          'invoices.*',
          DB::raw('IF(invoices.type = 1, "Invoice Jual", "Invoice Reimburse") as type_name'),
          'companies.name AS company_name',
          'contacts.name AS customer_name',
          'Y.aju',
          'Y.bl',
          'Y.item_name',
          'Y.vessel_name',
          DB::raw('IF(invoices.status = 1, "Diajukan", IF(invoices.status = 6, "Void", IF(invoices.status = 2, "Draft", IF(invoices.status = 3, "Posting", IF(invoices.status = 4, "Terbayar Sebagian", IF(invoices.status = 5, "Lunas", "-")))))) as invoice_status'),
          DB::raw("(grand_total+grand_total_additional) as total"),
          DB::raw("receivables.credit as pembayaran_sebagian")
          );

        return $item;
    }

    /*
      Date : 21-04-2021
      Description : Mendapatkan parameter filter data
      Developer : Didin
      Status : Create
    */
    public static function fetchFilter($params = []) {
        $request = [];
        $request['customer_id'] = $params['customer_id'] ?? null;
        $request['tgl_awal'] = $params['tgl_awal'] ?? null;
        $request['tgl_akhir'] = $params['tgl_akhir'] ?? null;
        $request['company_id'] = $params['company_id'] ?? null;
        $request['customer_id'] = $params['customer_id'] ?? null;
        $request['status'] = $params['status'] ?? null;
        $request['is_sales_order'] = $params['is_sales_order'] ?? null;
        $request['is_operational'] = $params['is_operational'] ?? null;
        $request['customer_id_array'] = $params['customer_id_array'] ?? null;
        $request['not_customer_id_array'] = $params['not_customer_id_array'] ?? null;
        $request['status_array'] = $params['status_array'] ?? null;
        $request['not_status_array'] = $params['not_status_array'] ?? null;
        $request['is_other'] = $params['is_other'] ?? null;

        return $request;
    }

    /*
      Date : 12-02-2021
      Description : Memvalidasi keberadaan data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id);
        
        $dt = $dt->first();

        if(!$dt) {
            throw new Exception('Data not found');
        }
    }
}
