<?php

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class AssetDepresiasi2 implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    protected $data;

    public function __construct($data) {
        $this->data = $data['asset'];
    }

    public function array(): array
    {
        $result = [];

        foreach ($this->data as $v => $asset) {
            $akumulasi = collect($asset['month'])->filter()->sum();
            $nilai_perolehan = $asset['beban_bulan'] * $asset['umur_ekonomis'];

            $result[] = [
                $asset['code'],
                $asset['name'],
                $asset['group_name'],
                $asset['purchase_price'],
                $asset['account_name'],
                $asset['nilai_buku'],
                $asset['description'],
            ];
        }

        return $result;
    }

    public function headings(): array
    {
        return [
            'Kode',
            'Nama Asset',
            'Nama Kelompok',
            'Nilai Perolehan',
            'Akumulasi Depresiasi',
            'Nilai Buku',
            'Keterangan'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        // Bold header row
        $sheet->getStyle('A1:' . $highestColumn . '1')->getFont()->setBold(true);

        // Center align header
        $sheet->getStyle('A1:' . $highestColumn . '1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Border all cells
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN)
            ->setColor(new Color('000000'));

        // Freeze header row
        $sheet->freezePane('A2');
    }
}
