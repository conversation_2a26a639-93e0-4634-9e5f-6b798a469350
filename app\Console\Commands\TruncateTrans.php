<?php
namespace App\Console\Commands;
use Illuminate\Console\Command;
use DB;
class TruncateTrans extends Command
{
    protected $signature = 'db:truncatetransaksi';
    protected $description = 'CLI DB truncate transaksi';
    public function __construct()
    {
        parent::__construct();
    }
    public function handle()
    {
        $this->truncateTransaksi();
       $this->info('sukses truncate transaksi');
    }
    public function truncateTransaksi(){
        DB::statement('SET foreign_key_checks = 0');
        $tables = [
            'work_order_details',
            'work_order_drafts',
            'work_orders',
            'job_order_detail_outputs',
            'job_order_containers',
            'job_order_costs',
            'job_order_details',
            'job_order_documents',
            'job_order_receivers',
            'job_order_transits',
            'invoice_job_order',
            'job_orders',
            'invoice_details',
            'invoice_job_order',
            'invoice_joins',
            'invoice_taxes',
            'invoice_vendor_details',
            'invoice_vendor_taxes',
            'invoice_vendors',
            'invoices',
            'inquery_activities',
            'inqueries',
            'handling_vehicles',
            'handlings',
            'gate_in_containers',
            'gate_activities',
            'failed_jobs',
            'email_logs',
            'emails',
            'driver_messages',
            'driver_commissions',
            'temp_driver_commissions',
            'delivery_reject_histories',
            'delivery_order_status_logs',
            'delivery_order_routes',
            'delivery_order_photos',
            'delivery_order_ongoing_jobs',
            'delivery_order_drivers',
            'delivery_manifest_status_logs',
            'delivery_manifests',
            'movement_container_details',
            'movement_containers',
            'manifest_costs',
            'manifest_details',
            'manifest_email_logs',
            'manifests',
            'lead_documents',
            'lead_activities',
            'leads',
            'journal_details',
            'journals',
            'jobs',
            'job_status_histories',
            'job_packets',
            'debt_details',
            'debt_payments',
            'debts',
            'container_inspection_details',
            'container_inspections',
            'claim_details',
            'register_bbm',
            'register_bbm_details',
            'write_offs',
            'write_off_details',
            'claim_category_details',
            'claim_sales',
            'claim_sale_details',
            'claims',
            'closing',
            'cek_giros',
            'cash_allocations',
            'cash_allocation_logs',
            'cash_advance_reports',
            'cash_advance_statuses',
            'cash_advance_transactions',
            'cash_advances',
            'cash_category_details',
            'cash_counts',
            'cash_count_details',
            'cash_migrations',
            'cash_transaction_details',
            'cash_transactions',
            'nota_credits',
            'nota_debets',
            'business',
            'bills',
            'bill_payments',
            'bill_payment_steps',
            'bill_files',
            'bill_details',
            'assets',
            'asset_sales_details',
            'asset_sales',
            'asset_purchases',
            'asset_purchase_details',
            'asset_depreciations',
            'asset_afkirs',
            'warehouses',
            'warehouse_stock_details',
            'warehouse_stocks',
            'warehouse_receipt_details',
            'warehouse_receipts',
            'warehouse_receipt_billings',
            'warehouse_maps',
            'vulkanisir_request_details',
            'vulkanisir_requests',
            'voyage_schedules',
            'voyage_receipts',
            'vehicle_insurances',
            'vehicle_trailer_connections',
            'vehicle_maintenance_details',
            'vehicle_drivers',
            'vehicle_distances',
            'vehicle_contacts',
            'vehicle_checklist_detail_items',
            'vehicle_checklist_detail_bodies',
            'vehicle_checklist_items',
            'vehicle_mutation_in_details',
            'vehicle_mutation_ins',
            'vehicle_mutation_out_details',
            'vehicle_mutation_outs',
            'vehicle_mutation_request_details',
            'vehicle_mutation_requests',
            'using_tires',
            'using_item_details',
            'using_items',
            'um_supplier_details',
            'um_supplier_paids',
            'um_suppliers',
            'um_customer_details',
            'um_customer_paids',
            'um_customers',
            'tire_manufacturers',
            'submission_costs',
            'stuffing_vehicles',
            'stuffings',
            'stok_opname_warehouse_details',
            'stok_opname_warehouses',
            'stock_adjustment_details',
            'stock_adjustments',
            'sales_order_details',
            'sales_order_return_details',
            'sales_order_returns',
            'sales_orders',
            'stock_transactions',
            'stock_initials',
            'stock_adjustment_details',
            'stock_adjustments',
            'stock_transactions_report',
            'storings',
            'shipment_statuses',
            'sales_order_return_receipts',
            'receivable_confirm_details',
            'receivable_confirms',
            'receivable_details',
            'receivables',
            'receipt_details',
            'receipt_list_details',
            'receipt_lists',
            'retur_details',
            'retur_receipt_details',
            'retur_receipts',
            'returs',
            'putaways',
            'purchase_order_details',
            'purchase_order_purchase_request',
            'purchase_order_receipt_details',
            'purchase_order_return_details',
            'purchase_order_returns',
            'purchase_orders',
            'purchase_request_details',
            'purchase_requests',
            'tire_on_off_details',
            'tire_on_offs',
            'tax_invoices',
            'picking_details',
            'pickings',
            'payable_details',
            'payables',
            'pallet_usage_details',
            'pallet_usages',
            'packaging_new_items',
            'packaging_old_items',
            'packagings',
            'nota_debets',
            'nota_credits'
        ];
        $filtered=[];
        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                $filtered[]=$table;
            }
        }
        foreach ($filtered as $key => $table) {
                DB::table($table)->truncate();
        }
        DB::statement('SET foreign_key_checks = 1');

    }
}
