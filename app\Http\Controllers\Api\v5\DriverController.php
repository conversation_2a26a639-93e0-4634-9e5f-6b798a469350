<?php

namespace App\Http\Controllers\Api\v5;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Contact\ContactLocation;
use Exception;
use App\Abstracts\Operational\DeliveryOrderDriver;
use App\Abstracts\Setting\User;
use App\Abstracts\Vehicle\VehicleDriver;
use App\Abstracts\Vehicle\VehicleLocation;
use App\Model\DeliveryOrderDriver as ModelDeliveryOrderDriver;
use App\Model\Vehicle;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DriverController extends Controller
{

    /*
      Date : 09-07-2021
      Description : Simpan lokasi driver 
      Developer : Didin
      Status : Create
    */
    public function storeLocation(Request $request)
    {
        $request->validate([
            'longitude' => 'required',
            'latitude' => 'required',
        ]);
        $resp = [];
        $resp['message'] = 'OK';
        $statusCode = 200;
        DB::beginTransaction();
        try {
            // $params = $request->all();
            $contact['longitude'] = $request->longitude;
            $contact['latitude'] = $request->latitude;
            $contact['delivery_order_driver_id'] = $request->delivery_order_driver_id;
            $contact['contact_id'] = auth()->user()->contact_id;
            ContactLocation::store($contact);

            // Jika terdapat DO/SJ maka simpan juga ke VehicleLocation
            if($request->delivery_order_driver_id){
                $dod = ModelDeliveryOrderDriver::find($request->delivery_order_driver_id);

                $vehicle['longitude'] = $request->longitude;
                $vehicle['latitude'] = $request->latitude;
                $vehicle['delivery_order_driver_id'] = $request->delivery_order_driver_id;
                $vehicle['vehicle_id'] = $dod->vehicle_id;
                if($dod->vehicle_id){
                    VehicleLocation::store($vehicle);
                    Vehicle::where('id', $dod->vehicle_id)->update([
                        'last_latitude' => $request->latitude,
                        'last_longitude' => $request->longitude,
                        'last_update_latlng' => Carbon::now()
                    ]);
                }
            }

            DeliveryOrderDriver::setJourneyDistance($contact['contact_id']);
            DB::commit();
        } catch(\Exception $e) {
            DB::rollback();
            $statusCode = 421;
            $resp['message'] = $e->getMessage();
        }

        return response()->json($resp, $statusCode);
    }

    /*
      Date : 09-07-2021
      Description : Logout 
      Developer : Didin
      Status : Create
    */
    public function logout(Request $request)
    {
        $resp = $this->auth->logout($request);

        return $resp;
    }

    /*
      Date : 09-07-2021
      Description : Ubah password 
      Developer : Didin
      Status : Edit
    */
    public function changePassword(Request $request)
    {
        $msg = 'OK';
        $status_code = 200;

        DB::beginTransaction();
        try {
            $id = auth()->id();
            User::changePassword($request->input('password'), $request->input('password_confirm'), $id);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $msg = $e->getMessage();
            $status_code = 421;
        }

        $data = [];
        $data['message'] = $msg;

        return response()->json($data, $status_code);
    }

    /*
      Date : 09-07-2021
      Description : Menampilkan summary pengiriman barang 
      Developer : Didin
      Status : Edit
    */
    public function showShipmentSummary(Request $request)
    {
        $user = auth()->user();

        $vehicle = VehicleDriver::showVehicleByDriver($user->contact_id);
        if($vehicle){
            $message = 'OK';
            $dt = DeliveryOrderDriver::indexShipmentSummary($user->contact_id, $vehicle->id);
        } else {
            $message = 'No Vehicle Data!';
            $dt = [];
        }
        $resp['message']  = $message;
        $resp['data'] = $dt;
        

        return response()->json($resp);
    }
}
