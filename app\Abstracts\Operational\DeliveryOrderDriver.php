<?php

namespace App\Abstracts\Operational;

use Carbon\Carbon;
use Exception;
use App\Model\delivery_order_driver AS M;
use App\Abstracts\Setting\JobStatus;
use App\Abstracts\Operational\ManifestDetail;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Operational\DeliveryOrderOngoingJob;
use App\Abstracts\Operational\Manifest;
use App\Abstracts\Contact\ContactLocation;
use App\Http\Controllers\Api\v5\DeliveryOrderController;
use App\Model\Contact;
use App\Model\DeliveryOrderDriver as ModelDeliveryOrderDriver;
use App\Model\DepoContainer;
use App\Model\Dock;
use App\Model\JobOrder;
use App\Model\Vehicle;
use Illuminate\Support\Facades\DB;
use PDO;

class DeliveryOrderDriver
{
    protected static $table = 'delivery_order_drivers';

    public static function query($params = []) {
        $wr="1=1";

        $request = self::fetchFilter($params);

        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr.=" AND manifests.company_id = ".auth()->user()->company_id;
        }
        if ($request['status']) {
            $wr.=" and delivery_order_drivers.job_status_id = ".$request['status'];
        }
        if ($request['not_status']) {
            $wr.=" and delivery_order_drivers.job_status_id not in (".$request['not_status'].")";
        }
        if ($request['delivery_order_status']) {
            $wr.=" and delivery_order_drivers.status = ".$request['delivery_order_status'];
        }

        if ($request['vehicle_id']) {
            $wr.=" and delivery_order_drivers.vehicle_id = " . $request['vehicle_id'];
        }

        $item = DB::table(self::$table)
        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', 'delivery_order_drivers.id')
        ->leftJoin('manifests','manifests.id','delivery_manifests.manifest_id')
        ->leftJoin('companies','companies.id','manifests.company_id');
        if($request['customer_id']){
            $query = DB::table('manifest_details')
                    ->join('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
                    ->join('job_orders', 'job_orders.id', 'job_order_details.header_id')
                    ->where('job_orders.customer_id', $request['customer_id'])
                    ->select('manifest_details.header_id as id')
                    ->groupBy('manifest_details.header_id')
                    ->get();
            // dd($query->pluck('id')->toArray());
            $item = $item->whereIn('manifests.id', $query->pluck('id')->toArray());
        }
        $item = $item->leftJoin('routes','routes.id','manifests.route_id')
        ->leftJoin('job_statuses','job_statuses.id','delivery_order_drivers.job_status_id')
        ->leftJoin('contacts as driver','driver.id','delivery_order_drivers.driver_id')
        ->leftJoin('vehicles','vehicles.id','delivery_order_drivers.vehicle_id')
        ->whereRaw($wr);

        if ($request['start_date']) {
            $start = Carbon::parse($request['start_date'])->format('Y-m-d');
            $item = $item->where(DB::raw('DATE_FORMAT(' . self::$table . '.pick_date, "%Y-%m-%d")'), '>=', $start);
        }

        if ($request['end_date']) {
            $end = Carbon::parse($request['end_date'])->format('Y-m-d');
            $item = $item->where(DB::raw('DATE_FORMAT(' . self::$table . '.pick_date, "%Y-%m-%d")'), '<=', $end);
        }

        if($request['job_status_slug']) {
            $item = $item->where('job_statuses.slug', $request['job_status_slug']);
        }

        if($request['not_in_job_status_slug']){
          $item = $item->whereNotIn('job_statuses.slug', $request['not_in_job_status_slug']);
        }

        if($request['is_active'] == 1){
            $item = $item->where(self::$table.'.status', '<', 3);
        }

        if($request['job_order_id']) {
            $manifests = Manifest::query([
                'job_order_id' => $request['job_order_id']
            ]);
            $manifests = $manifests->pluck('manifests.id');
            $item = $item->whereIn('delivery_manifests.manifest_id', $manifests);
        }

        if($request['driver_id']) {
            $item = $item->where(self::$table . '.driver_id', $request['driver_id']);
        }

        if($request['vendor_id']){
            $item = $item->where(self::$table.'.vendor_id', $request['vendor_id']);
        }

        if($request['route_id']){
            $item = $item->where('routes.id', $request['route_id']);
        }

        if($request['service_type_id']){
            $query = DB::table('manifest_details')
                    ->join('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
                    ->join('job_orders', 'job_orders.id', 'job_order_details.header_id')
                    ->where('job_orders.service_type_id', $request['service_type_id'])
                    ->select('manifest_details.header_id as id')
                    ->groupBy('manifest_details.header_id')
                    ->get();
            $item = $item->whereIn('manifests.id', $query->pluck('id')->toArray());
        }

        return $item;
    }

    public static function fetchFilter($args = []) {
        $params = [];
        $params['status'] = $args['status'] ?? null;
        $params['not_status'] = $args['not_status'] ?? null;
        $params['is_active'] = $args['is_active'] ?? null;
        $params['job_status_slug'] = $args['job_status_slug'] ?? null;
        $params['not_in_job_status_slug'] = $args['not_in_job_status_slug'] ?? null;
        $params['delivery_order_status'] = $args['delivery_order_status'] ?? null;
        $params['vehicle_id'] = $args['vehicle_id'] ?? null;
        $params['start_date'] = $args['start_date'] ?? null;
        $params['end_date'] = $args['end_date'] ?? null;
        $params['driver_id'] = $args['driver_id'] ?? null;
        $params['job_order_id'] = $args['job_order_id'] ?? null;
        $params['customer_id'] = $args['customer_id'] ?? null;
        $params['vendor_id'] = $args['vendor_id'] ?? null;
        $params['route_id'] = $args['route_id'] ?? null;
        $params['service_type_id'] = $args['service_type_id'] ?? null;

        return $params;
    }

    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id);

        $dt = $dt->first();

        if(!$dt) {
            throw new Exception('Delivery order not found');
        }
    }

    public static function validateJo($joId) {
        $dt = DB::table('job_orders')
        ->whereId($joId);

        $dt = $dt->first();

        if(!$dt) {
            throw new Exception('Job Order not found');
        }
    }

    /*
      Date : 19-02-2021
      Description : Menampilkan detail surat jalan driver berdasarkan manifest
      Developer : Didin
      Status : Create
    */
    public static function showByManifest($manifest_id) {
        $dt = DB::table(self::$table)
        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', self::$table.'.id')
        ->where('delivery_manifests.manifest_id',$manifest_id)
        ->where(self::$table.'.status', '<', 3);

        $dt = $dt->first();

        return $dt;
    }

    public static function getIdByManifest($manifest_id) {
        $r = null;

        $dt = self::showByManifest($manifest_id);
        if($dt) {
            $r = $dt->delivery_order_driver_id;
        }

        return $r;
    }

    /*
      Date : 19-02-2021
      Description : Menampilkan detail surat jalan driver
      Developer : Didin
      Status : Create
    */
    public static function show($id, $jobOrderId = null) {
        self::validate($id);
        $job_statuses = JobStatus::query(['job_order_id' => $jobOrderId]);
        // dd($job_statuses->get());
        $dt = DB::table(self::$table);
        $dt = $dt->leftJoin('vehicles', 'vehicles.id', self::$table . '.vehicle_id');
        $dt = $dt->leftJoin('contacts AS drivers', 'drivers.id', self::$table . '.driver_id');
        $dt = $dt->leftJoin('delivery_manifests', self::$table . '.id', 'delivery_manifests.delivery_order_driver_id');
        $dt = $dt->leftJoin('manifests', 'manifests.id', 'delivery_manifests.manifest_id');
        $dt = $dt->leftJoin('manifest_details', 'manifests.id', 'manifest_details.header_id');
        /*
            Date : 15-09-2022 By : Muhammad Firyanul Rizky
            Description : [Improve] Surat Jalan Driver
            Keterangan : Menambahkan join relasi tabel ke job_orders, services, contacts, containers, dan vessels untuk menampilkan datanya ke view delivery-orders-show
        */
        $dt = $dt->leftJoin('job_order_details', 'manifest_details.job_order_detail_id', 'job_order_details.id');
        $dt = $dt->leftJoin('job_orders', 'job_order_details.header_id', 'job_orders.id');
        $dt = $dt->leftJoin('service_types', 'job_orders.service_type_id', 'service_types.id');
        $dt = $dt->leftJoin('services', 'job_orders.service_id', 'services.id');
        $dt = $dt->leftJoin('contacts','contacts.id','job_orders.customer_id');
        $dt = $dt->leftJoin('companies', 'companies.id', 'manifests.company_id');
        $dt = $dt->leftJoin('containers','containers.id','manifests.container_id');
        $dt = $dt->leftJoin('vessels','vessels.id','containers.vessel_id');
        // Close join relasi tabel job_orders, services, dan contacts
        $dt = $dt->leftJoin('vehicle_types', 'vehicle_types.id', 'manifests.vehicle_type_id');
        $dt = $dt->leftJoin('container_types', 'container_types.id', 'manifests.container_type_id');
        $dt = $dt->leftJoin('routes', 'routes.id', 'manifests.route_id');
        $dt = $dt->leftJoinSub($job_statuses, 'job_statuses', function ($query){
            $query->on(self::$table . '.job_status_id', 'job_statuses.id');
        });
        $dt = $dt->where(self::$table . '.id', $id);

        $dt = $dt->select(
            self::$table . '.id',
            self::$table . '.code',
            self::$table . '.pick_date',
            self::$table . '.created_at',
            self::$table . '.driver_id',
            self::$table . '.status AS dod_status',
            DB::raw(self::$table . '.journey_distance / 1000 AS journey_distance'),
            'manifests.code AS manifest_code',
            'manifests.id AS manifest_id',
            'manifests.depart AS tanggal_berangkat',
            'manifests.arrive AS tanggal_sampai',
            'companies.name AS company_name',
            'manifests.no_sj_customer AS contact_name',
            'job_orders.code AS jo_code',
            'job_orders.no_bl AS no_bl',
            'job_orders.code_invoice AS code_invoice',
            'job_orders.no_po_customer AS no_po_customer',
            'container_types.name AS tipe_kontainer',
            'container_types.code AS code_container',
            'vessels.name AS nama_kapal',
            'containers.container_no AS container_no',
            'containers.stripping AS tanggal_bongkar',
            'containers.stuffing AS tanggal_muat',
            'service_types.id AS service_type_id',
            'service_types.name AS service_type_name',
            'services.name AS service_name',
            'contacts.name AS customer_name',
            'routes.name AS route_name',
            'drivers.name AS driver_name',
            'vehicles.id as vehicle_id',
            'vehicles.nopol',
            'vehicle_types.name as vehicle_type_name',
            'container_types.name as container_type_name',
            'job_statuses.id AS status',
            'job_statuses.slug AS status_slug',
            'job_statuses.urut AS status_urut',
            'job_statuses.urut_full AS status_urut_full',
            'job_statuses.is_start',
            'job_statuses.is_finish',
            'job_statuses.is_repeat',
            'job_statuses.name AS status_name',
            'job_statuses.next_status_id AS next_status',
            'job_statuses.next_status_is_repeat',
            'job_statuses.next_status_name AS next_status_name',
            'job_statuses.next_status_slug AS next_status_slug',
            'job_statuses.next_status_urut AS next_status_urut',
            'job_statuses.next_status_urut_full AS next_status_urut_full',
            'routes.name AS route',
            'routes.distance AS route_distance'
        );
        // Close Description : [Improve] Surat Jalan Driver

        $dt = $dt->first();

        return $dt;
    }

    public static function showDetail($id, $joId, $type = null)
    {
        self::validate($id);
        self::validateJo($joId);

        $job_statuses = JobStatus::query(['is_job_order' => 1, 'job_order_id' => $joId, 'type' => $type]);
        // dd($id, $joId,$type,$job_statuses->get());
        $dt = DB::table(self::$table);
        $dt = $dt->leftJoin('delivery_manifests', self::$table . '.id', 'delivery_manifests.delivery_order_driver_id');
        $dt = $dt->leftJoin('manifests', 'manifests.id', 'delivery_manifests.manifest_id');
        $dt = $dt->leftJoin('manifest_details', 'manifests.id', 'manifest_details.header_id');
        $dt = $dt->leftJoin('job_order_details', 'manifest_details.job_order_detail_id', 'job_order_details.id');
        $dt = $dt->leftJoin('job_orders', 'job_order_details.header_id', 'job_orders.id');
        $dt = $dt->leftJoin('service_types', 'job_orders.service_type_id', 'service_types.id');
        $dt = $dt->leftJoinSub($job_statuses, 'job_statuses', function ($query){
            $query->on('manifest_details.job_status_id', 'job_statuses.id');
        });
        $dt = $dt->where(self::$table . '.id', $id);
        $dt = $dt->where('job_order_details.header_id', $joId);

        // $deliveryOrder = ManifestDetail::query([
        //     'delivery_order_id' => $id,
        //     'job_order_id' => $joId
        // ]);

        // $deliveryOrder = $deliveryOrder->select(
        //     "manifest_details.id AS manifest_detail_id",
        //     "job_orders.id AS job_order_id",
        //     "job_orders.code AS code",
        //     "manifests.id AS manifest_id",
        //     "job_order_details.item_name AS commodity_name",
        //     'customers.name AS receiver_name',
        //     DB::raw('true AS is_approved'),
        //     'manifest_details.requested_qty as requested_qty',
        //     'manifest_details.discharged_qty as discharged_qty',
        //     'manifest_details.transported as transported_qty',
        // )->toSql();

        $dt = $dt->select(
            self::$table . '.id',
            self::$table . '.code',
            self::$table . '.pick_date',
            self::$table . '.driver_id',
            'manifests.code AS manifest_code',
            'manifest_details.id AS manifest_detail_id',
            'manifest_details.job_status_id AS manifest_detail_job_status_id',
            'job_orders.code AS job_order_code',
            'service_types.id AS service_type_id',
            'service_types.name AS service_type_name',
            'manifests.id AS manifest_id',
            'job_order_details.header_id AS job_order_id',
            'job_statuses.id AS status',
            'job_statuses.slug AS status_slug',
            'job_statuses.is_start',
            'job_statuses.is_finish',
            'job_statuses.is_repeat',
            'job_statuses.next_status_id AS next_status',
            'job_statuses.next_status_is_repeat',
            'job_statuses.name AS status_name',
            'job_statuses.type AS status_type',
            'job_statuses.next_status_name AS next_status_name',
            'job_statuses.next_status_type AS next_status_type',
            'job_statuses.next_status_slug AS next_status_slug',
            // DB::raw('IF()')
        );

        $dt = $dt->groupBy('job_order_details.header_id')->first();
        // dd($dt);

        $deliveryOrder = ManifestDetail::query([
            'delivery_order_id' => $id,
            'job_order_id' => $joId
        ]);

        $deliveryOrder = $deliveryOrder->select(
            "manifest_details.id AS manifest_detail_id",
            "job_orders.id AS job_order_id",
            "job_orders.code AS code",
            "manifests.id AS manifest_id",
            "job_order_details.item_name AS commodity_name",
            'customers.name AS receiver_name',
            DB::raw('true AS is_approved'),
            'manifest_details.requested_qty as requested_qty',
            'manifest_details.discharged_qty as discharged_qty',
            'manifest_details.transported as transported_qty',
        )->get()->toArray();

        if($dt){
            $dt->items = $deliveryOrder;
        }

        return $dt;
    }

    public static function destroy($id) {
        self::validate($id);
        DB::beginTransaction();

        DB::table(self::$table)
        ->whereId($id)
        ->delete();

        DB::commit();
    }

    /*
      Date : 19-02-2021
      Description : Update ke status selanjutnya
      Developer : Didin
      Status : Create
      Updated : Ferdi
    */
    public static function updateToNextStatus($id, $created_by, $forceFinish = false, $jobOrderId = null) {
        if($forceFinish){
            $dt = self::show($id, $jobOrderId);
        } else {
            $dt = self::show($id);
        }
        if($dt->is_finish) {
            if($forceFinish){
                return ['status' => 'is_finished', 'data' => $dt];
            } else {
                throw new Exception('Shipment was finished');
            }
        }
        if($forceFinish){
            if($dt->next_status){
                $params = [];
                $params['created_by'] = $created_by;
                $params['job_status_id'] = $dt->next_status;
                $params['delivery_order_driver_id'] = $id;
                DeliveryOrderStatusLog::store($params);
    
                $detail = DeliveryOrderController::indexManifest($id, false);
                // dd($detail[0]->manifest_id);
                if($detail[0]->manifest_id ?? null){
                    DB::table('manifest_details')
                        ->where('header_id', $detail[0]->manifest_id)
                        ->update([
                            'job_status_id' => $dt->next_status
                        ]);
                }
    
                DB::table(self::$table)->whereId($id)->update([
                    'job_status_id' => $dt->next_status,
                    'delivery_order_status_id' => 3 // finish
                ]);
            }

            return self::updateToNextStatus($id, $created_by, true);
        } else {
            if($dt->next_status) {
                $params = [];
                $params['created_by'] = $created_by;
                $params['job_status_id'] = $dt->next_status;
                $params['delivery_order_driver_id'] = $id;
                DeliveryOrderStatusLog::store($params);

                // jika status next_status_is_repeat == 0, maka semua detail ikut terupdate
                if($dt->next_status_is_repeat == 0){
                    $detail = DeliveryOrderController::indexManifest($id, false);
                    // dd($detail[0]->manifest_id);
                    if($detail[0]->manifest_id ?? null){
                        DB::table('manifest_details')
                            ->where('header_id', $detail[0]->manifest_id)
                            ->update([
                                'job_status_id' => $dt->next_status
                            ]);
                    }
                } // jika sedang next_status_is_repeat == 1, maka check apakah setiap detail memiliki status yg sama dengan status DO saat ini?
                else if($dt->is_repeat == 1 && $dt->next_status_is_repeat == 1) {
                    $detail = DeliveryOrderController::indexManifest($id, false);
                    // dd($detail, $dt);
                    if($detail[0]->items ?? null){
                        $items = collect($detail[0]->items)->where('manifest_detail_job_status_urut_full', '<', $dt->next_status_urut_full);
                        // dd($items, $dt, $detail[0]->items);
                        if($items->count() > 0){
                            throw new Exception('Every customer job\'s status must be equal to or after \''. $dt->next_status_name .'\'');
                        }
                    }
                } // jika next_status adalah is_repeat == 1, maka cek dahulu apakah ada JO yg memiliki status type 'optional'
                else if($dt->is_repeat == 0 && $dt->next_status_is_repeat == 1) {
                    $detail = DeliveryOrderController::indexManifest($id, false);
                    // dd($detail);
                    throw new Exception('Please update status by detail location');
                    if($detail[0]->items ?? null){
                        $items = collect($detail[0]->items)->unique('job_order_id');
                        // dd($items);
                        foreach($items as $item){
                            self::updateToNextStatusPerJo($id, $created_by, $item->job_order_id, false,);
                        }
                    }
                }

                DB::table(self::$table)->whereId($id)->update([
                    'job_status_id' => $dt->next_status,
                    'delivery_order_status_id' => 2
                ]);

                return [
                    'name' => $dt->next_status_name,
                    'slug' => $dt->next_status_slug,
                ];
            } else {
                return [
                    'name' => null,
                    'slug' => null,
                    'message' => "No Next Status Found. Please update by Job Order!"
                ];
            }
        }
    }

    public static function fetchNextStatusDetail($args =[])
    {
        $params = [];
        $params['type'] = $args['type'] ?? null;
        $params['location_id'] = $args['location_id'] ?? null;
        $params['location_type'] = $args['location_type'] ?? null;

        return $params;
    }

    /**
     * Description : Update to next status based on Job Order
     * Developer : Hendra
     */
    public static function updateToNextStatusPerJo($id, $created_by, $jobOrderId, $autoLog = true, $params = [])
    {
        $params = self::fetchNextStatusDetail($params);
        // dd('pass', $params);
        $dt = self::showDetail($id, $jobOrderId, $params['type']);
        // if($jobOrderId == 29){
            // dd($dt, $params);
        // }
        // $listJo = self::detailLocation($id);
        $locParams = $params;

        if($dt->is_finish) {
            throw new Exception('Shipment for '.$dt->job_order_code.' was finished', 500);
        }
        if($dt->next_status) {
            $params = [];
            $params['created_by'] = $created_by;
            $params['job_status_id'] = $dt->next_status;
            $params['job_order_id'] = $jobOrderId;
            $params['delivery_order_driver_id'] = $id;
            $params['description'] = $dt->next_status_name .' - ' .$dt->job_order_code;
            DeliveryOrderStatusLog::store($params);

            foreach($dt->items as $item){
                DB::table('manifest_details')->where('id',$item->manifest_detail_id)->update([
                    'job_status_id' => $dt->next_status
                ]);

                // update delivery_manifest_status_log
                DB::table('delivery_manifest_status_logs')
                ->where([
                    'delivery_order_driver_id' => $id,
                    'manifest_detail_id' => $item->manifest_detail_id,
                    'job_status_id' => $dt->next_status,
                ])->update([
                    'is_done' => 1
                ]);
                DB::table('delivery_manifest_status_logs')
                ->where([
                    'delivery_order_driver_id' => $id,
                    'manifest_detail_id' => $item->manifest_detail_id,
                    'job_status_id' => $dt->next_status,
                ])->get();
                if(isset($locParams['location_id']) && $locParams['location_id'] && isset($locParams['location_type']) && $locParams['location_type']){
                    $whereParam = [
                        'delivery_order_driver_id' => $id,
                    ];
                    if($locParams['location_type'] == 'contact'){
                        $whereParam['contact_id'] = $locParams['location_id'];
                    } else if($locParams['location_type'] == 'depo') {
                        $whereParam['depo_id'] = $locParams['location_id'];
                    }else if($locParams['location_type'] == 'dock'){
                        $whereParam['dock_id'] = $locParams['location_id'];
                    }
                    DB::table('delivery_order_routes')->where($whereParam)->update(['is_finished' => 1]);
                }
            }
            // dd('pass');

            self::updateMinimumDeliveryStatus($id, $dt->manifest_id, $created_by, $autoLog);

            return [
                'name' => $dt->next_status_name,
                'slug' => $dt->next_status_slug,
            ];
        }

        return [
            'name' => null,
            'slug' => null,
        ];
    }

    /**
     * Description : Update bulk status by location given
     * Developer : Hendra
     *
     */
    public static function updateStatusByLocation($id, $locationType, $locationId, $type)
    {
        // get list job yg ada di lokasi ini
        self::validate($id);

        $dt = DeliveryOrderRoute::showJo($id, $locationType, $locationId);
        // dd($dt);
        $status = [];
        foreach($dt[0]->items as $jo){
            // dd($jo);
            $status[] = self::updateToNextStatusPerJo($id, auth()->user()->id, $jo->job_order_id, true, $type);
        }
        return $status;
    }

    public static function updateMinimumDeliveryStatus($id, $manifestId, $createdBy, $autoLog = true, $fromVendorChannel = false)
    {
        $allDetail = DB::table('manifest_details')
                        ->join('job_statuses', 'job_statuses.id', 'manifest_details.job_status_id')
                        ->where('header_id', $manifestId)
                        ->get();
        $minStatus = $allDetail->pluck('urut_full')->min();

        $do = DB::table(self::$table)
                    ->join('job_statuses', 'job_statuses.id', self::$table.'.job_status_id')
                    ->where(self::$table.'.id', $id)->select('urut_full')->first();

        // Auto Update DO job_status when all job_status details have passed the DO job_status
        if(!empty($do->urut_full) && $do->urut_full < $minStatus && !($fromVendorChannel)){
            $doMin = $allDetail->where('urut_full', $minStatus)->first();
            DB::table(self::$table)->whereId($id)->update([
                'job_status_id' => $doMin->job_status_id
            ]);

            if($autoLog){
                $params = [];
                $params['created_by'] = $createdBy;
                $params['job_status_id'] = $doMin->job_status_id;
                $params['delivery_order_driver_id'] = $id;
                $params['description'] = $doMin->name .' (COMPLETE)';
                DeliveryOrderStatusLog::store($params);
            }
        } else if(!empty($do->urut_full) && $do->urut_full > $minStatus && $fromVendorChannel){
            $doMin = $allDetail->where('urut_full', $minStatus)->first();
            DB::table(self::$table)->whereId($id)->update([
                'job_status_id' => $doMin->job_status_id
            ]);

            if($autoLog){
                $params = [];
                $params['created_by_contact'] = $createdBy;
                $params['job_status_id'] = $doMin->job_status_id;
                $params['delivery_order_driver_id'] = $id;
                $params['description'] = $doMin->name;
                DeliveryOrderStatusLog::store($params);
            }
        }
    }

    /*
      Date : 21-07-2021
      Description : Menampilkan jumlah surat jalan hari inii
      Developer : Didin
      Status : Create
    */
    public static function amountToday($driver_id = null, $vehicle_id = null) {
        $params = [];
        $params['start_date'] = Carbon::now()->format('Y-m-d');
        $params['end_date'] = Carbon::now()->format('Y-m-d');
        if($driver_id) {
            $params['driver_id'] = $driver_id;
        }
        if($vehicle_id) {
            $params['vehicle_id'] = $vehicle_id;
        }

        $dt = self::query($params);
        $r = $dt->count(self::$table . '.id');
        return $r;
    }

    /*
      Date : 21-07-2021
      Description : Menampilkan jumlah surat jalan hari inii
      Developer : Didin
      Status : Create
    */
    public static function amountThisMonth($driver_id = null, $vehicle_id = null) {
        $params = [];
        $params['start_date'] = Carbon::now()->startOfMonth()->format('Y-m-d');
        $params['end_date'] = Carbon::now()->endOfMonth()->format('Y-m-d');
        if($driver_id) {
            $params['driver_id'] = $driver_id;
        }
        if($vehicle_id) {
            $params['vehicle_id'] = $vehicle_id;
        }

        $dt = self::query($params);
        $r = $dt->count(self::$table . '.id');
        return $r;
    }

    /*
      Date : 21-07-2021
      Description : Menampilkan summary pengiriman yang sudah selesai
      Developer : Didin
      Status : Create
    */
    public static function indexShipmentSummary($driver_id = null, $vehicle_id = null) {
        $params = [];
        $params['start_date'] = Carbon::now()->startOfYear()->format('Y-m-d');
        $params['end_date'] = Carbon::now()->endOfYear()->format('Y-m-d');
        if($driver_id) {
            $params['driver_id'] = $driver_id;
        }
        if($vehicle_id) {
            $params['vehicle_id'] = $vehicle_id;
        }
        $params['job_status_slug'] = 'jobFinished';

        $deliveryOrder = self::query($params);
        $deliveryOrder = $deliveryOrder->select(
            self::$table . '.id',
            DB::raw("DATE_FORMAT(" . self::$table . ".pick_date, '%m') AS month_sequence"),
            DB::raw("DATE_FORMAT(" . self::$table . ".pick_date, '%Y') AS year")
        );
        $shipments = DB::query()->fromSub($deliveryOrder, 'shipments');
        $shipments = $shipments->groupBy('month_sequence', 'year');
        $shipments = $shipments->select(
            "month_sequence",
            "year",
            DB::raw('COUNT(id) AS qty_shipment')
        );

        $months = DB::table('months');
        $months = $months->select(
            DB::raw('name AS `months`'),
            DB::raw('months.sequence AS month_sequence'),
            DB::raw('DATE_FORMAT(NOW(), "%Y") AS `this_year`'),
        );
        $dt = DB::query()->fromSub($months, 'months');
        $dt = $dt->leftJoinSub($shipments, 'shipments', function ($query){
            $query->on('shipments.month_sequence', 'months.month_sequence');
            $query->on('shipments.year', 'months.this_year');
        });

        $dt = $dt->select(
            DB::raw('0 AS point'),
            'months.months',
            DB::raw('COALESCE(shipments.qty_shipment, 0) AS qty_shipment'),
            DB::raw('DATE_FORMAT(NOW(), CONCAT("%Y-", months.month_sequence, "-01")) AS actual_date')
        );
        $dt = $dt->orderBy('months.month_sequence', 'ASC');
        $dt = $dt->get();

        return $dt;
    }

    public static function showOrigin($id) {
        self::validate($id);
        $dt = ManifestDetail::query(['delivery_order_id' => $id]);
        $dt = $dt->leftJoin('warehouse_receipt_details', 'warehouse_receipt_details.id', 'job_order_details.warehouse_receipt_detail_id');
        $dt = $dt->leftJoin('warehouse_receipts', 'warehouse_receipts.id', 'warehouse_receipt_details.header_id');
        $dt = $dt->leftJoin('warehouses', 'warehouses.id', 'warehouse_receipts.warehouse_id');

        $dt = $dt->selectRaw(
            'IFNULL(warehouses.name, senders.name) as name,
            IFNULL(warehouses.address, senders.address) as address,
            IFNULL(warehouses.latitude, senders.latitude) as latitude,
            IFNULL(warehouses.longitude, senders.longitude) as longitude'
        );

        $dt = $dt->first();
        if(!$dt) {
            $dt = [
                'name' => null,
                'address' => null,
                'latitude' => null,
                'longitude' => null
            ];
            $dt = json_decode(json_encode($dt));
        }

        return $dt;
    }

    public static function showReceiver($id) {
        self::validate($id);
        $dt = DeliveryOrderOngoingJob::query([
            'delivery_order_driver_id' => $id
        ]);

        $dt = $dt->selectRaw(
            'IFNULL(receivers.name, customers.name) as name,
            IFNULL(receivers.address, customers.address) as address,
            IFNULL(receivers.latitude, customers.latitude) as latitude,
            IFNULL(receivers.longitude, customers.longitude) as longitude'
        );

        $dt = $dt->first();

        // Jika belum ada, maka ambil dari Receivers
        if(!$dt){
            $dt = ManifestDetail::query(['delivery_order_id' => $id]);
            $dt = $dt->leftJoin('warehouse_receipt_details', 'warehouse_receipt_details.id', 'job_order_details.warehouse_receipt_detail_id');
            $dt = $dt->leftJoin('warehouse_receipts', 'warehouse_receipts.id', 'warehouse_receipt_details.header_id');
            $dt = $dt->leftJoin('warehouses', 'warehouses.id', 'warehouse_receipts.warehouse_id');

            $dt = $dt->selectRaw(
                'IFNULL(warehouses.name, receivers.name) as name,
                IFNULL(warehouses.address, receivers.address) as address,
                IFNULL(warehouses.latitude, receivers.latitude) as latitude,
                IFNULL(warehouses.longitude, receivers.longitude) as longitude'
            );

            $dt = $dt->first();
        }
        if(!$dt) {
            $dt = [
                'name' => null,
                'address' => null,
                'latitude' => null,
                'longitude' => null
            ];
            $dt = json_decode(json_encode($dt));
        }

        return $dt;
    }

    public static function setJourneyDistance($driver_id) {
        $start_status_urut = 0;
        $end_status_urut = 0;

        $start_status = JobStatus::showBySlug('startedByDriver');
        if($start_status) {
            $start_status_urut = $start_status->urut;
        }

        $end_status = JobStatus::showBySlug('jobFinished');
        if($end_status) {
            $end_status_urut = $end_status->urut;
        }

        $dt = DB::table(self::$table);
        $dt = $dt->join('delivery_order_status_logs', 'delivery_order_status_logs.delivery_order_driver_id', self::$table . '.id');
        $dt = $dt->join('job_statuses', 'job_statuses.id', 'delivery_order_status_logs.job_status_id');
        $dt = $dt->where(self::$table . '.driver_id', $driver_id);
        $dt = $dt->where('job_statuses.urut', '>', $start_status_urut);
        $dt = $dt->where('job_statuses.urut', '<', $end_status_urut);
        $dt = $dt->select(self::$table . '.id');
        $dt = $dt->get();
        $dt->each(function($v) use($driver_id){
            $start_data = DeliveryOrderStatusLog::getFirstIndex($v->id);
            $end_data = DeliveryOrderStatusLog::getLastIndex($v->id);
            $end_time = $end_data->created_at;
            $end_status = JobStatus::show($end_data->job_status_id);
            if(
                $end_status->slug !=  'jobFinished' &&
                $end_status->slug !=  'aborted' &&
                $end_status->slug !=  'rejected'
            ) {
                $end_time = Carbon::now('Asia/Jakarta')->format('Y-m-d H:i:s');
            }

            $journey_distance = ContactLocation::getDistance(
                $start_data->created_at,
                $end_time,
                $driver_id
            );
            DB::table(self::$table)
            ->whereId($v->id)
            ->update([
                'journey_distance' => $journey_distance
            ]);
        });
    }

    public static function getDeliveryRoute($id)
    {
        $dod = ModelDeliveryOrderDriver::find($id);
        if(!$dod){
            throw new Exception('Data tidak ditemukan!');
        }
        $dor = DeliveryOrderRoute::index($id, 1);
        if($dor->isNotEmpty()){
            $departure = null;
            if($dod->from_id){
                $departure = Contact::find($dod->from_id);
            }
            if($departure){
                $dor->prepend((object)[
                    'id' => null,
                    'job_order_detail_id' => null,
                    'type' => 'departure',
                    'location_id' => null,
                    'location_name' => $departure->name . ' (Asal Keberangkatan)',
                    'location_type' => 'departure',
                    'latitude' => $departure->latitude,
                    'longitude' => $departure->longitude,
                    'priority' => 0,
                    'is_finished' => 1
                ]);
            }
        }
        // dd($dor, 'dor');

        return $dor;
    }

    public static function getRoutePlan($id, $optimize = 0, $joId = null)
    {
        $dod = ModelDeliveryOrderDriver::find($id);
        if(!$dod){
            throw new Exception('Data tidak ditemukan!');
        }
        $md = DB::table('delivery_order_drivers as dod')
          ->join('delivery_manifests as dm', 'dm.delivery_order_driver_id', 'dod.id')
          ->join('manifests as m', 'm.id', 'dm.manifest_id')
          ->join('manifest_details as md', 'md.header_id', 'm.id')
          ->join('job_order_details as jod', 'jod.id', 'md.job_order_detail_id')
          ->join('job_orders as jo', 'jo.id', 'jod.header_id')
          ->where('md.transported', '>', 0)
          ->where('dod.id', $id);

        $md = $md->select('jo.id as jo_id',
                    'm.id as manifest_id',
                    'jod.id as jo_detail_id',
                    'jo.code as jo_code',
                    'm.code as manifest_code',
                    'jo.company_id',
                    'jo.customer_id',
                    'jo.sender_id',
                    'jo.receiver_id',
                    'jo.depo_awal_id',
                    'jo.depo_tujuan_id',
                    'jo.dock_awal_id',
                    'jo.dock_tujuan_id',
                    'jo.start_window_time',
                    'jo.end_window_time',
                    )
          ->get();
        //   dd($md);
        $data = [];
        foreach($md as $dt){
            $new['jo_id'] = $dt->jo_id;
            $new['jo_detail_id'] = $dt->jo_detail_id;
            $new['jo_code'] = $dt->jo_code;
            $new['start_window_time'] = $dt->start_window_time;
            $new['end_window_time'] = $dt->end_window_time;
            $new['locations'] = [];
            if($dt->depo_awal_id){
                $loc = DepoContainer::select('id','name','latitude', 'longitude', 'service_time')->find($dt->depo_awal_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => $loc->service_time,
                    'type' => 'depo_awal_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }
            if($dt->dock_awal_id){
                $loc = Dock::select('id','name','latitude', 'longitude', 'service_time')->find($dt->dock_awal_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => $loc->service_time,
                    'type' => 'dock_awal_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }
            if($dt->sender_id){
                $loc = Contact::select('id','name','latitude', 'longitude')->find($dt->sender_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => 0,
                    'type' => 'sender_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }
            if($dt->receiver_id){
                $loc = Contact::select('id','name','latitude', 'longitude')->find($dt->receiver_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => 0,
                    'type' => 'receiver_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }
            if($dt->depo_tujuan_id){
                $loc = DepoContainer::select('id','name','latitude', 'longitude', 'service_time')->find($dt->depo_tujuan_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => $loc->service_time,
                    'type' => 'depo_tujuan_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }
            if($dt->dock_tujuan_id){
                $loc = Dock::select('id','name','latitude', 'longitude', 'service_time')->find($dt->dock_tujuan_id);
                $new['locations'][] = [
                    'latitude' => $loc->latitude ?? null,
                    'longitude' => $loc->longitude ?? null,
                    'service_time' => $loc->service_time,
                    'type' => 'dock_tujuan_id',
                    'name' => $loc->name,
                    'id' => $loc->id,
                    'idloc' => $loc->id . '-' .$loc->name
                ];
            }

            if(count($new['locations']) > 0){
                foreach($new['locations'] as $newLoc){
                    if($newLoc['latitude'] == 0 || $newLoc['longitude'] == 0 || $newLoc['latitude'] == null || $newLoc['longitude'] == null){
                        throw new Exception('Latitude/longitude ' . $newLoc['name'] . ' tidak valid!');
                    }
                }
            }

            $data[] = $new;
            $dt->locations = $new['locations'];
        };

        $fleet = Vehicle::select('vehicles.id','vehicles.last_latitude', 'vehicles.last_longitude', 'vehicles.last_update_latlng', 'vehicle_types.type')
            ->join('vehicle_variants','vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->join('vehicle_types','vehicle_types.id', 'vehicle_variants.vehicle_type_id')
            ->where('vehicle_types.type', 1)
            // ->where('delivery_id')
            // ->where('vehicles.last_latitude', '<>', 0)
            // ->where('vehicles.last_longitude', '<>', 0)
            ->whereNotNull('vehicles.last_latitude')
            ->whereNotNull('vehicles.last_longitude')
            ->where('vehicles.id', $dod->vehicle_id)
            ->first();
        // dd($fleet, $dod);

        // dd($md);
        if($optimize ==1){
            $data = [];
            $t = self::template_route_planning($md, $fleet ? [$fleet] : [], $dod->from_id);
            if($t){
                $dt = $t->getData();
                $statusCode = $t->status();
                // dd($dt, $statusCode);
                if($statusCode != 200){
                    throw new Exception('Tidak dapat menemukan routing. Pastikan koordinat tiap titik dan kendaraan terisi dengan baik!');
                }
                $tours = [];
                if($dt->tours){
                    if(isset($dt->tours[0]->stops)){
                        $stops = $dt->tours[0]->stops;
                        $tours = [];
                        foreach($stops as $st){
                            $tours[] = [
                                'latitude' => $st->location->lat,
                                'longitude' => $st->location->lng,
                                'activities' => $st->activities
                            ];
                        }
                    }
                }
                $data['tours'] = $tours;
                $data['unassigned'] = $dt->unassigned ?? [];
            }
            return $data;
        } else {
            return $data;
        }

    }

    // public static function forceFinish($id){

    // }

    public static function template_route_planning($jobs = [], $fleets = [], $fromId = null)
    {
        if(!$fleets){
            throw new Exception('Kendaraan tidak ditemukan/belum diatur!');
        }
        //   dd($fleets);

        // berisi jobs disertai titik korrdinat masing2 pickup/delivery
        $templateJobs = [];

        // berisi list fleet/kendaraan
        $templateFleets = [];

        if($jobs){
            foreach($jobs as $j){
                $pData = collect($j->locations)->whereIn('type', ['depo_awal_id', 'dock_awal_id', 'sender_id']);
                $dData = collect($j->locations)->whereIn('type', ['depo_tujuan_id', 'dock_tujuan_id', 'receiver_id']);
                // dd($pData, $dData);
                $pickups = [];
                $deliveries = [];

                foreach($pData as $idx => $pck){
                    $pckData = [];
                    $pckData = [
                        'demand' => [$dData->count()],
                        'location' => [
                            'lat' => (float) $pck['latitude'],
                            'lng' => (float) $pck['longitude'],
                        ],
                        'duration' => $pck['service_time'],
                        'tag' => $pck['type']
                    ];
                    if(!empty($pck['start_window_time']) && !empty($pck['end_window_time'])){
                        $pckData['times'] = [[ Carbon::parse($pck['start_window_time'])->format("Y-m-d\TH:m:s\Z"), Carbon::parse($pck['end_window_time'])->format("Y-m-d\TH:m:s\Z") ]];
                    }
                    $pickups[] = $pckData;
                }

                foreach($dData as $dlv){
                    $dlvData = [];
                    $dlvData = [
                        'demand' => [$pData->count()],
                        'location' => [
                            'lat' => (float) $dlv['latitude'],
                            'lng' => (float) $dlv['longitude'],
                        ],
                        'duration' => $dlv['service_time'],
                        'tag' => $dlv['type']
                    ];
                    if(!empty($dlv['start_window_time']) && !empty($dlv['end_window_time'])){
                        $dlvData['times'] = [[ Carbon::parse($dlv['start_window_time'])->format("Y-m-d\TH:m:s\Z"), Carbon::parse($dlv['end_window_time'])->format("Y-m-d\TH:m:s\Z") ]];
                    }
                    $deliveries[] = $dlvData;
                }

                // replace / menjadi '' pada JO Code
                $j->jo_code = str_replace('/', '', $j->jo_code);

                $templateJobs[] = [
                    'id' => $j->jo_code . '-' . $j->jo_detail_id,
                    'places' => [
                        'pickups' => $pickups,
                        'deliveries' => $deliveries
                    ]
                ];
            }
        }

        // dd($templateJobs);

        if($fleets){
            foreach($fleets as $f){
                $lat = $f->last_latitude;
                $lng = $f->last_longitude;
                if($fromId){
                    $c = Contact::find($fromId);
                    if($c && $c->latitude != 0 && $c->longitude != 0){
                        $lat = $c->latitude;
                        $lng = $c->longitude;
                    }
                }
                if(!$lat || !$lng){
                    throw new Exception('Tidak ditemukan Latitude/Longitude asala keberangkatan/kendaraan!');
                }
                // Prevent error
                if($f->last_update_latlng == '0000-00-00 00:00:00' || $f->last_update_latlng <= Carbon::parse('1970-01-01 00:00:00')){
                    $f->last_update_latlng = Carbon::now();
                }
                $templateFleets[] = [
                    'id' => $f->id . '-VEHICLEID',
                        'profile'=> 'normal_truck',
                        'costs' => [
                            "distance"=> 0.0000, // optional
                            "time"=> 0.000, // optional
                            "fixed"=> 0 // optional
                        ],
                        "shifts"=> [
                            [
                                "start"=> [
                                    "time"=> Carbon::parse($f->last_update_latlng)->format("Y-m-d\TH:m:s\Z"), // diisi waktu saat ini / waktu shipment
                                    "location"=> [// diisi lokasi titik awal kirim (?)
                                        "lat"=>(float) $lat,
                                        "lng"=>(float) $lng
                                    ]
                                ]
                            ]
                        ],
                        'limits' => [
                        "maxDistance"=> 3000000, // sudah max
                        // "shiftTime"=> 86400 // sudah max
                    ],
                    "capacity"=> [
                        ($f->max_tonase && $f->max_tonase != 0) ? $f->max_tonase : (($f->max_volume && $f->max_volume != 0) ? $f->max_volume : 999)
                    ],
                    "amount"=> 1
                ];
            }
        }
        // dd($templateFleets);

        if(count($templateJobs) < 1 && count($templateFleets) < 1){
            throw new Exception('Data routing tidak lengkap!');
        }

        $body = [
            "plan"=> [
                "jobs"=>
                $templateJobs
            ],
            "fleet"=> [
                "types"=>
                $templateFleets,
                "profiles"=> [
                    [
                        "name"=> "normal_truck",
                        "type"=> "truck",
                    ]
                ]
            ]
        ];
        $client = new \GuzzleHttp\Client();
        $apiKey = env('hereapi_key');
        $endpoint = 'https://tourplanning.hereapi.com/v2/problems?apiKey=' . $apiKey;

        $response = $client->request('POST', $endpoint, [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body'=> json_encode($body)
        ]);

        $content = json_decode($response->getBody(), true);

        return response()->json($content);
    }

    public static function listLocation($id)
    {
        self::validate($id);

        $dor = DeliveryOrderRoute::index($id);

        return $dor;
    }

    public static function detailLocation($id, $type, $locationId)
    {
        self::validate($id);

        $dt = DeliveryOrderRoute::showJo($id, $type, $locationId);

        return $dt;
    }

    public static function forceFinish($id)
    {
        $do = ModelDeliveryOrderDriver::find($id);
        $status = [];
  
        $jo = DB::table('delivery_order_drivers')
        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', 'delivery_order_drivers.id')
        ->leftJoin('manifests', 'manifests.id','delivery_manifests.manifest_id')
        ->leftJoin('manifest_details', 'manifest_details.header_id','manifests.id')
        ->leftJoin('job_order_details', 'job_order_details.id','manifest_details.job_order_detail_id')
        ->leftJoin('job_orders', 'job_orders.id','job_order_details.header_id')
        ->where('delivery_order_drivers.id', $id)
        ->select('job_orders.service_type_id')
        ->first();
  
  
        if($jo){
          if(in_array($jo->service_type_id, [1,2,3,4])){
            $status = [
                0 => 2,     //Assigned To Driver
                1 => 3,     //Terima Job
                2 => 4,     //Pengambilan
                3 => 5,     //Tiba dilokasi pengambilan
                4 => 7,     //Berangkat
                5 => 8,     //Tiba dilokasi
                6 => 11,    //Selesai
                7 => 0,     //Status tidak bisa terupdate
            ];
          }
          elseif(in_array($jo->service_type_id, [20,21,24])){
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 15,    //Menuju Depo/Dock Awal
                  3 => 16,    //Tiba di Depo/Dock Awal
                  4 => 18,    //Menuju Depo/Dock Tujuan
                  5 => 20,   //Tiba di Depo/Dock Tujuan
                  6 => 11,    //Selesai
                  7 => 0,    //Status tidak bisa terupdate
              ];
          }
          elseif(in_array($jo->service_type_id, [22,27])){
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 15,    //Menuju Depo/Dock Awal
                  3 => 16,    //Tiba di Depo/Dock Awal
                  4 => 7,     //Berangkat
                  5 => 8,     //Tiba dilokasi
                  6 => 11,    //Selesai
                  7 => 0,    //Status tidak bisa terupdate
              ];
          }
          elseif(in_array($jo->service_type_id, [23])){
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 15,    //Menuju Depo/Dock Awal
                  3 => 16,    //Tiba di Depo/Dock Awal
                  4 => 7,     //Berangkat
                  5 => 8,     //Tiba dilokasi
                  6 => 18,    //Menuju Depo/Dock Tujuan
                  7 => 20,   //Tiba di Depo/Dock Tujuan
                  8 => 11,    //Selesai
                  9 => 0,     //Status tidak bisa terupdate
              ];
          }
          elseif(in_array($jo->service_type_id, [28])){
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 15,    //Menuju Depo/Dock Awal
                  3 => 16,    //Tiba di Depo/Dock Awal
                  4 => 4,     //Pengambilan
                  5 => 5,     //Tiba dilokasi pengambilan
                  6 => 18,    //Menuju Depo/Dock Tujuan
                  7 => 20,    //Tiba di Depo/Dock Tujuan
                  8 => 11,    //Selesai
                  9 => 0,     //Status tidak bisa terupdate
              ];
          }
          elseif(in_array($jo->service_type_id, [25,26])){
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 4,     //Pengambilan
                  3 => 5,     //Tiba dilokasi pengambilan
                  4 => 18,    //Menuju Depo/Dock Tujuan
                  5 => 20,    //Tiba di Depo/Dock Tujuan
                  6 => 11,    //Selesai
                  7 => 0,     //Status tidak bisa terupdate
              ];
          }else{
              $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 3,     //Terima Job
                  2 => 4,     //Pengambilan
                  3 => 5,     //Tiba dilokasi pengambilan
                  4 => 7,     //Berangkat
                  5 => 8,     //Tiba dilokasi
                  6 => 11,    //Selesai
                  7 => 0,     //Status tidak bisa terupdate
              ];
          }
        }
  
        DB::beginTransaction();

        
        if( $do->job_status_id !== "14"){
          // Melakukan update status do log sampai selesai
          $status_no = array_search($do->job_status_id, $status);
          $next_status = $status_no + 1;
          if(count($status) > 0){
            while($status[$next_status] !== 0) {
              $params = [];
              $params['created_by'] = auth()->id();
              $params['job_status_id'] = $status[$next_status];
              $params['delivery_order_driver_id'] = $id;
              DeliveryOrderStatusLog::store($params);
              $next_status = $next_status + 1;
            }
          }
              
          $params = [];
          $params['created_by'] = auth()->id();
          $params['job_status_id'] = 14; //Done and Checked
          $params['delivery_order_driver_id'] = $id;
          DeliveryOrderStatusLog::store($params);

        }
        $do->update([
          'job_status_id' => 14, //Done and Checked
          'delivery_order_status_id' => 3 // finish
        ]);

        $delivery_manifest = DB::table('delivery_manifests')->where('delivery_order_driver_id', $id)->select('manifest_id')->get();

        if(count($delivery_manifest) > 0){
          foreach($delivery_manifest as $manifest){
            Manifest::forceFinish($manifest->manifest_id);
          }
        }
  
        DB::commit();
    }
}
