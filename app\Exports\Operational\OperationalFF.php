<?php
namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OperationalFF implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        return $report_controller->get_data($this->request);
    }
    public function headings(): array
    {
        return [
            'No WO',
            'Tanggal',
            'Customer',
            'No AJU',
            'No BL',
            '<PERSON>anan',
            'Qty',
            'Satuan',
            'Biaya Operasional'
        ];
    }
    public function map($data): array
    {
        return [
            $data->code ?? '-',
            $data->date ?? '-',
            $data->customer_name ?? '-',
            $data->aju_number ?? '-',
            $data->no_bl ?? '-',
            $data->service_name ?? '-',
            $data->qty ?? '-',
            $data->imposition_name ?? '-',
            $data->cost ?? '-'
        ];
    }
}
