<?php
namespace App\Http\Controllers\Api\Finance;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Contact as AbstractsContact;
use App\Model\CashCategory;
use App\Model\Company;
use App\Model\Contact;
use App\Model\Debt;
use App\Model\DebtDetail;
use App\Model\DebtPayment;
use App\Model\InvoiceVendor;
use App\Model\Payable;
use App\Model\PayableDetail;
use App\Model\Account;
use App\Model\AccountDefault;
use App\Model\CekGiro;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Model\CashTransaction;
use App\Model\CashTransactionDetail;
use App\Model\{Invoice, Receivable, ReceivableDetail, UmCustomer, UmSupplier};
use App\Utils\TransactionCode;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Response;
use Barryvdh\DomPDF\Facade as PDF;
class DebtPayableController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
      $data['account']=Account::with('parent','type')->where('is_base',0)->orderBy('code')->get();
      $data['cash_category']=CashCategory::with('category')->where('is_base', 0)->get();
      $data['company']=companyAdmin(auth()->id());
      $data['supplier']=AbstractsContact::showVendor();
      $data['taxes']=DB::table('taxes')->selectRaw('id,name,pemotong_pemungut,npwp,non_npwp,is_ppn,is_pph')->get();
      $data['vendor'] = Contact::whereRaw("is_vendor = 1 and vendor_status_approve = 2 or is_supplier = 1")->select('id','name','company_id','address')->get();;
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    /*
      Date : 06-03-2020
      Description : Menyimpan pembayaran hutang
      Developer : Didin
      Status : Edit
    */
    public function store(Request $request)
    {
      $request->validate([
        'company_id' => 'required',
        'supplier_id' => 'required',
        'date_transaction' => 'required',
        'total' => 'required',
        'overpayment' => 'required',
      ]);
      DB::beginTransaction();
      $leftover = 0;
      $journal_id= NULL;
      foreach ($request->detail as $key => $value) {
          $leftover += $value['leftover'];
      }
      // if($leftover != 0) {
      //     if(!$request->filled('akun_selisih')) {
      //         return Response::json(['message' => 'Akun selisih tidak boleh kosong'], 421);
      //     }
      // }
      $code = new TransactionCode($request->company_id, 'debtPayable');
      $code->setCode();
      $trx_code = $code->getCode();
      $b=Debt::create([
        'company_id' => $request->company_id,
        'create_by' => auth()->id(),
        'date_request' => dateDB($request->date_transaction),
        'akun_selisih' => $request->akun_selisih,
        'total' => $request->total,
        'description' => $request->description,
        'code' => $trx_code,
      ]);
      $setting_penjualan= null;
      $account_default = DB::table('account_defaults')->first();
      foreach ($request->detail as $key => $value) {
        if(empty($value)) {
            continue;
        }
        $r=Payable::find($value['payable_id']);
        //check type transaksi good receipt
        $type_transaksi= DB::table('type_transactions')->where('id',$r->type_transaction_id)->first();
        $check_setting= false;
        if($type_transaksi->slug == 'receipt'){
          $check_setting= true;
          $slug_setting='good_receipt';
          $slug_setting_detail="pengakuan_jatuh_tempo";
        }
        else if($type_transaksi->slug == 'invoiceVendorOpr'){
          $check_setting= true;
          $slug_setting='general';
          $slug_setting_detail="pengakuan_hutang_perawatan";
        }
        //check penerimaan hutang po/pr or invoice vendor
        if($check_setting == true){
        $setting = new \App\Http\Controllers\Setting\SettingController();
        $check= $setting->show_data($slug_setting);
        foreach($check->getData() as $c){
            foreach($c->content->settings as $s){
                if($s->slug == $slug_setting_detail){
                    $setting_penjualan=$s->value;
                }
            }
        }
        }
        if($setting_penjualan != null && ($setting_penjualan == "VENDOR_BILL" || $setting_penjualan == "PENAGIHAN_VENDOR_BILL" )){
          //jurnal vendor bill invoice //ga jalan pas save recanana bayar, jalan hanya pada storepayment
          $journal_id= $this->create_jurnal_penagihan_vendor($value['payable_id'],$trx_code);
        }
        $invoiceVendor = InvoiceVendor::wherePayableId($r->id ?? null);
        if($invoiceVendor->first() != null) {
          $invoiceVendor->update([
              'status_approve' =>  4
          ]);
        }
        // $rd=PayableDetail::create([ 
        //   'header_id' => $r->id,
        //   'type_transaction_id' => 29, // debt payable
        //   'code' => $trx_code,
        //   'date_transaction' => dateDB($request->date_transaction),
        //   'relation_id' => $b->id,
        //   'debet' => $value['debt'],
        //   'is_journal' => 0
        // ]); // error create payable double
        DebtDetail::create([
          'header_id' => $b->id,
          'type_transaction_id' => $r->type_transaction_id,
          'code' => $r->code,
          'payable_id' => $value['payable_id'],
          // 'payable_detail_id' => $r->id,//error
          'create_by' => auth()->id(),
          'debt' => $value['debt'],
          'leftover' => $value['leftover'],
          'total_debt' => $value['total'],
          'description' => $value['description']??'-',
        ]);
        $payable = DB::table('payables')
        ->join('manifest_costs', 'manifest_costs.id', 'payables.relation_id')
        ->join('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
        ->where('payables.id', $value['payable_id'])
        ->where('payables.type_transaction_id',54)
        ->first() ?? DB::table('payables')
        ->join('job_order_costs', 'job_order_costs.id', 'payables.relation_id')
        ->join('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
        ->where('payables.id', $value['payable_id'])
        ->where('payables.type_transaction_id',50)
        ->first();
        // dd($payable);
        // die();
        $contact= DB::table('contacts')->where('id',$r->contact_id)->first();
        if($payable != null) {
            $akun_uang_muka = $payable->akun_uang_muka;
            $akun_kas_hutang = $payable->akun_kas_hutang;
        } else {
            if($contact->akun_hutang != null){
              $akun_kas_hutang = $contact->akun_hutang;
            }
            else{
              $akun_kas_hutang = $account_default->hutang;
            }
            if(!$akun_kas_hutang) {
                throw new Exception('Akun hutang belum di set');
            }
        }
        $detail_payable= PayableDetail::where('header_id',$r->id)->get();
        // foreach($detail_payable as $d){
        //   // dd($d);
        //   $po= DB::table('warehouse_receipts')->where('code',$d->code)->first();
        //   if($payable == null){
        //     $po_detail= DB::table('purchase_order_details')->where('id',$d->relation_id)->first();
        //     if($po_detail != null){
        //       $item= DB::table('items')->where('id',$po_detail->item_id)->first();
        //       $akun_uang_muka= $item->account_id;
        //     }
        //     $akun_uang_muka = $account_default->persediaan;
        //     if(!$akun_uang_muka) {
        //         throw new Exception('Akun persediaan belum di set');
        //     }
        //   }
        //   $search= JournalDetail::where('header_id',$journal_id)->where('account_id',$akun_uang_muka)->where('credit',0)->first();
        //   // dd($po_detail->price*$po_detail->qty);
        //   // if($search != null){
        //   //   $update= JournalDetail::find($search->id);
        //   //   $update->debet= $update->debet+($po_detail->price*$po_detail->qty);
        //   //   $update->save();
        //   // }
        //   // else{
        //   //   JournalDetail::create([
        //   //     'header_id' => $journal_id,
        //   //     'account_id' => $akun_uang_muka,
        //   //     'debet' => $po_detail->price*$po_detail->qty,
        //   //     'credit' => 0,
        //   //     'description' => $value['description'] ?? ''
        //   //   ]);
        //   // }
        //   // JournalDetail::create([
        //   //   'header_id' => $journal_id,
        //   //   'account_id' => $akun_uang_muka,
        //   //   'debet' => $po_detail->price*$po_detail->qty,
        //   //   'credit' => 0,
        //   //   'description' => $value['description'] ?? ''
        //   // ]);
        // }
        if($setting_penjualan != null){
          if($setting_penjualan == "PENAGIHAN_VENDOR_BILL"){
            $akun_uang_muka = $account_default->hutang_belum_tertagih;
            if(!$akun_uang_muka) {
              throw new Exception('Akun hutang sementara belum di set');
            }
          }
          else{
            $akun_uang_muka = $account_default->persediaan;
            if(!$akun_uang_muka) {
              throw new Exception('Akun persediaan belum di set');
            }
          }
        }
        if($journal_id != NULL){
          JournalDetail::create([
            'header_id' => $journal_id,
            'account_id' => $akun_uang_muka,
            'debet' => $value['debt'],
            'credit' => 0,
            'description' => $value['description'] ?? ''
          ]);
          JournalDetail::create([
            'header_id' => $journal_id,
            'account_id' => $akun_kas_hutang,
            'debet' => 0,
            'credit' => $value['debt'],
            'description' => $value['description'] ?? ''
          ]);
        // if($leftover < 0) {
        //     if(!$request->filled('akun_selisih')) {
        //         return Response::json(['message' => 'Akun selisih tidak boleh kosong'], 421);
        //     }
        //     JournalDetail::create([
        //     'header_id' => $journal_id,
        //     'account_id' => $request->akun_selisih,
        //     'debet' => -$leftover,
        //     'credit' => 0,
        //     'description' => $request->description ?? ''
        //   ]);
        // } else if($leftover > 0) {
        //     if(!$request->filled('akun_selisih')) {
        //         return Response::json(['message' => 'Akun selisih tidak boleh kosong'], 421);
        //     }
        //     JournalDetail::create([
        //     'header_id' => $journal_id,
        //     'account_id' => $request->akun_selisih,
        //     'debet' => 0,
        //     'credit' => $leftover,
        //     'description' => $request->description ?? ''
        //   ]);
        // }
        }
      }
      DB::commit();
      return Response::json(null);
    }
   /*
      Date : 02-03-2020
      Description : Menampilkan detail pembayaran hutang
      Developer : Didin
      Status : Create
    */
    public function show($id)
    {
      $data['item']=Debt::with('company')
      ->leftJoin('accounts', 'accounts.id', 'debts.akun_selisih')
      ->where('debts.id', $id)
      ->select('debts.*', 'accounts.name AS akun_selisih_name')
      ->first();
      $data['detail']=DebtDetail::with('type_transaction:id,name','payable.contact:id,name,address')
      ->where('header_id', $id)
      ->get();
      $data['payment']=DebtPayment::with('cash_account','cek_giro')->where('header_id', $id)->whereNull('reff')->get();
      $data['paymentbp']=DebtPayment::with('cash_account','cek_giro')->where('header_id', $id)->whereNotNull('reff')->get();
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
      $data['account']=Account::with('parent','type')->where('is_base',0)->orderBy('code')->get();
      $data['company']=companyAdmin(auth()->id());
      $data['item']=Debt::with('company')->where('id', $id)->first();
      $data['detail']=DebtDetail::with('type_transaction','payable.contact')->where('header_id', $id)->get();
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
      $request->validate([
        'company_id' => 'required',
        'supplier_id' => 'required',
        'date_transaction' => 'required',
        'total' => 'required',
        'overpayment' => 'required',
      ]);
      DB::beginTransaction();
      $b=Debt::find($id);
      $b->update([
        'company_id' => $request->company_id,
        'date_request' => dateDB($request->date_transaction),
        'total' => $request->total,
        'description' => $request->description
      ]);
      // delete
      $this->deleteDebtDetails($b->id);
      if (empty($request->detail)) {
        return Response::json(['message' => 'tidak ada tagihan yang di pilih.'], 422);
      }
      foreach ($request->detail as $key => $value) {
        if (empty($value['payable_id'])) {
          continue;
        }
        $r=Payable::find($value['payable_id']);
        $rd=PayableDetail::create([
          'header_id' => $r->id,
          'type_transaction_id' => 29, // debt payable
          'code' => $b->code,
          'date_transaction' => dateDB($request->date_transaction),
          'relation_id' => $b->id,
          'debet' => $value['debt'],
          'is_journal' => 0
        ]);
        DebtDetail::create([
          'header_id' => $b->id,
          'type_transaction_id' => $r->type_transaction_id,
          'code' => $r->code,
          'payable_id' => $value['payable_id'],
          'payable_detail_id' => $rd->id,
          'create_by' => auth()->id(),
          'debt' => $value['debt'],
          'leftover' => $value['leftover'],
          'total_debt' => $value['total'],
          'description' => $value['description']??'-',
        ]);
      }
      DB::commit();
      return Response::json(null);
    }
    public function destroyDebtDetailById($id)
    {
      try {
        //delete tagihan by id
        $debtDetail = DebtDetail::find($id);
        $payableDetail = $debtDetail->payableDetail;
        // reset value debet in payable
        $payable = $debtDetail->payable;
        $payable->debet = $payable->debet - $payableDetail->debet;
        $payable->save();
        $debtDetail->delete();
        $payableDetail->delete();
      } catch (\Exception $e) {
        return Response::json([], 500);
      }
      return Response::json([], 204);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
     public function destroy($id)
     {
       DB::beginTransaction();
       try {
         Debt::find($id)->update([
          'status' => 6
         ]);
         $this->deleteDebtDetails($id);
       } catch (\Exception $e) {
         DB::rollback();
         return Response::json([$e->getMessage()], 500);
       }
       DB::commit();
       return Response::json([], 204);
     }
    private function deleteDebtDetails($id)
    {
      $debt = Debt::find($id);
      $debtDetails = $debt->debtDetails;
      if ($debt->status != 6) {
        foreach ($debtDetails as $debtDetail) {
          $debtDetail->delete();
        }
      }
    }
    public function cari_supplier_list($id) {
        $data = Contact::whereRaw("is_supplier = 1 or is_vendor = 1 and vendor_status_approve = 2")
            ->select('id','name','address')
            ->get();
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function payment($id)
    {
      $data['item']=Debt::with('company')->where('id', $id)->first();
      $data['detail']=DebtDetail::with(['type_transaction', 'payable.contact'])->where('header_id', $id)->get();
      $data['account']=Account::with('parent')->where('is_base',0)->select('id','code','name','no_cash_bank')->get();
      //by andre 7 agustus 2019
      $data['accountall']=DB::table('accounts')->where('is_base',0)->select('id','deep','code','name','no_cash_bank')->get();
      // $data['accountall']=Account::with('parent')->select('id','code','name','no_cash_bank')->get();
      //end by andre
      $data['cek_giro']=CekGiro::where('is_used',0)->where('jenis', 2)->where('penerima_id', $data['detail'][0]->payable->contact_id)->get();
      $data['payable']=Payable::leftJoin('contacts','contacts.id','=','payables.contact_id')->whereRaw('(credit-debet) > 0')->where('payables.contact_id', $data['detail'][0]->payable->contact_id)->select('payables.id','payables.code', 'payables.contact_id', 'contacts.name',DB::raw('credit-debet as total'))->get();
      // $data['uang_muka']=UmSupplier::leftJoin('contacts','contacts.id','um_suppliers.contact_id')
      // ->leftJoin('um_supplier_paids', 'um_supplier_paids.header_id', 'um_suppliers.id')
      // ->where('contact_id', $data['payable'][0]->contact_id)
      // ->select('um_suppliers.id', 'um_suppliers.code', 'contacts.name', DB::raw('debet-credit as total'))->whereRaw('(debet-credit) > 0')
      // ->groupBy
      // ->get();
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function validasiBP(Request $request, $id)
    {
      $bp=[];
      $nilaibp=[];
      $todaydate = date('Y-m-d');
      // foreach ($request->paymentbp as $key => $value) {
      //   if (empty($value)) {
      //     continue;
      //   }
      //   if ($value['bp_cash_account_id']??null) {
      //     $bp[]=$value['bp_cash_account_id'];
      //     $nilaibp[]=$value['totalbp'];
      //   }
      // }
      // foreach ($bp as $key => $value) {
      //     JournalDetail::create([
      //     'header_id' => $j->id,
      //     'account_id' => $value,
      //     // 'cash_category_id' => $cid,
      //     'debet' => 0,
      //     'credit' => $nilaibp[$key],
      //   ]);
      //   }
      $cekDefault=AccountDefault::first();
      if (empty($cekDefault->bukti_potong_hutang)) {
        return Response::json(['message' => 'Akun Default Hutang Belum Ditentukan!'],500);
      }
      $idutkbill=DB::table('debt_payments')
            ->select('header_id')
            ->where('id', $request->id)
            ->get();
      foreach ($idutkbill as $target) {
               $tabelbill=DB::table('debts')
                ->select('company_id')
                ->where('id', $target->header_id)
                ->get();
                 foreach ($tabelbill as $targetdlm) {
                  $code = new TransactionCode($targetdlm->company_id, 'billReceivablePayment');
                  $code->setCode();
                  $trx_code = $code->getCode();
                  $j=Journal::create([
                    'company_id' => $targetdlm->company_id,
                    'type_transaction_id' => 31,
                    'date_transaction' => $todaydate,
                    'created_by' => auth()->id(),
                    'code' => $trx_code,
                    'description' => 'Pembayaran Tagihan Hutang - ',
                    'debet' => 0,
                    'credit' => 0,
                  ]);
                  JournalDetail::create([
                    'header_id' => $j->id,
                    'account_id' => $cekDefault->bukti_potong,
                    // 'cash_category_id' => $cid,
                    'debet' => 0,
                    'credit' => $request->total,
                  ]);
                  JournalDetail::create([
                  'header_id' => $j->id,
                  'account_id' => $request->cash_account_id,
                  // 'cash_category_id' => $cid,
                  'debet' => $request->total,
                  'credit' => 0,
                ]);
                 };
            };
      // JournalDetail::create([
      //     'header_id' => $request->journal_id,
      //     'account_id' => $request->cash_account_id,
      //     // 'cash_category_id' => $cid,
      //     'debet' => 0,
      //     'credit' => $request->total,
      //   ]);
      DB::table('debt_payments')
            ->where('id', $request->id)
            ->update(['valid' => 1]);
       foreach ($idutkbill as $target) {
               DB::table('debts')
                ->where('id', $target->header_id)
                ->update(['status' => 3]);
            };
    }
    public function uploadBP(Request $request,$id){
    $request->validate([
        'file' => 'required|mimetypes:image/jpeg,image/png,application/pdf',
      ],[
        'file.mimetypes' => 'File Harus Berupa Gambar atau PDF!',
        'file.required' => 'File belum ada!'
      ]);
    $file=$request->file('file');
    $file_name="BP_".$id."_".date('Ymd_His').'.'.$file->getClientOriginalExtension();
    DB::table('debt_payments')
            ->where('id', $id)
            ->update(['filename' => $file_name]);
    $file->move(public_path('files'),$file_name);
  }
    public function store_payment(Request $request, $id){
      // dd($request->all());
      DB::beginTransaction();
      $debt=Debt::find($id);
      $code = new TransactionCode($debt->company_id, 'debtPayablePayment');
      $code->setCode();
      $trx_code = $code->getCode();
      $totalPaymentBp=$request->total_paymentbp;
      $cash_account_krg=$request->cash_account_id_krg;
      $statusbill=2;
      Debt::find($id)->update([
        'status' => 2,
        'code_receive' => $trx_code,
        'date_receive' => dateDB($request->date_receive),
      ]);
      //hitung Kas/Bank-Cek/giroPayable::
      $giro=[];
      $kas=[];
      $bp=[];
      $nilaibp=[];
      $bpA=0;
      $totalPayment=$request->total_payment;
      $nilaiKas=[];
      $giroA=0;
      $kasA=0;
      foreach ($request->detail as $key => $value) {// tagihannya dari table debt_details
        if (empty($value)) {
          continue;
        }
        //jika ada payable id
        if ($value['payable_detail_id']??null) {
          if ($value['debt'] < $totalPayment) {
            $byr=$value['debt'];
            $totalPayment-=$value['debt'];
          } else {
            $byr = $totalPayment;
          }
          PayableDetail::find($value['payable_detail_id'])->update([
          'debet' => $byr
          ]);
        } else {
          if ($value['payable_id']??null) {
            //simpan di tabel piutang detail
            if ($value['debt'] < $totalPayment) {
              $byr=$value['debt'];
              $totalPayment-=$value['debt'];
            } else {
              $byr = $totalPayment;
            }
            $rds=PayableDetail::create([
              'header_id' => $value['payable_id'],
              'type_transaction_id' => 29, //debt payable
              'code' => $trx_code,
              'date_transaction' => dateDB($request->date_receive),
              'relation_id' => $id,
              'debet' => $byr,
              'is_journal' => 0
            ]);
          }
        }
        //jika ada id detail
        if ($value['id']??false) {
          DebtDetail::where('id', $value['id']??0)->update([
            'debt' => $value['debt'], 
            'description' => $value['description']??null,
          ]);
        } else {
          DebtDetail::create([
            'header_id' => $id,
            'type_transaction_id' => 29,
            'code' => $trx_code,
            'payable_id' => $value['payable_id']??null,
            'payable_detail_id' => $rds->id??null,
            'um_supplier_id' => $value['um_supplier_id']??null,
            'create_by' => auth()->id(),
            'debt' => $value['debt'],
            'leftover' => $request->total_tagih-$request->total_payment,//$value['leftover'],
            'total_debt' => $value['debt'],
            'description' => @$value['description'],
            'jenis' => $value['jenis']??1,
            // 'account_id' => $value['account_id']??null,// account id kolom tidak ditemukan
          ]);
        }
        //jika ada cndn
        // if ($value['account_id']??null) {
        //   if (in_array($value['no_cash_bank'],[1,2])) {
        //     $i=CashTransaction::create([
        //       'company_id' => $debt->company_id,
        //       'type_transaction_id' => 31, // pembayaran tagihan piutang
        //       // 'code' => $trx_code,
        //       // 'journal_id' => $j->id,
        //       'reff' => $debt->code,
        //       'jenis' => $value['jenis'],
        //       'type' => $value['no_cash_bank'],
        //       'description' => 'Pembayaran Tagihan Piutang - '.$debt->code,
        //       'total' => $value['debt'],
        //       'account_id' => $value['account_id'],
        //       'date_transaction' => dateDB($request->date_receive),
        //       'status_cost' => 3,
        //       'created_by' => auth()->id()
        //     ]);
        //
        //     CashTransactionDetail::create([
        //       'header_id' => $i->id,
        //       'account_id' => $value['cash_account_id'],
        //       'contact_id' => $debt->customer_id,
        //       'amount' => $value['total'],
        //       'description' => @$value['description'],
        //       'jenis' => 1
        //     ]);
        //
        //   }
        // }
        $r=Payable::find($value['payable_id']); //paksa update debet = jumlah dibayar
        $r->debet=$r->getTotalBayarAttribute();
        $r->save();

        // Update Invoice Vendor IF have relation with type_transaction 101
        if ($r->type_transaction_id == 101){
          if($r->debet == $r->credit){
            InvoiceVendor::find($r->relation_id)->update([
              'status_approve' => 4,
              'status' => 2
            ]);
          } else {
            InvoiceVendor::find($r->relation_id)->update([
              'status_approve' => 4,
            ]);
          }
        }
      }
      if (empty($request->payment_detail)) {
        return Response::json(['message' => 'cara bayar belum diisi'], 422);
      }
      foreach ($request->payment_detail as $key => $value) {
        // dd();
        if ($value['cek_giro_id']??null) {
          $giro[]=$value['cek_giro_id'];
          $giroA+=$value['total'];
        }
        if ($value['cash_account_id']??null) {
          $kas[]=$value['cash_account_id'];
          $nilaiKas[]=$value['total'];
          $kasA+=$value['total'];
        }
      }
      foreach ($request->paymentbp_detail as $key => $value) {
        if (empty($value)) {
          continue;
        }
        if ($value['bp_cash_account_id']??null) {
          $bp[]=$value['bp_cash_account_id'];
          $nilaibp[]=$value['totalbp'];
          $bpA+=$value['totalbp'];
        }
      }
      //get code transaksi
      $codeTrx = [];
      $get_code_transaksi = DebtDetail::where('header_id', $id)->get();
      foreach ($get_code_transaksi as $key => $value) {
        $codeTrx[] = $value->code;
      }
      if(count($codeTrx) > 1){ 
        $get_code_string = join(", ",$codeTrx);
      } else {
        $get_code_string = $codeTrx[0];
      }

      // $get_code_transaksi = DebtDetail::where('header_id', $request->detail[0]['id'])->first();
      //menjurnal dahulu
      $j=Journal::create([
        'company_id' => $debt->company_id,
        'type_transaction_id' => 31,
        'date_transaction' => dateDB($request->date_receive),
        'created_by' => auth()->id(),
        'code' => $trx_code,
        // 28. [Bug] Perubahan kalimat type transaksi pada Jurnal jika Payable adalah Pembayaran Tagihan Hutang
        'description' => 'Pembayaran Tagihan Hutang - '.$get_code_string,
        // end 28. [Bug] Perubahan kalimat type transaksi pada Jurnal jika Payable adalah Pembayaran Tagihan Hutang
        'debet' => 0,
        'credit' => 0,
      ]);
      //jurnal detail kas
      foreach ($kas as $key => $value) {
        $cekCC=cekCashCount($debt->company_id,$value);
        if ($cekCC) {
          return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $value, // ?
          // 'cash_category_id' => $cid,
          'debet' => 0,
          'credit' => $nilaiKas[$key]??0,
        ]);
      }
      //jurnal hutang
      $cekDefault=AccountDefault::first();
      
      $ddetail=DebtDetail::where('header_id',$debt->id)->first();
      $debt->journal_id=$j->id;
      $debt->save();
      $payables=Payable::find($ddetail->payable_id);
      $contact=$payables->contact_id;
      $supplier = Contact::find($contact);
      $akunHutang = $supplier->akun_hutang;
      if(!$supplier->akun_hutang) {
        $akunHutang = $cekDefault->hutang;
        if (!$cekDefault->hutang) {
          return Response::json(['message' => 'tidak ada akun hutang yang di setting pada default akun'],500);
        }
      }
      // if (empty($cekDefault->hutang)) {
      //   return Response::json(['message' => 'Akun Default Hutang Belum Ditentukan!'],500);
      // }
      JournalDetail::create([
        'header_id' => $j->id,
        'account_id' => $akunHutang,
        // 'cash_category_id' => $cid,
        'debet' => $request->total_tagih,
        'credit' => 0,
      ]);
      //jurnal detail giro / jika ada
      if (count($giro)>0) {
        if (empty($cekDefault->cek_giro_masuk)) {
          return Response::json(['message' => 'Akun Default Penjualan Belum Ditentukan!'],500);
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $cekDefault->cek_giro_masuk,
          // 'cash_category_id' => $cid,
          'debet' => 0,
          'credit' => $giroA,
        ]);
      }
      //by andre 7 agustus 2019
      //jurnal bukti potong / jika ada
      if ($totalPaymentBp>0) {
        // if (empty($cekDefault->cek_giro_masuk)) {
        //   return Response::json(['message' => 'Akun Default Penjualan Belum Ditentukan!'],500);
        // }
        foreach ($bp as $key => $value) {
          JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $value,
          // 'cash_category_id' => $cid,
          'debet' => 0,
          'credit' => $nilaibp[$key],
        ]);
        }
      }
      //end by andre
      //versi sebelumnya cuma ada lebih bayar, comment by andre 7 agustus 2019
      // //jika ada lebih bayar
      // if ($request->leftover_payment>0) {
      //   if (empty($cekDefault->lebih_bayar_hutang)) {
      //     return Response::json(['message' => 'Akun Default Lebih Bayar Hutang Belum Ditentukan!'],500);
      //   }
      //   JournalDetail::create([
      //     'header_id' => $j->id,
      //     'account_id' => $cekDefault->lebih_bayar_hutang,
      //     // 'cash_category_id' => $cid,
      //     'debet' => $request->leftover_payment,
      //     'credit' => 0,
      //   ]);
      // }
      // end by andre
      //jika ada lebih bayar(versi baru,made by andre 7 agustus 2019)
      $testkrg=$request->total_tagih-$request->total_payment;
      if ($testkrg>0) {
        if (empty($cekDefault->lebih_bayar_hutang)) {
          return Response::json(['message' => 'Akun Default Lebih Bayar Hutang Belum Ditentukan!'],500);
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $cash_account_krg,
          'debet' => 0,
          'credit' => $request->leftover_payment,
        ]);
      }
      if ($testkrg<0) {
        if (empty($cekDefault->lebih_bayar_piutang)) {
          return Response::json(['message' => 'Akun Default Lebih Bayar Piutang Belum Ditentukan!'],500);
        }
        JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $cash_account_krg,
          'debet' => $request->leftover_payment,
          'credit' => 0,
        ]);
        //send to umsupplier ga sido
      }
      //end by andre
      //simpan debt payment
      foreach ($request->payment_detail as $key => $value) {
        if(is_null($value))
            continue;
        DebtPayment::create([
          'header_id' => $id,
          'journal_id' => $j->id,
          'create_by' => auth()->id(),
          'payment_type' => $value['payment_type'],
          'total' => $value['total'],
          'description' => $value['description']??null,
          'cash_account_id' => $value['cash_account_id']??null,
          'cek_giro_id' => $value['cek_giro_id']??null,
        ]);
        if ($value['cash_account_id']??null) {
          //jika pakai kas simpan di transaksi kas
          $acc=Account::find($value['cash_account_id']);
          $i=CashTransaction::create([
            'company_id' => $debt->company_id,
            'type_transaction_id' => 31, // pembayaran tagihan piutang
            'code' => $trx_code,
            'journal_id' => $j->id,
            'reff' => $debt->code,
            'jenis' => 2,
            'type' => $acc->no_cash_bank,
            'description' => 'Pembayaran Tagihan Hutang - '.$debt->code,
            'total' => $value['total'],
            'account_id' => $acc->id,
            'date_transaction' => dateDB($request->date_receive),
            'status_cost' => 3,
            'created_by' => auth()->id()
          ]);
          $acd=AccountDefault::first();
          if (empty($acd->hutang)) {
            return Response::json(['message' => 'Akun Default Hutang Belum Ditentukan!'],500);
          }
          CashTransactionDetail::create([
            'header_id' => $i->id,
            'account_id' => $acd->hutang,
            'contact_id' => $debt->debtDetails()->first()->payable->contact_id,
            'amount' => $value['total'],
            'description' => @$value['description'],
            'jenis' => 1
          ]);
        }
        //jika ada giro
        if ($value['cek_giro_id']??null) {
          $cg=CekGiro::find($value['cek_giro_id'])->update([
            'is_used' => 1,
            'reff_no' => $trx_code
          ]);
        }
      }
      foreach ($request->paymentbp_detail as $key => $value) {
        if(is_null($value))
            continue;
        DebtPayment::create([
          'header_id' => $id,
          'journal_id' => $j->id,
          'create_by' => auth()->id(),
          'reff' => $value['nmrbp'],
          'total' => $value['totalbp'],
          'description' => $value['description']??null,
          'cash_account_id' => $value['bp_cash_account_id']??null,
          'cek_giro_id' => $value['cek_giro_id']??null,
        ]);
      }
      
      DB::commit();
    }
    public function draftListHutangDetail($id)
    {
        $item = Payable::with('payableDetails','company','contact','journal','type_transaction')
            ->find($id);
        return Response::json(['item'=>$item]);
    }

    public function draftListSpb($id)
    {
        $item = Payable::with('payableDetails','company','contact','journal','type_transaction')
            ->where('company_id', $id);
        return Response::json(['item'=>$item]);
    }

    public function chooseListHutangPrint(Request $request)
    {
      $now=date('d M Y',strtotime(now()));
      $start_date=date('Y-01-01', strtotime(now()));
      $end_date=date('Y-m-t', strtotime($now));

      $id = [];
      foreach($request->all() as $rid) {
        $rid = json_decode($rid);          
        array_push($id,$rid->id);
      }

      $receivable= Receivable::whereIn('id',$id)->get();
      $sum_receivable= Receivable::whereIn('id',$id)->sum('debet');
      foreach($receivable as $receiv){
        $update= Receivable::find($receiv->id);
        $update->is_confirm= 1;
        $update->save();

      }

      $id_receivable= $receivable->pluck('id')->toArray();
      $receivable_detail= ReceivableDetail::whereIn('header_id',$id_receivable)->get();

      $data['customer_id'] = $receivable[0]['contact_id'];
      $data['start_date']= $start_date;
      $data['end_date']= $end_date;
      $data['amount']= $sum_receivable;
      $data['detail']= $receivable_detail;

      //Create receivable confirm
      $rc_controller = new \App\Http\Controllers\Finance\ReceivableConfirmController();
      $rc=$rc_controller->store(new Request($data));

      $contacts= DB::table('contacts')->where('id', $receivable[0]['contact_id'])->first();
       $company= DB::table('companies')->where('id', auth()->user()->company_id)->first();

       $data['now'] = $now;
       $data['receivable']= $receivable;
       $data['receivable_detail']= $receivable_detail;
       $data['confirm']= $rc;
       $data['contact']= $contacts;
       $data['company']= $company;

      $pdf =  PDF::loadView('pdf.konfirmasi_piutang',$data);
      return $pdf->download('Tanda Terima Invoice '.date('d-m-Y').'.pdf');
    }

    public function create_jurnal_penagihan_vendor($id,$trx_code){
      $r= Payable::find($id);
      $debt = Debt::where('code',$trx_code)->first();
      $jurnal=[
        'company_id' => $r->company_id,
        'date_transaction' => date('Y-m-d'),
        'created_by' => auth()->id(),
        'code' => $trx_code,
        'description' => $debt->description,
        'debet' => 0,
        'credit' => 0,
        'status' => 2,
        'type_transaction_id' => 29
    ];
    $journal_id = DB::table('journals')
    ->insertGetId($jurnal);
      if($r->is_temporary == 1){
        $r->date_transaction=Carbon::now()->format('Y-m-d');
        $r->journal_id= $journal_id;
        $r->date_tempo = Carbon::now()->addDays($r->period)->format('Y-m-d');
        $r->is_temporary = 0;
        $r->period = 0;
        $r->save();
        $payable_detail= PayableDetail::where('header_id',$r->id)->get();
        foreach($payable_detail as $d){
          $update= PayableDetail::find($d->id);
          $update->journal_id= $journal_id;
          $update->date_transaction= Carbon::now()->format('Y-m-d');
          $update->save();
        }
      }
      return $journal_id;
    }

    public function add_cr(Request $request, $id)
    {
      $request->validate([
          'cash_request' => 'required',
      ]);

      DB::beginTransaction();
      Payable::find($id)->update([
          'cash_request' => $request->cash_request,
          'cr_desc' => $request->cr_desc,
          'status_cr' => "Diajukan",
          'cr_date' => Carbon::now()
      ]);
      DB::commit();
    }

    public function proc_cr(Request $request, $id)
    {
      DB::beginTransaction();

      if($request->type == "approve"){
        Payable::find($id)->update([
          'status_cr' => "Disetujui"
        ]);
      // }else if($request->type == "priority"){
      //   Payable::find($id)->update([
      //     'priority' => $request->priority
      //   ]);
      }else{
        Payable::find($id)->update([
          'status_cr' => "Ditolak"
        ]); 
      }

      DB::commit();
    }

    public function proc_sp(Request $request, $id)
    {
      DB::beginTransaction();

      if($request->type == "approve"){
        Payable::find($id)->update([
          'status_cr' => "Pusat"
        ]);
      }else{
        Payable::find($id)->update([
          'status_cr' => "Ditolak"
        ]); 
      }

      DB::commit();
    }

    public function printPayment($id)
    {
      $data['item']=Debt::with('company')
        ->leftJoin('accounts', 'accounts.id', 'debts.akun_selisih')
        ->where('debts.id', $id)
        ->select(
          'debts.*', 
          'accounts.name AS akun_selisih_name',
          DB::raw('IF(debts.status = 1, "BELUM TERBAYAR", IF(debts.status = 2, "TERBAYAR TANPA BP", IF(debts.status = 3, "TERBAYAR DENGAN BP", ""))) as status')
        )
        ->first();
      $data['detail']=DebtDetail::with('type_transaction:id,name','payable.contact:id,name,address')
        ->where('header_id', $id)
        ->get();
      $data['payment']=DebtPayment::with('cash_account','cek_giro')->where('header_id', $id)->whereNull('reff')->get();
      $data['paymentbp']=DebtPayment::with('cash_account','cek_giro')->where('header_id', $id)->whereNotNull('reff')->get();
      $data['buktipotong'] = [];
      foreach($data['paymentbp'] as $val){
        array_push($data['buktipotong'], $val->reff);
      }
      $data['remark']=DB::table('print_remarks')
        ->where('company_id', $data['item']->company_id)
        ->first();
      return PDF::loadView('pdf.finance.payable.payment', $data)->stream();
    }
}
