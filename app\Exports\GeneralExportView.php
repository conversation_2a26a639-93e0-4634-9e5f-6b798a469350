<?php
/*
usage : return Excel::download(new GeneralExportView($collections,$path,$type), "$fileName$now.xlsx");
$type arrays or collections
*/
namespace App\Exports;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromView;
class GeneralExportView implements FromView
{
  protected $data;
  protected $viewpath;
  public function __construct($data,String $path)
  {
      $this->data =$data;
      $this->viewpath = $path;
  }
  public function view(): View
  {
      return view($this->viewpath, ['data'=>$this->data]);
  }
}
?>