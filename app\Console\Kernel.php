<?php
namespace App\Console;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
class Kernel extends ConsoleKernel
{
    protected $commands = [
      //
    ];
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('reset:numbering')->monthly();
        // $schedule->command('push:notification')->daily();
        $schedule->command('gps:fetch-gps')->everyFifteenMinutes()->appendOutputTo(storage_path('logs/fetch-gps.log'));//triger by env each also update km
        $schedule->command('kurs:jisdor')->hourly();
        $schedule->command('receivable:invoice')->hourly();
        $schedule->command('shipment_schedule:send')->hourly();
        $schedule->command('asset_depreciation:auto')->hourly();
        // $schedule->command('qrcode:generate')->hourly();
    }
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        require base_path('routes/console.php');
    }
}
