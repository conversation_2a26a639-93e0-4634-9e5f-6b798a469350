<?php

namespace App\Abstracts\Vehicle;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use App\Utils\TransactionCode;
use App\Abstracts\Setting\Setting;
use App\Abstracts\Setting\Checker;
use App\Abstracts\Inventory\Warehouse;
use App\Model\UsingItem as ModelUsingItem;
use App\Model\EquipmentDetail as ModelEquipmentDetail;
use App\Model\WarehouseStockDetail;
use App\Abstracts\Journal;
use App\Model\UsingItemDetail;
use Illuminate\Support\Facades\DB as FacadesDB;
use App\Abstracts\Inventory\StockTransaction AS ST2;
use PDO;

class Equipment
{
    protected static $table = 'using_items';
    public static $type_transaction = 'vehicleEquipment';

    /*
      Date : 05-03-2021
      Description : Mengquery data
      Developer : Didin
      Status : Create
    */
    public static function query($params = []) {
        $dt = FacadesDB::table(self::$table)->where('is_equipment', 1);

        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Menampilkan daftar nama item condition
      Developer : Didin
      Status : Create
    */
    public static function index($using_item_id) {
        $params = [];
        if($using_item_id) {
            $params['header_id'] = $using_item_id;
        }
        $dt = self::query($params);
        $dt = $dt->get();

        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Mengambil parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($args) {
        $params = [];
        $params['date_request'] = $args['date_request'] ?? null;
        $params['requested_by'] = $args['requested_by'] ?? null;
        Checker::checkDate($params['date_request']);
        $params['date_request'] = Carbon::parse($params['date_request'])->format('Y-m-d');
        $params['description'] = $args['description'] ?? null;
        $params['warehouse_id'] = $args['warehouse_id'] ?? null;
        $params['vehicle_id'] = $args['vehicle_id'] ?? null;
        $params['is_equipment'] = $args['is_equipment'] ?? 1;

        if(!$params['warehouse_id']) {
            throw new Exception('Warehouse is required');
        } else {
            $wh = Warehouse::show($params['warehouse_id']);
            $params['company_id'] = $wh->company_id;
        }

        return $params;
    }

    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = FacadesDB::table(self::$table)
        ->whereId($id)
        ->first();

        if(!$dt) {
            throw new Exception('UsingItem not found');
        }
    }

    /*
      Date : 29-08-2021
      Description : Menampilkan detail kategori barang
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);
        $dt = FacadesDB::table(self::$table);
        $dt = $dt->leftJoin('companies', 'companies.id', self::$table . '.company_id');
        $dt = $dt->leftJoin('warehouses', 'warehouses.id', self::$table . '.warehouse_id');
        $dt = $dt->leftJoin('using_item_statuses', 'using_items.status', 'using_item_statuses.id');
        $dt = $dt->leftJoin('vehicles', 'vehicles.id', self::$table.'.vehicle_id');
        $dt = $dt->where(self::$table . '.id', $id);
        $dt = $dt->select(self::$table . '.*', 'warehouses.name AS warehouse_name', 'companies.name AS company_name', 'using_item_statuses.name AS status_name', 'vehicles.nopol as nopol');
        $dt = $dt->first();

        return $dt;
    }

    /*
      Date : 29-08-2021
      Description : Menyimpan data
      Developer : Didin
      Status : Create
    */
    public static function store($params) {
        $insert = self::fetch($params);
        $company_id = $insert['company_id'];
        $code = new TransactionCode($company_id, self::$type_transaction);
        $code->setCode();
        $trx_code = $code->getCode();
        if(empty($trx_code)){
          throw new Exception("Format penomoran equipment belum diatur !");
        };
        $insert['created_at'] = Carbon::now();
        $insert['code'] = $trx_code;
        $id = FacadesDB::table(self::$table)->insertGetId($insert);

        EquipmentDetail::storeMultiple($params['detail'], $id);

        return $id;
    }

    /*
      Date : 29-08-2021
      Description : Update data
      Developer : Didin
      Status : Create
    */
    public static function update($params = [], $id) {
        self::validate($id);
        $detail = $params ['detail'] ?? null;
        $update = self::fetch($params);

        FacadesDB::table(self::$table)
        ->whereId($id)
        ->update($update);

        if($detail && is_array($detail)) {
            EquipmentDetail::clearStock($id);
            EquipmentDetail::clear($id);

            EquipmentDetail::storeMultiple($detail, $id);
        }
    }

    /*
      Date : 14-03-2021
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validateIsTakeout($id);
        $EquipmentDetail = UsingItemDetail::where('header_id', $id)->get();
        foreach($EquipmentDetail as $x){
          $wsd = WarehouseStockDetail::where('warehouse_receipt_detail_id', $x->warehouse_receipt_detail_id)
                                    ->where('rack_id', $x->rack_id)
                                    ->where('item_id', $x->item_id)
                                    ->first();
          if($wsd){
            $wsd->available_qty += $x->qty;
            $wsd->onhand_qty -= $x->qty;
            $wsd->save();
          }
        }
        // EquipmentDetail::clear($id);
        FacadesDB::table(self::$table)
        ->whereId($id)->update([
          'status' => 6
        ]);
    }

    /*
      Date : 14-03-2021
      Description : Menghapus stok
      Developer : Didin
      Status : Create
    */
    public static function clearStock($using_item_id) {
        $items = self::index($using_item_id)->where('requested_stock_transaction_id', '!=', null)->pluck('requested_stock_transaction_id')->toArray();
        FacadesDB::table(self::$table)->whereHeaderId($using_item_id)->update([
            'requested_stock_transaction_id' => null
        ]);
        ST2::destroyMultiple($items);
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya draft / request
      Developer : Didin
      Status : Create
    */
    public static function getRequestedStatus() {
        return 1;
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya item keluar
      Developer : Didin
      Status : Create
    */
    public static function getOutboundStatus() {
        return 2;
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya item masuk
      Developer : Didin
      Status : Create
    */
    public static function getInboundStatus() {
        return 3;
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dikeluarkan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsTakeout($id) {
        $dt = self::show($id);
        $approveStatus = self::getOutboundStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was take out');
        }
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dimasukkan di rak tujuan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsTakein($id) {
        $dt = self::show($id);
        $approveStatus = self::getInboundStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was take in at destination bin location');
        }
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dimasukkan di rak tujuan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsRequested($id) {
        $dt = self::show($id);
        $approveStatus = self::getRequestedStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data still requested');
        }
    }

    /*
      Date : 29-08-2021
      Description : Membuat pengeluaran barang
      Developer : Didin
      Status : Create
    */
    public static function itemOut($id) {
        self::validateIsTakeout($id);

        FacadesDB::table(self::$table)->whereId($id)
        ->update([
            'status' => self::getOutboundStatus(),
            'date_approve' => Carbon::now()
        ]);

        EquipmentDetail::doMultipleOutbound($id);

        $EquipmentDetail = UsingItemDetail::leftJoin('items','items.id','using_item_details.item_id')
        ->leftJoin('warehouse_receipt_details','warehouse_receipt_details.id','using_item_details.warehouse_receipt_detail_id')
        ->leftJoin('warehouse_receipts','warehouse_receipts.id','warehouse_receipt_details.header_id')
        ->leftJoin('purchase_orders','purchase_orders.id','warehouse_receipts.purchase_order_id')
        ->where('using_item_details.header_id', $id)
        ->select(
          'using_item_details.id',
          'items.id as item_id',
          'using_item_details.qty',
          'using_item_details.warehouse_receipt_detail_id',
          'using_item_details.rack_id',
          'items.is_stock AS is_stock',
          'items.harga_beli AS harga_beli',
          'using_item_details.total AS total',
          'using_item_details.qty AS qty',
          'purchase_orders.id as po_id'
        )
        ->get();

        foreach($EquipmentDetail as $x){
          $wsd = WarehouseStockDetail::where('warehouse_receipt_detail_id', $x->warehouse_receipt_detail_id)
                                    ->where('rack_id', $x->rack_id)
                                    ->where('item_id', $x->item_id)
                                    ->first();
          if($wsd){
            $wsd->available_qty += $x->qty;
            $wsd->onhand_qty -= ($x->qty * 2);
            $wsd->qty -= $x->qty;
            $wsd->save();
          }

          $po_detail= FacadesDB::table('purchase_order_details')->where('header_id',$x->po_id)->where('item_id',$x->item_id)->first();
          $price=$x->harga_beli;
          if($po_detail != null){
              $price= $po_detail->price;
          }

          UsingItemDetail::where('id', $x->id)->update([
            'cost' => $price,
            'total' => $price*$x->qty,
            'used' => $x->qty
          ]);

        }
        //jurnal pengeluaran barang / penjualan
        if(Journal::is_use_finance()){
          Journal::setJournal(109, $id);
        }

    }

    /*
      Date : 29-08-2021
      Description : Membuat penerimaan barang
      Developer : Didin
      Status : Create
    */
    public static function itemIn($approve_by, $id) {
        self::validateIsRequested($id);
        self::validateIsTakein($id);

        FacadesDB::table(self::$table)->whereId($id)
        ->update([
            'status' => self::getInboundStatus(),
            'approve_by' => $approve_by,
            'date_approve' => Carbon::now()
        ]);

        EquipmentDetail::doMultipleInbound($id);
    }

     /*
      Date : 29-08-2021
      Description : Update reject item usage
      Developer : Atika
      Status : Update
    */

    public static function RejectItemUsage($id) {
      $using_item= ModelUsingItem::find($id);
      $using_item->status = 5;
      $using_item->save();
      $EquipmentDetail = UsingItemDetail::where('header_id', $id)->get();
      foreach($EquipmentDetail as $x){
        $wsd = WarehouseStockDetail::where('warehouse_receipt_detail_id', $x->warehouse_receipt_detail_id)
                                  ->where('rack_id', $x->rack_id)
                                  ->where('item_id', $x->item_id)
                                  ->first();
        if($wsd){
          $wsd->available_qty += $x->qty;
          $wsd->onhand_qty -= $x->qty;
          $wsd->save();
        }
      }
    }


}
