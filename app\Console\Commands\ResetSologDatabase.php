<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetSologDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'solog:reset-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset DB for New Project';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if(!in_array(strtolower(env('APP_ENV', 'local')), ['local', 'local', 'dev', 'development'])){
            $this->error('This command can\'t be executed!');
            die;
        }
        DB::beginTransaction();
        try {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            // ALL TRANSACTIONS
            DB::table('movement_container_details')->truncate();
            DB::table('movement_containers')->truncate();
            $this->info('movement_containers');
            
            DB::table('purchase_order_return_details')->truncate();
            DB::table('purchase_order_returns')->truncate();
            DB::table('purchase_order_receipt_details')->truncate();
            DB::table('purchase_order_details')->truncate();
            DB::table('purchase_orders')->truncate();
            $this->info('purchase_order');
            
            DB::table('purchase_request_details')->truncate();
            DB::table('purchase_requests')->truncate();
            $this->info('purchase_requests');
            
            DB::table('manifest_email_logs')->truncate();
            DB::table('manifest_details')->truncate();
            DB::table('manifest_costs')->truncate();
            DB::table('manifests')->truncate();
            $this->info('manifests');
            
            DB::table('invoice_vendor_taxes')->truncate();
            DB::table('invoice_vendor_details')->truncate();
            DB::table('invoice_vendors')->truncate();
            $this->info('invoice_vendors');
            
            DB::table('delivery_order_ongoing_jobs')->truncate();
            DB::table('delivery_order_status_logs')->truncate();
            DB::table('delivery_order_photos')->truncate();
            DB::table('delivery_order_drivers')->truncate();
            $this->info('delivery_order_drivers');
            
            DB::table('invoice_taxes')->truncate();
            DB::table('invoice_joins')->truncate();
            DB::table('invoice_job_order')->truncate();
            DB::table('invoice_details')->truncate();
            DB::table('invoices')->truncate();
            $this->info('invoices');
            
            DB::table('lead_activities')->truncate();
            DB::table('leads')->truncate();
            $this->info('leads');
            
            DB::table('using_item_details')->truncate();
            DB::table('using_items')->truncate();
            $this->info('using_items');

            DB::table('asset_purchase_details')->truncate();
            DB::table('asset_purchases')->truncate();
            $this->info('asset_purchases');

            DB::table('asset_sales_details')->truncate();
            DB::table('asset_sales')->truncate();
            $this->info('asset_sales');
            
            DB::table('stok_opname_warehouse_details')->truncate();
            DB::table('stok_opname_warehouses')->truncate();
            $this->info('stok_opname_warehouses');
            
            DB::table('stock_initials')->truncate();
            DB::table('stock_transactions_report')->truncate();
            DB::table('stock_transactions')->truncate();
            $this->info('stocks');
            
            DB::table('kpi_logs')->truncate();
            $this->info('kpi_logs');
            DB::table('shipment_statuses')->truncate();
            $this->info('shipment_statuses');
            
            DB::table('picking_details')->truncate();
            DB::table('pickings')->truncate();
            $this->info('pickings');
            
            DB::table('payable_details')->truncate();
            DB::table('payables')->truncate();
            $this->info('payables');
            
            DB::table('receivable_details')->truncate();
            DB::table('receivables')->truncate();
            $this->info('receivables');
            
            DB::table('retur_receipt_details')->truncate();
            DB::table('retur_receipts')->truncate();
            DB::table('returs')->truncate();
            $this->info('returs');
            
            DB::table('item_migration_receipts')->truncate();
            DB::table('item_migration_details')->truncate();
            DB::table('item_migrations')->truncate();
            $this->info('item_migrations');
            
            DB::table('item_deletion_details')->truncate();
            DB::table('item_deletions')->truncate();
            $this->info('item_deletions');
            
            DB::table('inquery_customers')->truncate();
            DB::table('inquery_activities')->truncate();
            DB::table('inqueries')->truncate();
            $this->info('inqueries');
            
            DB::table('packaging_new_items')->truncate();
            DB::table('packaging_old_items')->truncate();
            DB::table('packagings')->truncate();
            $this->info('packagings');
            
            DB::table('sales_order_return_receipts')->truncate();
            DB::table('sales_order_return_details')->truncate();
            DB::table('sales_order_returns')->truncate();
            DB::table('sales_order_details')->truncate();
            DB::table('sales_orders')->truncate();
            $this->info('sales_orders');
            
            DB::table('customer_order_files')->truncate();
            DB::table('customer_order_details')->truncate();
            DB::table('customer_orders')->truncate();
            $this->info('customer_orders');
            
            DB::table('job_status_histories')->truncate();
            DB::table('job_packets')->truncate();
            $this->info('job_packets');
            
            DB::table('cost_types')->truncate();
            $this->info('cost_types');
            
            DB::table('voyage_receipts')->truncate();
            DB::table('voyage_schedules')->truncate();
            $this->info('voyage_schedules');
            
            DB::table('job_order_receivers')->truncate();
            DB::table('job_order_documents')->truncate();
            DB::table('job_order_transits')->truncate();
            DB::table('job_order_costs')->truncate();
            DB::table('job_order_details')->truncate();
            DB::table('job_orders')->truncate();
            $this->info('job_orders');
            
            DB::table('work_order_details')->truncate();
            DB::table('work_orders')->truncate();
            $this->info('work_orders');
            
            DB::table('journal_favorite_details')->truncate();
            DB::table('journal_favorites')->truncate();
            DB::table('journal_details')->truncate();
            DB::table('journals')->truncate();
            $this->info('journals');
            
            DB::table('warehouse_receipt_billings')->truncate();
            DB::table('warehouse_receipt_details')->truncate();
            DB::table('warehouse_receipts')->truncate();
            DB::table('warehouse_stock_details')->truncate();
            DB::table('warehouse_stocks')->truncate();
            $this->info('warehouse_stocks');
            
            // TRANSACTION MASTER            
            DB::table('price_list_costs')->truncate();
            DB::table('price_list_details')->truncate();
            DB::table('price_lists')->truncate();
            $this->info('price_lists');
            
            DB::table('quotation_history_offers')->truncate();
            DB::table('quotation_files')->truncate();
            DB::table('quotation_items')->truncate();
            DB::table('quotation_costs')->truncate();
            DB::table('quotation_details')->truncate();
            DB::table('quotations')->truncate();
            $this->info('quotations');
            
            DB::table('vendor_prices')->truncate();
            $this->info('vendor_prices');
            
            DB::table('vehicle_contacts')->truncate();
            DB::table('vehicle_distances')->truncate();
            DB::table('vehicle_documents')->truncate();
            DB::table('vehicle_drivers')->truncate();
            DB::table('vehicle_insurances')->truncate();
            DB::table('vehicle_locations')->truncate();
            DB::table('vehicle_tires')->truncate();
            DB::table('vehicle_maintenance_details')->truncate();
            DB::table('vehicle_maintenances')->truncate();
            DB::table('vehicle_checklist_items')->truncate();
            DB::table('vehicles')->truncate();
            DB::table('vehicle_variants')->truncate();
            $this->info('vehicles');
            
            DB::table('route_cost_details')->truncate();
            DB::table('route_costs')->truncate();
            DB::table('routes')->truncate();
            $this->info('routes');
            
            DB::table('contact_locations')->truncate();
            DB::table('contact_addresses')->truncate();
            $this->info('contact_locations');
            
            DB::table('container_inspection_details')->truncate();
            DB::table('container_inspections')->truncate();
            DB::table('containers')->truncate();
            $this->info('containers');
            
            DB::table('warehouses')->truncate();
            $this->info('warhouses');
            
            // OTHERS
            DB::table('noticed_notifications')->truncate();
            DB::table('noticed_journal_notifications')->truncate();
            DB::table('notification_users')->truncate();
            DB::table('notification_type_users')->truncate();
            DB::table('notifications')->truncate();
            $this->info('notifications');
            
            DB::table('contacts')->truncate();
            $this->info('contacts');

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    
            DB::commit();
        } catch (Exception $e){
            DB::rollBack();
            dd($e->getMessage());
        }
    }
}
