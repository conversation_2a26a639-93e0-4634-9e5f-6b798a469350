<?php
namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OperationalCost implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        return collect($report_controller->get_cost($this->request));
    }

    public function headings(): array
    {
        return [
            'Tanggal SJ',
            'Tanggal Kegiatan',
            'Nama Cost',
            'Nopol',
            'Nama',
            'No. Rekening',
            'Bank',
            'Kategori',
            // 'Deskripsi',
            'No. Order',
            'No. Muatan',
            'No. SJ Pabrik',
            'No. SJ Customer',
            'Jenis Item',
            // 'Satuan',
            'No. Container',
            // 'Qty',
            'Sektor',
            'Customer',
            'Biaya Rencana',
            'Status',
            'Total Biaya'
        ];
    }
    public function map($data): array
    {
        /*
            Date : 30-09-2022 By : <PERSON> Rizky
            Description : [Improve] Report Operational Cost
            Keterangan : Menambah field co-driver untuk file export excel operational cost
        */
        return [
            date('Y-m-d', strtotime($data['tgl_sj'])) ?? '-',
            date('Y-m-d', strtotime($data['shipment_date'])) ?? '-',
            $data['cost_name'] ?? '',
            $data['kendaraan'] ?? '-',
            $data['name'] ?? '-',
            $data['rek_no'] ?? '-',
            $data['bank'] ?? '-',
            $data['kategori'] ?? '-',
            // $data['joc_description'] ?? '-',
            $data['jo_code'] ?? '-',
            $data['ma_code'] ?? '-',
            $data['sj_code'] ?? '-',
            $data['no_sj_customer'] ?? '-',
            $data['jo_item'] ?? '-',
            // $data['satuan'] ?? '-',
            ($data['container_code'] .' - '. $data['container_no']) ?? '-',
            // $data['qty'] ?? '-',
            $data['name_sektor'] ?? '-',
            $data['customer_name'] ?? '-',
            number_format($data['price_rencana']) ?? '-',
            $data['status_cost_name'] ?? '-',
            number_format($data['price']) ?? '-'
        ];
        // Close Description : [Improve] Report Operational Cost
    }
}
