<?php

namespace App\Http\Controllers\Api;

use App\Abstracts\Contact\ContactLocation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Vehicle\VehicleController;
use App\Model\Contact;
use App\Abstracts\Operational\DeliveryOrderDriver AS DOD;
use App\Abstracts\Operational\DeliveryOrderDriver as OperationalDeliveryOrderDriver;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Operational\Manifest as OperationalManifest;
use App\Abstracts\Operational\ManifestDetail AS MD;
use App\Abstracts\Operational\V2\DeliveryOrderDriver as V2DeliveryOrderDriver;
use App\Abstracts\Setting\JobStatus;
use App\Jobs\HitungJoCostManifestJob;
use App\Http\Controllers\Api\OperationalApiController as ApiOperationalApiController;
use App\Http\Controllers\Api\v4\OperationalApiController;
use App\Http\Controllers\Operational\InvoiceVendorController;
use App\Http\Controllers\Operational\ManifestFTLController;
use App\Model\Account;
use App\Model\Commodity;
use App\Model\DeliveryManifest;
use App\Model\VendorPrice;
use App\Model\Vehicle;
use App\Model\VehicleContact;
use App\Model\DeliveryOrderDriver;
use App\Model\Manifest;
use App\Model\JobStatusHistory;
use App\Model\ManifestCost;
use App\Model\InvoiceVendor;
use App\Model\Company;
use App\Model\JobOrderDetail;
use App\Model\ManifestDetail;
use App\Model\VehicleOwner;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class VendorApiController extends Controller
{
  public function profile()
  {
    $user = Contact::select('code', 'name', 'address', 'postal_code', 'phone', 'email', 'contact_person', 'contact_person_email', 'contact_person_no', 'latitude', 'longitude')
                  ->find(auth()->user()->id);

    return response()->json(['item' => $user], 200);
  }

  public function change_password(Request $request)
  {
    $request->validate([
      // 'old_password' => 'required',
      'new_password' => 'required',
      're_password' => 'required',
    ], [
      // 'old_password.required' => 'Password lama wajib diisi',
      'new_password.required' => 'Password baru wajib diisi',
      're_password.required' => 'Konfirmasi password baru wajib diisi',
    ]);

    if($request->new_password != $request->re_password){
      return response()->json(['message'=> 'Password baru dan konfirmasi tidak sesuai'], 421);
    }

    $user = Contact::find(auth()->user()->id);
    // $cek = Hash::check($request->old_password, $user->password);
    $cek = true;
    if($cek){
      $user->password = Hash::make($request->new_password);
      $user->save();

      return response()->json(['message'=> 'Berhasil ubah password'], 200);
    }
    return response()->json(['message'=> 'Password lama tidak sesuai'], 421);
  }

  public function vendor_datatable(Request $request)
  {
    $item = Contact::leftJoin('cities','cities.id','=','contacts.city_id')
            ->where('is_vendor', 1)
            ->where('vendor_status_approve', 2)
            ->select('contacts.*',DB::raw("CONCAT(cities.type,' ',cities.name) as cityname"),DB::raw("IFNULL(contacts.code,'-') as codes"));
    $is_active = $request->is_active == null ? '' : $request->is_active;
    // dd($is_active);
    if($is_active == "1"){
      $is_active=1;
    }

    if($is_active != ''){
      $item= $item->where("contacts.is_active", 1);
    }
    return DataTables::of($item)
      ->addColumn('action', function($item){
        $html="<a ng-show=\"roleList.includes('vendor.vendor.detail')\" ui-sref=\"vendor.vendor.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        return $html;
      })
      ->addColumn('action_fr_contact', function($item){
        $html="<a ng-show=\"roleList.includes('vendor.vendor.detail')\" ui-sref=\"contact.vendor.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        return $html;
      })
      ->filterColumn('codes', function($query, $keyword) {
          $sql = "IFNULL(contacts.code,'-') like ?";
          $query->whereRaw($sql, ["%{$keyword}%"]);
        })
      ->filterColumn('cityname', function($query, $keyword) {
          $sql = "CONCAT(cities.type,' ',cities.name) like ?";
          $query->whereRaw($sql, ["%{$keyword}%"]);
        })
      ->rawColumns(['action','action_fr_contact'])
      ->make(true);
  }

  public function register_vendor_datatable()
  {
    $item = Contact::leftJoin('cities','cities.id','=','contacts.city_id')
            ->where('is_vendor', 1)
            ->where('is_active', 1)
            ->where('vendor_status_approve', 1)
            ->select('contacts.*',DB::raw("CONCAT(cities.type,' ',cities.name) as cityname"),DB::raw("IFNULL(contacts.code,'-') as codes"));

    return DataTables::of($item)
      ->editColumn('action', function($item){
        $html="<a ng-show=\"roleList.includes('vendor.register.detail')\" ui-sref=\"vendor.register_vendor.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        $html.="<a ng-show=\"roleList.includes('vendor.register.delete')\" ng-click='deletes($item->id)'><span class='fa fa-trash-o'></span></a>";
        return $html;
      })
      ->filterColumn('codes', function($query, $keyword) {
          $sql = "IFNULL(contacts.code,'-') like ?";
          $query->whereRaw($sql, ["%{$keyword}%"]);
        })
      ->filterColumn('cityname', function($query, $keyword) {
          $sql = "CONCAT(cities.type,' ',cities.name) like ?";
          $query->whereRaw($sql, ["%{$keyword}%"]);
        })
      ->rawColumns(['action'])
      ->make(true);
  }

  /*
    Date : 02-03-2020
    Description : Menampilkan tarif vendor dalam format datatable
    Developer : Didin
    Status : Edit
  */
    public function vendor_price_datatable(Request $request, $id=null)
    {
        $wr="1=1";
        if (isset($id)) {
            $wr.=" AND vendor_prices.vendor_id = $id";
        }
        if ($request->vendor_id) {
            $wr.=" AND vendor_prices.vendor_id = $request->vendor_id";
        }
        if ($request->company_id) {
            $wr.=" AND vendor_prices.company_id = $request->company_id";
        } else {
            if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
                $wr.=" AND vendor_prices.company_id = ".auth()->user()->company_id;
            }
        }
        if($request->filled('is_approve')) {
          $wr.=" AND vendor_prices.is_approve = $request->is_approve";
        }
        if($request->filled('vehicle_type_id')) {
          $wr.=" AND vehicle_types.id = $request->vehicle_type_id";
        }
        if($request->filled('cost_type_id')) {
          $wr.=" AND cost_types.id = $request->cost_type_id";
        }
        $user = $request->user();

        // $item = VendorPrice::with('vendor','company','commodity','service','piece','route','moda','vehicle_type','service_type')->whereRaw($wr)->select('vendor_prices.*');
        $item = DB::table('vendor_prices')
        ->leftJoin('contacts','contacts.id','vendor_prices.vendor_id')
        ->leftJoin('companies','companies.id','vendor_prices.company_id')
        ->leftJoin('routes','routes.id','vendor_prices.route_id')
        ->leftJoin('cost_types','cost_types.id','vendor_prices.cost_type_id')
        ->leftJoin('vehicle_types','vehicle_types.id','vendor_prices.vehicle_type_id')
        ->leftJoin('container_types','container_types.id','vendor_prices.container_type_id')
        ->whereRaw($wr)
        ->whereIsUsed(1)
        ->selectRaw('
          vendor_prices.id,
          vendor_prices.date,
          vendor_prices.vendor_id,
          vendor_prices.cost_category,
          vendor_prices.price_full,
          cost_types.id as cost_type_id,
          if(vehicle_types.name is not null, vehicle_types.name, container_types.code) as vtype,
          cost_types.name as cost_type_name,
          cost_types.id as cost_type_id,
          routes.name as trayek,
          companies.name as cabang,
          contacts.name as vendor,
          is_approve
          ');

        return DataTables::of($item)
        ->filterColumn('vtype', function($query, $keyword) {
            $sql="if(vehicle_types.name is not null, vehicle_types.name, container_types.code) like ?";
            $query->whereRaw($sql, ["%{$keyword}%"]);
        })
        ->filterColumn('is_approve', function($query, $keyword) {
          $sql = "IF(is_approve=0,'Pengajuan',IF(is_approve=1,'Disetujui',IF(is_approve=2, 'Ditolak'))) like ?";
          $query->whereRaw($sql, ["%{$keyword}%"]);
        })
        ->editColumn('price_full', function($item){
            return number_format($item->price_full);
        })
        ->addColumn('action', function($item){
            $html="<a ui-sref=\"marketing.vendor_price.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ui-sref=\"vendor.register_vendor.show.price.edit({id:$item->vendor_id,idprice:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
            return $html;
        })
        ->addColumn('action_fr_contact', function($item){
            $html="<a ui-sref=\"contact.vendor.show.price.edit({id:$item->vendor_id,idprice:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
            return $html;
        })
        ->addColumn('action_approve', function($item){
            $html="<a ui-sref=\"vendor.vendor.show.price.edit({id:$item->vendor_id,idprice:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
            return $html;
        })
        ->rawColumns(['action','action_approve','action_fr_contact'])
        ->make(true);
    }

  public function add_vehicle()
  {
    $data['vehicle_owner']=DB::table('vehicle_owners')->get();
    $data['vehicle_variant']=DB::table('vehicle_variants')->get();
    $data["vehicle_owner"]=DB::table("vehicle_owners")->get();

    return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }

  /**
   * Date : 07-02-2022
   * Description : Menambahkan kolom no stnk, nama stnk, alamat stnk, masa berlaku stnk, no bpkb, tgl kir, kapasitas ban serep
   * Developer : Syahrul
   * Status : Edit
   */
  public function store_vehicle(Request $request,$id=null)
  {
    DB::beginTransaction();
    // return response()->json($request->all());
    $vhcOwner = VehicleOwner::where('name', 'Eksternal')->first();
    if ($id) {
      $request->validate([
        'nopol' => 'required|unique:vehicles,nopol,'.$id,
        'tahun_pembuatan' => 'required|numeric|digits:4',
        'stnk_no' => 'required',
        'stnk_date' => 'required',
        'kir_date' => 'required',
        'tahun_pembuatan' => 'required'
        // 'vehicle_owner_id' => 'required'
      ],[
        'nopol.required' => 'Nomor Polisi harus diisi',
        'tahun_pembuatan.digits' => 'Tahun pembuatan harus 4 digit',
        'stnk_no.required' => 'No STNK harus diisi',
        'stnk_date.required' => 'Masa Berlaku STNK harus diisi',
        'kir_date.required' => 'Tanggal KIR terakhir harus diisi',
        'tahun_pembuatan.required' => 'Tahun pembuatan harus diisi'
        // 'vehicle_owner_id.required' => 'Tipe Kepemilikan Kendaraan harus diisi'
      ]);

      Vehicle::find($id)->update([
        'company_id' => auth()->user()->company_id,
        'vehicle_owner_id' => $request->vehicle_owner_id ?? $vhcOwner->id,
        'vehicle_variant_id' => $request->vehicle_variant_id,
        'supplier_id' => auth()->user()->id,
        'code' => $request->code,
        'nopol' => $request->nopol,
        'chassis_no' => $request->chassis_no,
        'machine_no' => $request->machine_no,
        'date_manufacture' => $request->tahun_pembuatan ? $request->tahun_pembuatan.'-01-01' : null,
        'date_operation' => $request->tgl_operasi ? Carbon::parse($request->tgl_operasi)->format('Y-m-d') : null,
        'color' => $request->color,
        'is_active' => $request->is_active,
      ]);
    } else {
      $request->validate([
        'nopol' => 'required|unique:vehicles,nopol,'.$id,
        'tahun_pembuatan' => 'required|numeric|digits:4',
        'stnk_no' => 'required',
        'stnk_date' => 'required',
        'kir_date' => 'required',
        'tahun_pembuatan' => 'required'
        // 'vehicle_owner_id' => 'required'
      ],[
        'nopol.required' => 'Nomor Polisi harus diisi',
        'tahun_pembuatan.digits' => 'Tahun pembuatan harus 4 digit',
        'stnk_no.required' => 'No STNK harus diisi',
        'stnk_date.required' => 'Masa Berlaku STNK harus diisi',
        'kir_date.required' => 'Tanggal KIR terakhir harus diisi',
        'tahun_pembuatan.required' => 'Tahun pembuatan harus diisi'
        // 'vehicle_owner_id.required' => 'Tipe Kepemilikan Kendaraan harus diisi'
      ]);

      $data =  [
        'company_id' => auth()->user()->company_id,
        'code' => $request->code,
        'nopol' => $request->nopol,
        'vehicle_variant_id' => $request->vehicle_variant_id,
        'chassis_no' => $request->chassis_no,
        'machine_no' => $request->machine_no,
        'color' => $request->color,
        'bbm_capacity' => 0,
        'vehicle_owner_id' => $request->vehicle_owner_id ?? $vhcOwner->id,
        'supplier_id' => auth()->user()->id,
        'date_manufacture' => $request->tahun_pembuatan.'-01-01',
        'date_operation' => $request->tgl_operasi ? Carbon::parse($request->tgl_operasi)->format('Y-m-d') : null,
        'is_active' => $request->is_active,
        'not_active_reason' => null,
        'stnk_no' => $request->stnk_no,
        'stnk_date' => $request->stnk_date ? Carbon::parse($request->stnk_date)->format('Y-m-d') : null,
        'stnk_name' => $request->stnk_name,
        'stnk_address' => $request->stnk_address,
        'bpkb_no' => $request->bpkb_no,
        'kir_date' => $request->kir_date ? Carbon::parse($request->kir_date)->format('Y-m-d') : null,
        'initial_km' => 0,
        'initial_km_date' => null,
        'last_km' => (int) 0,
        'last_km_date' => null,
        'daily_distance' => 0,
        'bbm_usage_ratio' => 0,
        'is_use_km_gps' => 1,
        'is_internal' => 0,
        'serep_tire' => $request->serep_tire ?? 0,
        'is_trailer' => 0,
        'trailer_size' => 0,
        'max_tonase' => 0,
        'max_volume' => 0,
      ];

      $vc = new VehicleController;
      $vc->store(new Request($data));

      // Vehicle::create([
      //   'company_id' => auth()->user()->company_id,
      //   'vehicle_owner_id' => $request->vehicle_owner_id,
      //   'supplier_id' => auth()->user()->id,
      //   'code' => $request->code,
      //   'nopol' => $request->nopol,
      //   'chassis_no' => $request->chassis_no,
      //   'machine_no' => $request->machine_no,
      //   'color' => $request->color,
      //   'is_active' => $request->is_active,
      //   'is_internal' => 0
      // ]);
    }
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function assign_driver(Request $request,$id)
  {
    $request->validate([
      'driver_id' => 'required',
      'vehicle_id' => 'required'
    ],[
      'driver_id.required' => 'Nama Driver harus dipilih',
      'vehicle_id.required' => 'Kendaraan harus dipilih',
    ]);
    DB::beginTransaction();
    $isi=[
      'vehicle_id' => $request->vehicle_id,
      'driver_id' => $request->driver_id,
    ];
    $dod=DeliveryOrderDriver::find($id);
    Manifest::find($dod->manifest_id)->update($isi);
    $isi['job_status_id']=2;//asign to driver
    //update history status
    JobStatusHistory::create([
      'delivery_id' => $dod->id,
      'job_status_id' => 2,
      'vendor_id' => auth()->id(),
      'driver_id' => $request->driver_id,
      'vehicle_id' => $request->vehicle_id
    ]);
    //end update
    $dod->update($isi);
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function reject_job($id)
  {
    DB::beginTransaction();
    $isi=[
      'vehicle_id' => null,
      'driver_id' => null,
      'nopol' => null,
      'driver' => null,
    ];
    $dod=DeliveryOrderDriver::find($id);
    Manifest::find($dod->manifest_id)->update($isi);
    $js=DB::table('job_statuses')->where('is_reject',1)->first();
    $isi['job_status_id']=$js->id;
    $isi['vendor_id']=null;
    $dod->update($isi);
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  /**
   * Date : 07-02-2022
   * Description : Menambahkan kolom no_ktp & no_sim
   * Developer : Syahrul
   * Status : Edit
   */
  public function store_driver(Request $request,$id=null)
  {
    DB::beginTransaction();
    if ($id) {
      $request->validate([
        'email' => 'required|unique:contacts,email,'.$id,
        'name' => 'required',
        'address' => 'required',
        'no_ktp' => 'required|max:16',
        'no_sim' => 'required'
      ],[
        'email.required' => 'Email Driver harus diisi',
        'name.required' => 'Nama driver harus diisi',
        'address.required' => 'Alamat harus diisi',
        'no_ktp.required' => 'Nomor KTP harus diisi',
        'no_ktp.max' => 'Nomor KTP maksimal 16 digit',
        'no_sim.required' => 'Nomor SIM harus diisi'
      ]);

      $update=[
        'company_id' => auth()->user()->company_id,
        'parent_id' => auth()->user()->id,
        'is_driver' => 1,
        'is_internal' => 0,
        'name' => $request->name,
        'address' => $request->address,
        'email' => $request->email,
        'phone' => $request->phone1,
        'phone2' => $request->phone2,
        'city_id' => $request->city_id,
        'postal_code' => $request->post_code,
        'is_active' => $request->is_active,
        'no_ktp' => $request->no_ktp,
        'no_sim' => $request->no_sim,
      ];
      if ($request->password) {
        $update['password']=bcrypt($request->password);
      }
      Contact::find($id)->update($update);
    } else {
      $request->validate([
        'email' => 'required|unique:contacts,email',
        'name' => 'required',
        'password' => 'required',
        'address' => 'required',
        'no_ktp' => 'required|max:16',
        'no_sim' => 'required'
      ],[
        'email.required' => 'Email Driver harus diisi',
        'name.required' => 'Nama driver harus diisi',
        'password.required' => 'Password driver harus diisi',
        'address.required' => 'Alamat harus diisi',
        'no_ktp.required' => 'Nomor KTP harus diisi',
        'no_ktp.max' => 'Nomor KTP maksimal 16 digit',
        'no_sim.required' => 'Nomor SIM harus diisi'
      ]);

      Contact::create([
        'company_id' => auth()->user()->company_id,
        'parent_id' => auth()->user()->id,
        'is_driver' => 1,
        'is_internal' => 0,
        'name' => $request->name,
        'address' => $request->address,
        'email' => $request->email,
        'phone' => $request->phone1,
        'phone2' => $request->phone2,
        'city_id' => $request->city_id,
        'postal_code' => $request->post_code,
        'password' => bcrypt($request->password),
        'is_active' => $request->is_active,
        'api_token' => str_random(100),
        'no_ktp' => $request->no_ktp,
        'no_sim' => $request->no_sim,
      ]);
    }
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }
  public function delete_vehicle_driver($id)
  {
    DB::beginTransaction();
    VehicleContact::find($id)->delete();
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function delete_vehicle($id)
  {
    DB::beginTransaction();
    Vehicle::find($id)->delete();
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function show_vehicle($id)
  {
    $data['item']=DB::table('vehicles')
    ->leftJoin('vehicle_owners','vehicle_owners.id','vehicles.vehicle_owner_id')
    ->where('vehicles.id', $id)
    ->selectRaw('vehicles.*,vehicle_owners.name as vehicle_owner, YEAR(date_manufacture) as tahun_pembuatan')->first();
    $data['vehicle_owner']=DB::table('vehicle_owners')->get();

    return response()->json($data,200);
  }
  public function show_driver($id)
  {
    $data['item']=DB::table('contacts')
    ->leftJoin('cities', 'cities.id', 'contacts.city_id')
    ->where('contacts.id', $id)
    ->selectRaw('contacts.*, cities.name as city')->first();

    return response()->json($data,200);
  }

  public function delete_driver($id)
  {
    DB::beginTransaction();
    DB::table('contacts')->where('id', $id)->delete();
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function vehicle_datatable(Request $request)
  {

    $wr="1=1";
    if ($request->supplier_id) {
      $wr.=" and vehicles.supplier_id = ".$request->supplier_id;
    }
    if ($request->driver_id) {
      $wr.=" and vehicle_contacts.contact_id = ".$request->driver_id;
    }

    $item=DB::table('vehicles')
    ->leftJoin('vehicle_owners','vehicle_owners.id','vehicles.vehicle_owner_id')
    ->leftJoin('vehicle_contacts','vehicle_contacts.vehicle_id','vehicles.id')
    ->leftJoin('contacts','vehicle_contacts.contact_id','contacts.id')
    ->leftJoin('vehicle_variants as vv', 'vv.id', 'vehicles.vehicle_variant_id')
    ->leftJoin('vehicle_types as vt', 'vt.id', 'vv.vehicle_type_id')
    ->whereRaw($wr)
    ->selectRaw('
      vehicles.*,
      vehicle_owners.name as owner,
      vv.name as vehicle_variant,
      vv.year_manufacture,
      vt.name as vehicle_type,
      group_concat(contacts.name separator "<br>") as driver_list,
      concat(ifnull(vehicles.chassis_no,\'-\'),\' / \',ifnull(vehicles.machine_no,\'-\')) as chassis_machine
    ')
    ->groupBy('vehicles.id');

    return DataTables::of($item)
    ->filterColumn('chassis_machine', function($query, $keyword) {
      $sql="concat(ifnull(vehicles.chassis_no,'-'),' / ',ifnull(vehicles.machine_no,'-')) like ?";
      $query->whereRaw($sql, ["%{$keyword}%"]);
    })
    ->editColumn('action', function($item){
      $html="<a ui-sref=\"p.vehicle.show({id:$item->id})\"><i class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></i></a>&nbsp;";
      $html.="<a ui-sref=\"p.vehicle.edit({id:$item->id})\"><i class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></i></a>&nbsp;";
      $html.="<a href='' ng-click=\"deletes($item->id)\"><i class='fa fa-trash'></i></a>";
      return $html;
    })
    ->editColumn('is_active', function($item){
      $stt=[
        1 => '<span class="badge badge-success">AKTIF</span>',
        0 => '<span class="badge badge-danger">TIDAK AKTIF</span>',
      ];
      return $stt[$item->is_active];
    })
    ->rawColumns(['action','is_active','driver_list'])
    ->make(true);
  }
  public function vehicle_driver_datatable(Request $request)
  {
    $wr="1=1";
    if ($request->driver_id) {
      $wr.=" and vehicle_contacts.contact_id = ".$request->driver_id;
    }

    $item=DB::table('vehicle_contacts')
    ->leftJoin('vehicles','vehicles.id','vehicle_contacts.vehicle_id')
    ->whereRaw($wr)
    ->selectRaw('
      vehicles.*,
      vehicle_contacts.id as vid,
      concat(ifnull(vehicles.chassis_no,\'-\'),\' / \',ifnull(vehicles.machine_no,\'-\')) as chassis_machine
    ');

    return DataTables::of($item)
    ->filterColumn('chassis_machine', function($query, $keyword) {
      $sql="concat(ifnull(vehicles.chassis_no,'-'),' / ',ifnull(vehicles.machine_no,'-')) like ?";
      $query->whereRaw($sql, ["%{$keyword}%"]);
    })
    ->editColumn('action', function($item){
      $html="<a ng-click=\"deletes($item->vid)\"><i class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></i></a>";
      return $html;
    })
    ->editColumn('is_active', function($item){
      $stt=[
        1 => '<span class="badge badge-success">AKTIF</span>',
        2 => '<span class="badge badge-danger">TIDAK AKTIF</span>',
      ];
      return $stt[$item->is_active];
    })
    ->rawColumns(['action','is_active'])
    ->make(true);
  }
  public function driver_datatable(Request $request)
  {
    $wr="contacts.is_driver = 1";
    if (auth()->user()->id) {
      $wr.=" and contacts.parent_id = ".auth()->user()->id;
    }

    $item=DB::table('contacts')
    ->leftJoin('vehicle_contacts','vehicle_contacts.contact_id','contacts.id')
    ->leftJoin('vehicles','vehicle_contacts.vehicle_id','vehicles.id')
    ->leftJoin('cities', 'cities.id', 'contacts.city_id')
    ->whereRaw($wr)
    ->selectRaw('
      contacts.*,
      cities.name as city,
      group_concat(vehicles.nopol) as vehicle_list
    ')->groupBy('contacts.id');

    return DataTables::of($item)
    ->filterColumn('vehicle_list', function($query, $keyword) {
      $sql="group_concat(vehicles.nopol) like ?";
      $query->whereRaw($sql, ["%{$keyword}%"]);
    })
    ->filterColumn('city', function($query, $keyword) {
      $sql="cities.name like ?";
      $query->whereRaw($sql, ["%{$keyword}%"]);
    })
    ->editColumn('action', function($item){
      $html="<a ui-sref=\"p.driver.show({id:$item->id})\"><i class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></i></a>&nbsp;";
      $html.="<a ui-sref=\"p.driver.edit({id:$item->id})\"><i class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></i></a>&nbsp;";
      $html.="<a ng-click=\"deletes($item->id)\"><i class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></i></a>";
      return $html;
    })
    ->editColumn('is_active', function($item){
      $stt=[
        1 => '<span class="badge badge-success">AKTIF</span>',
        0 => '<span class="badge badge-danger">TIDAK AKTIF</span>',
      ];
      return $stt[$item->is_active];
    })
    ->rawColumns(['action','is_active'])
    ->make(true);
  }

  public function vehicle_list(Request $request)
  {
    $item=DB::table('vehicles')->where('supplier_id', $request->supplier_id)->selectRaw('id,nopol')->get();
    return response()->json($item,200,[],JSON_NUMERIC_CHECK);
  }
  public function login(Request $request)
  {
    if($request->password != null){
      if($request->filled('type')){
        $password = Crypt::decryptString($request->password);
      }
      else{
        $password = $request->password;
      }

      $auth=Auth::guard('contact')->attempt([
        'email' => $request->email,
        'password' => $password,
      ]);
    }
    else{
      $user = User::where('email', $request->email)->first();

      if(!$user){
        return response()->json(['message' => 'Akun tidak ditemukan!'],500);
      }

      $auth = Auth::guard('contact')->loginUsingId($user->id);
    }

    if ($auth) {
      // User::where('email', $request->email)->update([
      //   'api_token' => str_random(100)
      // ]);
      $user=DB::table('contacts')
      ->where('contacts.email', $request->email)
      ->selectRaw('
        contacts.name,
        contacts.email,
        contacts.api_token,
        contacts.is_vendor,
        contacts.id,
        contacts.company_id,
        contacts.id as contact_id,
        contacts.name as contact_name,
        contacts.address as contact_address,
        contacts.phone as contact_phone,
        contacts.rek_bank_id as contact_bank_id,
        contacts.rek_cabang as contact_rek_cabang,
        contacts.rek_milik as contact_rek_name,
        contacts.rek_no as contact_rek_no
      ')->first();
      // dd($user);
      if ($user->is_vendor==0) {
        return response()->json(['message' => 'Bukan akun customer!'],500);
      } elseif (empty($user->contact_id)) {
        return response()->json(['message' => 'Akun anda belum terdaftar, silahkan menghubungi admin!'],500);
      }
      return response()->json($user, 200, [], JSON_NUMERIC_CHECK);
    } else {
      return response()->json(['message' => 'Username atau Password tidak cocok!'],500);
    }
  }

  public function get_user(Request $request)
  {
    $data=User::with('contact')->where('id', auth()->id())->first();
    return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
  }

  public function store_vehicle_driver(Request $request)
  {
    $request->validate([
      'vehicle_id' => 'required',
      'driver_id' => 'required',
    ],[
      'vehicle_id.required' => 'Kendaraan Harus dipilih!'
    ]);
    DB::beginTransaction();
    VehicleContact::create([
      'contact_id' => $request->driver_id,
      'vehicle_id' => $request->vehicle_id,
      'is_active' => 1,
      'driver_status' => 1
    ]);
    DB::commit();

    return response()->json(null,200,[],JSON_NUMERIC_CHECK);
  }

  public function order_datatable(Request $request)
  {
    $request->merge([
      'supplier_id' => auth()->user()->id
    ]);

      $dt = new ApiOperationalApiController();
      $dt = $dt->manifest_datatable($request);
      $dt = $dt->getData();

      return response()->json($dt, 200);
  }

  public function delivery_order_datatable(Request $request)
  {
    $request->merge([
      'vendor_id' => auth()->user()->id
    ]);

    $item = DOD::query($request->all());

    $item = $item->selectRaw('
        delivery_order_drivers.*,
        manifests.code as code_pl,
        driver.name as driver,
        companies.name as company_name,
        vehicles.nopol,
        routes.name as trayek,
        job_statuses.name as status_name,
        if(delivery_order_drivers.driver_id is not null,driver.name, delivery_order_drivers.driver_name) as sopir,
        if(delivery_order_drivers.vehicle_id is not null,vehicles.nopol, delivery_order_drivers.nopol) as kendaraan
        ');

        return DataTables::of($item)
        ->addColumn('action', function($item){
            $html="<a ng-show=\"roleList.includes('operational.delivery_order.detail')\" ui-sref='operational.delivery_order_driver.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            return $html;
        })
        ->editColumn('pick_date', function($item){
            return dateView($item->pick_date);
        })
        ->editColumn('status_name', function($item){
          if($item->status == 3){
            return '-';
          } else {
            return $item->status_name;
          }
        })
        ->editColumn('status', function($item){
            $stt=[
              1=>'Ditugaskan',
              2=>'Selesai',
              3=>'Dibatalkan'
            ];
            return $stt[$item->status] ?? '';
        })
        ->editColumn('is_finish', function($item){
            $stt=[
              1=>'<span class="badge badge-primary">Finished</span>',
              0=>'<span class="badge badge-warning">Ongoing</span>',
            ];
            return $stt[$item->is_finish] ?? null;
        })
        ->rawColumns(['action','is_finish'])
        ->make(true);
  }

  public function detail_job($id)
  {
      $data['item']=Manifest::show($id);
      $data['detail']=MD::index($id);
      $data['cost']=ManifestCost::with('cost_type','vendor')->where('header_id', $id)->get();
  }

  public function detail_order(Request $request,$id)
  {
      $data = new ManifestFTLController();
      $data = $data->show($id);
      $data = $data->getData();

      return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }

  public function detail_order_delivery($deliveryOrderId)
  {
    $data['delivery_order'] = DOD::show($deliveryOrderId);
    $detail = MD::query(['delivery_order_id' => $deliveryOrderId]);
      $detail = $detail->select(
        'job_order_details.header_id as job_order_id',
        'job_order_details.item_name',
        'job_order_details.weight',
        'job_order_details.volume',
        DB::raw('COALESCE(job_orders.code, sales_orders.code) AS job_order_code'),
        'manifest_details.transported',
        'pieces.name AS piece_name'
      );
      $detail = $detail->get();
      $data['delivery_order_detail'] = $detail;

      $data['history'] = DeliveryOrderStatusLog::index($deliveryOrderId);
      $data['tracking'] = ContactLocation::showHistory($data['delivery_order']->driver_id, $deliveryOrderId);
      $data['from'] = V2DeliveryOrderDriver::showOrigin($deliveryOrderId);
      $data['to'] = V2DeliveryOrderDriver::showReceiver($deliveryOrderId);
      $data['route'] = V2DeliveryOrderDriver::getRoutePlan($deliveryOrderId);

    return response()->json($data, 200);
  }

  public function order($id)
  {
    $data['item']=DB::table('delivery_order_drivers')
    ->leftJoin('manifests','manifests.id','delivery_order_drivers.manifest_id')
    ->leftJoin('vehicle_types','vehicle_types.id','manifests.vehicle_type_id')
    ->leftJoin('container_types','container_types.id','manifests.container_type_id')
    ->leftJoin('routes','routes.id','manifests.route_id')
    ->whereRaw('delivery_order_drivers.id', $id)
    ->selectRaw('
    delivery_order_drivers.*,
    manifests.id as id_manifest,
    manifests.code as code_manifest,
    manifests.date_manifest as date_manifest,
    routes.name as route,
    if(vehicle_types.id is not null,vehicle_types.name,container_types.code) as vtype
    ')->first();
    $data['detail']=DB::table('manifest_details')
    ->leftJoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
    ->leftJoin('commodities','commodities.id','job_order_details.commodity_id')
    ->selectRaw('
      commodities.name as komoditas,
      manifest_details.transported,
      job_order_details.item_name
    ')->get();
    return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }

  public function get_drivers(Request $request)
  {
    $data['driver']=DB::table('contacts')->whereRaw("is_driver = 1 and parent_id = ".auth()->id())->selectRaw('id,name')->get();
    return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }
  public function get_vehicle_drivers(Request $request)
  {
    $data['vehicles']=DB::table('vehicle_contacts')
    ->leftJoin('vehicles','vehicles.id','vehicle_contacts.vehicle_id')
    ->whereRaw('vehicle_contacts.contact_id', $request->driver_id)
    ->selectRaw('vehicles.id,nopol')->get();
    return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }

  public function vendor_job_datatable(Request $request)
  {
    // $manifest_details = DB::raw('(SELECT job_order_details.header_id AS job_order_id, manifest_details.header_id AS manifest_id FROM manifest_details JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id GROUP BY manifest_details.header_id) AS manifest_details');
    // $manifest = DB::table('manifest_costs')
    // ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'manifest_costs.vendor_job_status_id')
    // ->join('contacts AS vendors', 'vendors.id', 'manifest_costs.vendor_id')
    // ->join('manifests', 'manifests.id', 'manifest_costs.header_id')
    // ->join($manifest_details, 'manifest_details.manifest_id', 'manifests.id')
    // ->join('job_orders', 'job_orders.id', 'manifest_details.job_order_id')
    // ->join('contacts', 'contacts.id', 'job_orders.customer_id')
    // ->join('companies', 'companies.id', 'manifests.company_id')
    // ->join('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
    // ->selectRaw('manifest_costs.id, "Manifest" AS source_name, "manifest" AS source, companies.name AS company_name, contacts.name AS customer_name, manifests.code, cost_types.name AS cost_type_name, manifest_costs.qty, manifest_costs.price, manifest_costs.total_price, vendor_job_statuses.name AS vendor_job_status_name, vendor_job_statuses.id AS vendor_job_status_id, vendors.name AS vendor_name');

    // if($request->filled('customer_id')) {
    //     $manifest->where('job_orders.customer_id', $request->customer_id);
    // }

    // if($request->filled('vendor_id')) {
    //     $manifest->where('manifest_costs.vendor_id', $request->vendor_id);
    // }

    $item = DB::table('job_order_costs')
    ->join('job_orders', 'job_orders.id', 'job_order_costs.header_id')
    ->join('contacts AS vendors', 'vendors.id', 'job_order_costs.vendor_id')
    ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'job_order_costs.vendor_job_status_id')
    ->join('contacts', 'contacts.id', 'job_orders.customer_id')
    ->join('companies', 'companies.id', 'job_orders.company_id')
    ->join('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
    ->selectRaw('job_order_costs.id, "Job Order" AS source_name, "job_order" AS source, companies.name AS company_name, contacts.name AS customer_name, job_orders.code, cost_types.name AS cost_type_name, job_order_costs.qty, job_order_costs.price, job_order_costs.total_price, vendor_job_statuses.name AS vendor_job_status_name, vendor_job_statuses.id AS vendor_job_status_id, vendors.name AS vendor_name');
    if($request->filled('customer_id')) {
        $item->where('job_orders.customer_id', $request->customer_id);
    }

    if($request->filled('vendor_id')) {
        $item->where('job_order_costs.vendor_id', $request->vendor_id);
    }
    // $item = $item->union($manifest);

    $item = $item->get()->sortByDesc('id');
    return DataTables::of($item)
    ->make(true);
  }

  public function get_city()
  {
    $data = DB::table('cities')
        ->select('id', 'name')
        ->get();

    return response()->json($data,200,[],JSON_NUMERIC_CHECK);
  }

  public function invoice_vendor_create()
  {
      $data['account']=Account::with('parent')->where('is_base',0)->orderBy('code','asc')->get();
      $data['taxes']=DB::table('taxes')->selectRaw('id,name,pemotong_pemungut,npwp,non_npwp')->get();
      return response()->json($data, 200);
  }

  public function invoice_vendor_store(Request $request)
  {
    $request->merge([
      'company_id' => auth()->user()->company_id,
      'vendor_id' => auth()->user()->id,
      'from_vendor_channel' => 1
    ]);
    $invVendorCont = new InvoiceVendorController;
    $resp = $invVendorCont->store($request);

    return $resp;
  }

  public function invoice_vendor_show($id)
  {
    $invVendorCont = new InvoiceVendorController;
    $resp = $invVendorCont->show($id);

    return $resp;
  }

  public function invoice_datatable(Request $request)
  {
    $wr="1=1";
    $wr.=" AND invoice_vendors.vendor_id = ". auth()->user()->id;

    $item = InvoiceVendor::with('vendor','company')->whereRaw($wr);
    $start_date = $request->start_date;
    $start_date = $start_date != null ? new DateTime($start_date) : '';
    $end_date = $request->end_date;
    $end_date = $end_date != null ? new DateTime($end_date) : '';
    $item = $start_date != '' && $end_date != '' ? $item->whereBetween('date_invoice', [$start_date->format('Y-m-d'), $end_date->format('Y-m-d')]) : $item;

    $company_id = $request->company_id;
    $company_id = $company_id != null ? $company_id : '';
    $item = $company_id != '' ? $item->where('company_id', $company_id) : $item;

    $vendor_id = $request->vendor_id;
    $vendor_id = $vendor_id != null ? $vendor_id : '';
    $item = $vendor_id != '' ? $item->where('vendor_id', $vendor_id) : $item;

    $status = $request->status;
    $status = $status != null ? $status : '';
    $item = $status != '' ? $item->where('status', $status) : $item;
    $item = $item->select('invoice_vendors.*');

    if($request->draw == 1) {
        $item = $item->orderBy('invoice_vendors.id', 'DESC');
    }

    return DataTables::of($item)
      ->addColumn('action', function($item){
        // $html="<a ui-sref='main.invoice.show({id:".$item->id."})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>";
        return $item->id;
      })
      ->editColumn('status', function($item){
        $stt=[
          1=>'Belum Lunas',
          2=>'Lunas',
        ];
        return $stt[$item->status];
      })
      // ->editColumn('status_approve', function($item){
      //   $stt=[
      //     0=>'<span class="badge badge-warning">Belum Disetujui</span>',
      //     1=>'<span class="badge badge-primary">Disetujui</span>',
      //     2=>'<span class="badge badge-success">Jurnal disetujui</span>',
      //     4=>'<span class="badge badge-success">Hutang telah dibuat</span>',
      //   ];
      //   return $stt[$item->status_approve];
      // })
      ->editColumn('total', function($item){
        return formatNumber($item->total);
      })
      ->rawColumns(['status', 'action'])
      ->make(true);
  }

  public function jo_cost_vendor_datatable(Request $request)
  {
    $request->merge([
      'vendor_id' => auth()->user()->id
    ]);

    $opApiCont = new ApiOperationalApiController;
    $dt = $opApiCont->jo_cost_vendor_datatable($request);
    $dt = $dt->getData();

    return response()->json($dt, 200);
  }

  public function get_jo_cost($id)
  {
    $data = DB::table('job_order_costs as joc');
      $data = $data->leftJoin('job_orders as jo','jo.id','joc.header_id');
      $data = $data->leftJoin('cost_types as ct','ct.id','joc.cost_type_id');
      $data = $data->select([
        'joc.id',
        'jo.code',
        'ct.name',
        'joc.total_price',
        'joc.description',
      ]);
      $data = $data->where('joc.id', $id)->first();
      return response()->json($data, 200);
  }

  public function get_jo_statuses($manDetailId)
  {
    $manDetail = ManifestDetail::select('manifest_details.id', 'manifest_details.job_order_detail_id', 'job_statuses.id as status_id', 'job_statuses.name as status_name')
                          ->join('job_statuses', 'job_statuses.id', 'manifest_details.job_status_id')
                          ->where('manifest_details.id',$manDetailId)
                          ->first();
    if(!$manDetail){
      throw new Exception('Job tidak ditemukan!');
    }
    $jod = JobOrderDetail::find($manDetail->job_order_detail_id);
    $job_statuses = JobStatus::query(['job_order_id' => $jod->header_id]);
    $job_statuses = $job_statuses->get();

    $data = [
      'job_statuses' => $job_statuses,
      'current_status' => $manDetail->status_name
    ];

    return response()->json($data, 200);
  }

  public function change_jo_status(Request $request, $id)
  {
    $request->validate([
      'manifest_detail_id' => 'required',
      'job_status_id' => 'required',
    ], [
      'manifest_detail_id.required' => 'Job/SJ wajib diisi',
      'job_status_id.required' => 'Status wajib diisi',
    ]);

    DB::beginTransaction();
    $manDetail = ManifestDetail::find($request->manifest_detail_id);
    $manDetail->job_status_id = $request->job_status_id;
    $manDetail->save();

    OperationalDeliveryOrderDriver::updateMinimumDeliveryStatus($id, $manDetail->header_id, auth()->user()->id, true, true);
    DB::commit();

    return response()->json(['message'=> 'Success'], 200);
  }

  public function set_vehicle_form($id)
  {
    $vehicle_type_id = Manifest::where('manifests.id', $id)
                      ->join('vehicle_types as vt', 'vt.id', 'manifests.vehicle_type_id')
                      ->select('vt.id')
                      ->first();
    $data['item'] = Manifest::select('id', 'code')->find($id);
    $data['commodity'] = Commodity::select('id', 'name')->get();
    $data['vehicle_eksternal'] = Vehicle::select('vehicles.id', 'vehicles.nopol', 'vehicles.code')
                                ->join('vehicle_variants as variant', 'variant.id', 'vehicles.vehicle_variant_id')
                                ->join('vehicle_types as type', 'type.id', 'variant.vehicle_type_id')
                                ->where('supplier_id', auth()->id())
                                ->where('type.id', $vehicle_type_id->id)
                                ->get();
    $data['driver'] = Contact::where([
                          'is_driver' => 1,
                          'parent_id' => auth()->id()
                        ])->select('id','name')->get();
    return response()->json($data, 200);
  }

  public function set_vehicle(Request $request, $id)
  {
    $request->validate([
      'delivery_order_number' => 'required',
      'driver_eksternal_id' => 'required',
      'vehicle_eksternal_id' => 'required',
    ], [
      'delivery_order_number.required' => 'No. Surat Jalan wajib diisi',
      'driver_eksternal_id.required' => 'Driver wajib diisi',
      'vehicle_eksternal_id.required' => 'Kendaraan wajib diisi',
    ]);

    $request->merge([
      'vendor_id' => auth()->id(),
      'is_vendor_channel' => 1
    ]);
    $dt = new ManifestFTLController;
    $resp = $dt->store_delivery($request, $id);
    // Update ManifestCost
    HitungJoCostManifestJob::dispatch($id);

    return $resp;
  }

  public function cancel_delivery(Request $request, $id)
  {
    $dt = new ManifestFTLController($request, $id);
    $dt = $dt->cancel_vehicle($request, $id);
    return $dt;
  }

  public function company_list(Request $request)
  {
    $data=Company::all();
    return response()->json(['status' => 'OK', 'message' => 'List Cabang','data' => $data],200);
  }

  public function vendor_list(Request $request, $id)
  {
    $data=Contact::where(['company_id' => $id, 'is_vendor' => 1])->get();
    return response()->json(['status' => 'OK', 'message' => 'List Vendor','data' => $data],200);
  }

  public function cost_list(Request $request)
  {
    $data=DB::table('cost_types')->whereNull('parent_id')->get();
    return response()->json(['status' => 'OK', 'message' => 'List Cost','data' => $data],200);
  }

  public function vehicle_type_list(Request $request)
  {
    $data=DB::table('vehicle_types')->get();
    return response()->json(['status' => 'OK', 'message' => 'List Vehicle Type','data' => $data],200);
  }

  public function container_type_list(Request $request)
  {
    $data=DB::table('container_types')->get();
    return response()->json(['status' => 'OK', 'message' => 'List Container Type','data' => $data],200);
  }

  public function route_list(Request $request)
  {
    $data=DB::table('routes')->get();
    return response()->json(['status' => 'OK', 'message' => 'List Route','data' => $data],200);
  }

  public function store_price_list(Request $request)
  {
    $request->validate([
      'name' => 'required',
      'code' => 'required',
      'tgl_mulai_berlaku' => 'required',
      'route_id' => 'nullable',
      'vehicle_type_id' => 'nullable',
      'container_type_id' => 'nullable',
    ]);

    DB::beginTransaction();
    $msg = 'Data successfully saved';
    $status_code = 200;
    $contact = DB::table('contacts')->where('id', auth()->user()->id)->first();

    $params = [
      // 'cost_type_id' => $request->cost_id,
      'cost_category' => 1,
      'service_id' => 0,
      'service_type_id' => 0,
      'name' => $request->name,
      'code' => $request->code,
      'price_full' => $request->unit_price,
      'date' => date("Y-m-d", strtotime($request->tgl_mulai_berlaku)),
      'route_id' => $request->route_id ?? null,
      'vehicle_type_id' => $request->vehicle_type_id ?? null,
      'container_type_id' => $request->container_type_id ?? null,
      'is_approve' => 0,
      'vendor_id' => auth()->user()->id,
      'company_id' => $contact->company_id,
      'created_by' => auth()->user()->id
    ];

    DB::table('vendor_prices')->insert($params);

    DB::commit();

    $data['message'] = $msg;
    return response()->json($data, $status_code);
  }

  public function show_price_list($id)
  {
    $data = DB::table('vendor_prices')->where('vendor_prices.id', $id)
    ->join('companies as com', 'com.id', 'vendor_prices.company_id')
    ->leftJoin('cost_types as ct', 'ct.id', 'vendor_prices.cost_type_id')
    ->leftJoin('container_types as cont', 'cont.id', 'vendor_prices.container_type_id')
    ->leftJoin('vehicle_types as veht', 'veht.id', 'vendor_prices.vehicle_type_id')
    ->leftJoin('routes as rot', 'rot.id', 'vendor_prices.route_id')
    ->selectRaw('
    vendor_prices.*,
    com.name as company_name,
    ct.name as cost_name,
    cont.name as container_name,
    veht.name as vehicle_name,
    rot.name as route_name
    ')
    ->first();

    return response()->json($data);
  }
}
