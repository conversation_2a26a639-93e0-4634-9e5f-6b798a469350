<?php
namespace App\Console\Commands;

use App\Model\Debt;
use App\Model\DeliveryOrderRoute;
use App\Model\RegisterBbm;
use App\Model\RegisterBbmDetail;
use App\Model\StockTransaction;
use App\Utils\TransactionCode;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use SimpleXMLElement;

class Cli extends Command{
    protected $signature = 'cli:tester';
    protected $description = 'Command line tester / thinker ';
    public function __construct()
    {
        parent::__construct();
    }
    public function handle(){
        $this->data_vehicle();
    }
  public function data_vehicle(){
    $slug='157';
    $data = DB::table('vehicles')
    ->where('vehicles.is_active', 1)
    ->whereRaw(
        '(`vehicles`.`nopol` LIKE "%'.$slug.'%" OR `vehicles`.`code` LIKE "%'.$slug.'%")')
    ->leftJoin('delivery_order_drivers', 'delivery_order_drivers.vehicle_id', 'vehicles.id')
    ->leftJoin('contacts', 'contacts.id', 'delivery_order_drivers.driver_id')
    ->leftJoin('job_statuses', 'job_statuses.id', 'delivery_order_drivers.job_status_id')
    ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', 'delivery_order_drivers.id')
    ->leftJoin('manifests', 'manifests.id', 'delivery_manifests.manifest_id')
    ->leftJoin('routes', 'routes.id', 'manifests.route_id')
    ->leftJoin('cities', 'cities.id', 'routes.city_from')
    ->leftJoin('cities as c', 'c.id', 'routes.city_to')
    ->select('vehicles.*', 'delivery_order_drivers.id as delivery_order_id', DB::raw('IFNULL(contacts.name,"Kosong") as name_contact'),
    //  DB::raw('
    // IF(job_statuses.name = "Assigned To Driver" || job_statuses.name = "Terima Job", "Terima Job", 
    //     (IF(job_statuses.name = "Done & Checked" || job_statuses.name = "Selesai", "Selesai",
    //         (IF(job_statuses.name != "Done & Checked", "Kosong","Kosong"))
    //     )) 
    // ) as job_status_name'), 
    DB::raw('IFNULL(max(manifests.code),"Kosong") as code_manifest'), 'manifests.id as id_manifest', 'routes.name as route_name', 'cities.name as city_from', 'c.name as city_to', 'delivery_order_drivers.job_status_id as delivery_job_status_id' , 'delivery_order_drivers.updated_at as delivery_order_driver_update_at', 'job_statuses.name as job_status_name')
    ->whereRaw('`vehicles`.nopol != "" and vehicles.is_trailer=0 and vehicles.last_latitude !=0 and vehicles.last_longitude !=0 and vehicles.no_imei !="" and vehicles.gps_no !="" ')
    ->groupBy('id')
    ->toSql();
    $this->info($data);
  }
}