<?php
namespace App\Abstracts;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use App\Abstracts\Finance\Closing;
use Illuminate\Support\Facades\DB;
use App\Model\{Journal AS J,TypeTransaction,JournalDetail,JobOrderCost,Claim, Debt, DebtPayment, Payable, PayableDetail};
use App\Utils\TransactionCode;
class Journal
{
    protected static $table = 'journals';
    /** [improve] is use module finance 7-11-2022 wawa */
    public static function is_use_finance(){//boolean
        return J::is_use_finance();
    }
    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();
        if(!$dt) {
            throw new Exception('Data not found');
        }
    }
    /*
      Date : 29-08-2021
      Description : Menampilkan detail
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);
        $dt = DB::table(self::$table);
        $dt = $dt->where(self::$table . '.id', $id);
        $dt = $dt->first();
        return $dt;
    }
    /*
      Date : 29-08-2021
      Description : Membatalkan posting
      Developer : Didin
      Status : Create
    */
    public static function unposting($unposting_by, $unposting_reason, $id) {
        $dt = self::show($id);
        Closing::preventByDate($dt->date_transaction);
        if($dt->status == 2) {
            throw new Exception('Data was unposted');
        }
        DB::table(self::$table)->whereId($id)->update([
            'unposting_by' => $unposting_by,
            'unposting_reason' => $unposting_reason,
            'date_unposting' => Carbon::now(),
            'status' => 2
        ]);
    }
    /*
      Date : 29-08-2021
      Description : posting jurnal, untuk memasukkan jurnal ke pembukuan
      Developer : Didin
      Status : Create
    */
    public static function approvePost($posting_by, $item = []) {
        if (empty($item) || array_sum($item)<1) {
            throw new Exception('Tidak Ada Jurnal Dipilih!');
        }
        if(is_array($item)) {
            foreach ($item as $key => $value) {
                if (empty($value)) {
                  continue;
                }
                self::validateWasPosted($key);
                $dt = self::show($key);
                Closing::preventByDate($dt->date_transaction);
                DB::table(self::$table)->whereId($key)->update([
                    'status' => 3,
                    'posting_by' => $posting_by,
                    'date_posting' => date('Y-m-d'),
                ]);
            }
        }
    }
    /*
      Date : 29-08-2021
      Description : Menyetujui jurnal
      Developer : Didin
      Status : Create
    */
    public static function approve($posting_by, $item = []) {
        if (empty($item) || array_sum($item)<1) {
            throw new Exception('Tidak Ada Jurnal Dipilih!');
        }
        if(is_array($item)) {
            foreach ($item as $key => $value) {
                if (empty($value)) {
                  continue;
                }
                self::validateWasApproved($key);
                $dt = self::show($key);
                Closing::preventByDate($dt->date_transaction);
                DB::table(self::$table)->whereId($key)->update([
                    'status' => 2
                ]);
            }
        }
    }
    public static function validateWasPosted($id) {
        $dt = self::show($id);
        if($dt->status == 3) {
            throw new Exception('Journal was posted');
        }
    }
    public static function validateWasApproved($id) {
        $dt = self::show($id);
        if($dt->status == 2) {
            throw new Exception('Journal was approved');
        }
    }
    /*
      Date : 29-08-2021
      Description : posting jurnal, untuk memasukkan jurnal ke pembukuan
      Developer : Didin
      Status : Create
    */
    public static function posting($posting_by, $detail, $id) {
        self::validateWasPosted($id);
        $dt = self::show($id);
        Closing::preventByDate($dt->date_transaction);
        DB::table(self::$table)->whereId($id)->update([
            'posting_by' => $posting_by,
            'date_posting' => Carbon::now(),
            'status' => 3 // 
        ]);
        if(is_array($detail)) {
            foreach ($detail as $value) {
                DB::table('journal_details')->whereId($value['id'])->update([
                    'cash_category_id' => $value['cash_category_id']
                ]);
            }
        }
    }
    /*
      Date : 29-08-2021
      Description : Membatalkan persetujuan jurnal
      Developer : Didin
      Status : Create
    */
    public static function undoApprove($id) {
        $dt = self::show($id);
        Closing::preventByDate($dt->date_transaction);
        DB::table(self::$table)->whereId($id)->update([
            'status' => 1
        ]);
    }
    /*
      Date : 29-08-2020
      Description : Menyimpan jurnal
      Developer : Didin
      Status : Create
    */
    /*
      Date : 12-07-2021
      Description : Tambah simpan jurnal klaim
      Developer : Hendra
      Status : Edit
    */
    public static function setJournal($type_transaction_id, $id) {
        $journal_id= null;
        $params = [];
        $params['date_transaction'] = Carbon::now()->format('Y-m-d');
        $params['type_transaction_id'] = $type_transaction_id;
        $createJournal = true;
        switch ($type_transaction_id) {
            case 14 :
                $purchaseOrder = DB::table('purchase_orders')
                ->whereId($id)
                ->first();
                $grandtotal = DB::table('purchase_order_details')
                ->whereHeaderId($id)
                ->sum('total');
                $account_default = DB::table('account_defaults')
                ->first();
                $purchaseOrderDetails = DB::table('purchase_order_details')
                ->join('items', 'items.id', 'purchase_order_details.item_id')
                ->where('purchase_order_details.header_id', $id)
                ->selectRaw('purchase_order_details.*, items.name AS item_name')
                ->get();
                if($account_default->pembelian == null) {
                    throw new Exception('Akun pembelian belum di-set pada setting default akun');
                }
                if($purchaseOrder->payment_type == 2) {
                    if($account_default->hutang == null) {
                        throw new Exception('Akun hutang belum di-set pada setting default akun');
                    }
                    $params['debet'] = $purchaseOrderDetails->map(function($p){
                        return $p->total;
                    })->toArray();
                    $params['credit'] = $purchaseOrderDetails->map(function($p){
                        return 0;
                    })->toArray();
                    $params['keterangan'] = $purchaseOrderDetails->map(function($p){
                        return 'Pembelian item ' . $p->item_name;
                    })->toArray();
                    $params['account_id'] = $purchaseOrderDetails->map(function($p) use($account_default){
                        $account = DB::table('accounts')
                        ->whereId($account_default->pembelian)
                        ->first();
                        return ['id' => $account_default->pembelian, 'type' => ['id' => $account->type_id]];
                    });
                    $account = DB::table('accounts')
                    ->whereId($account_default->hutang)
                    ->first();
                    $params['account_id'][] = ['id' => $account_default->hutang, 'type' => ['id' => $account->type_id]];
                    $params['credit'][] =  $grandtotal;
                    $params['debet'][] =  0;
                    $params['company_id'] = $purchaseOrder->company_id;
                    $params['description'] = 'Purchase order ' . $purchaseOrder->code;
                    $params['company_id'] = $purchaseOrder->company_id;
                } else {
                    $createJournal = false;
                    $cashTransaction = new \App\Http\Controllers\Finance\CashTransactionController();
                    $cashParams = [];
                    $cashAccount = DB::table('accounts')
                        ->where('no_cash_bank', '>', 0)
                        ->select('id')
                        ->first();
                    $companies= DB::table('companies')->where('id',$purchaseOrder->company_id)->first();
                    if($companies->cash_account_id != null){
                        $cashAccountIdData = $companies->cash_account_id;
                    }
                    else {
                        $cashAccountIdData = $cashAccount->id;
                    };
                    $cashAccountDefaultId = null;
                    //check warehouse is_transit = 1
                    $warehouse= DB::table('warehouses')->where('id',$purchaseOrder->warehouse_id)->first();
                    //if true
                    if($warehouse->is_transit == 1){
                        //check accounts deposit vendor in contacts table
                        $contact= DB::table('contacts')->where('id',$purchaseOrder->supplier_id)->first();
                        if($contact->akun_um_supplier != null){
                            $cashAccountDeposit = DB::table('accounts')
                            ->where('id', $contact->akun_um_supplier)
                            ->select('id')
                            ->first();
                            $cashAccountDefaultId=$cashAccountDeposit->id;
                        }
                        else{
                            //check default accounts deposit vendor
                            $cashAccountDefault= DB::table('account_defaults')->select('deposit_vendor')->first();
                            if($cashAccountDefault->deposit_vendor != null){
                                $cashAccountDeposit = DB::table('accounts')
                                ->where('id', $cashAccountDefault->deposit_vendor)
                                ->select('id')
                                ->first();
                            }
                            $cashAccountDefaultId=$cashAccountDeposit->id;
                        }
                    }
                    if(!$cashAccount) {
                        throw new Exception('Cash account is not exist, please check setting account');
                    }
                    $cashParams['date_transaction'] = Carbon::now()->format('Y-m-d');
                    $cashParams['company_id'] = $purchaseOrder->company_id;
                    $cashParams['cash_bank'] = $cashAccountIdData;
                    $cashParams['type'] = 1;
                    $cashParams['jenis'] = 1;
                    $cashParams['kasbon_id'] = 0;
                    $cashParams['account_id'] = $account_default->pembelian;
                    $cashParams['description'] = 'Purchase order - ' . $purchaseOrder->code;
                    $cashParams['detail'] = $purchaseOrderDetails->map(function($p) use($account_default,$warehouse,$cashAccountDefaultId){
                        //check if warehouse is transit or not
                        if($warehouse->is_transit == 1){
                            $idAccount=$cashAccountDefaultId;
                        }
                        else{
                            $idAccount=$account_default->pembelian;
                        }
                        $params = ['jenis' => 1, 'amount' => $p->total, 'account_id' => $idAccount, 'description' => 'Purchase order item ' . $p->item_name, 'file' => ''];
                        return $params;
                    });
                    $cashTransaction->store(new Request($cashParams));
                    $newCashTransaction = DB::table('cash_transactions')
                    ->orderBy('id', 'desc')
                    ->first();
                    DB::table('purchase_orders')
                    ->whereId($id)
                    ->update([
                        'cash_transaction_id' => $newCashTransaction->id,
                    ]);
                }
            break;
            case 16 :
                $receipt = DB::table('receipts')
                ->join('receipt_lists', 'receipt_lists.header_id', 'receipts.id')
                ->where('receipt_lists.id', $id)
                ->select('receipts.*')
                ->first();
                $grandtotal = DB::table('receipt_list_details')
                ->whereHeaderId($id)
                ->sum('total_price');
                $account_default = DB::table('account_defaults')
                ->first();
                $receiptDetails = DB::table('receipt_list_details')
                ->join('items', 'items.id', 'receipt_list_details.item_id')
                ->where('receipt_list_details.header_id', $id)
                ->selectRaw('receipt_list_details.*, items.name AS item_name, items.account_id')
                ->get();
                if($account_default->pembelian == null) {
                    throw new Exception('Akun pembelian belum di-set pada setting default akun');
                }
                $params['account_id'] = $receiptDetails->map(function($p) use($account_default){
                    if(!$p->account_id) {
                        $account_id = $account_default->inventory;
                        if(!$account_id) {
                            throw new Exception('Akun pada item ' . $p->item_name . ' atau akun inventory pada setting belum diisi');
                        }
                    } else {
                        $account_id = $p->account_id;
                    }
                    $account = DB::table('accounts')
                    ->whereId($account_id)
                    ->first();
                    return ['id' => $account_id, 'type' => ['id' => $account->type_id]];
                });
                $params['debet'] = $receiptDetails->map(function($p){
                    return $p->total_price;
                })->toArray();
                $params['credit'] = $receiptDetails->map(function($p){
                    return 0;
                })->toArray();
                $params['credit'][] =  $grandtotal;
                $params['debet'][] =  0;
                $account = DB::table('accounts')
                ->whereId($account_default->pembelian)
                ->first();
                $params['account_id'][] = ['id' => $account_default->pembelian, 'type' => ['id' => $account->type_id]];
                $params['keterangan'] = $receiptDetails->map(function($p){
                    return 'Penerimaan barang ' . $p->item_name;
                })->toArray();
                $params['company_id'] = $receipt->company_id;
                $params['description'] = 'Penerimaan barang ' . $receipt->code;
            break;
            case 122 :
                $account_default = DB::table('account_defaults')
                                        ->first();
                if(!$account_default->biaya_klaim) {
                    throw new Exception('Biaya klaim harus di-setting terlebih dahulu');
                }
                $claim = DB::table('claims')
                            ->join('contacts', 'contacts.id', 'claims.collectible_id')
                            ->where('claims.id', $id)
                            ->select('claims.id', 'claims.code', 'contacts.akun_piutang', 'claim_type', 'claims.vendor_id', 'claims.driver_id', 'contacts.name AS collectible_name', 'claims.company_id', 'claims.collectible_id')
                            ->first();
                $akun_piutang = $claim->akun_piutang;
                if(!$claim->akun_piutang) {
                    if(!$account_default->piutang) {
                        throw new Exception('Akun piutang harus di-setting terlebih dahulu');
                    }
                    $akun_piutang = $account_default->piutang;
                }
                if($claim->claim_type == 1) {
                    $granted = $claim->driver_id;
                } else {
                    $granted = $claim->vendor_id;
                }
                $suspect = DB::table('contacts')
                            ->whereId($granted)
                            ->first();
                $akun_hutang = $suspect->akun_hutang;
                if(!$suspect->akun_hutang) {
                    if(!$account_default->hutang) {
                        throw new Exception('Akun hutang harus di-setting terlebih dahulu');
                    }
                    $akun_hutang = $account_default->hutang;
                }
                $hutang = DB::table('claim_details')
                            ->whereHeaderId($id)
                            ->sum('total_price');
                $piutang = DB::table('claim_details')
                            ->whereHeaderId($id)
                            ->sum('claim_total_price');
                $left = $hutang - $piutang;
                $src = DB::table('accounts')
                        ->whereId($account_default->biaya_klaim)
                        ->first();
                $params['account_id'] = [];
                $params['account_id'][] = [
                    'id' => $account_default->biaya_klaim,
                    'type' => [
                        'id' => $src->type_id
                    ]
                ];
                $src = DB::table('accounts')
                        ->whereId($account_default->piutang)
                        ->first();
                $params['account_id'][] = [
                    'id' => $account_default->piutang,
                    'type' => [
                        'id' => $src->type_id
                    ]
                ];
                $src = DB::table('accounts')
                        ->whereId($account_default->hutang)
                        ->first();
                $params['account_id'][] = [
                    'id' => $account_default->hutang,
                    'type' => [
                        'id' => $src->type_id
                    ]
                ];
                $params['debet'] = [$left, $piutang, 0];
                $params['credit'] = [0, 0, $hutang];
                $params['keterangan'] = [
                    'Biaya klaim terhadap transaksi ' . $claim->code,
                    'Piutang terhadap ' . $suspect->name,
                    'Hutang terhadap ' . $claim->collectible_name
                ];
                $params['company_id'] = $claim->company_id;
                $params['description'] = 'Klaim -  ' . $claim->code;
            break;
            case 67 :
                $createJournal= false;
                $account_default = DB::table('account_defaults')
                                        ->first();
                if(!$account_default->biaya_klaim) {
                    throw new Exception('Biaya klaim harus di-setting terlebih dahulu');
                }
                if(!$account_default->pendapatan_klaim) {
                    throw new Exception('Akun pendapatan klaim harus di-setting terlebih dahulu');
                }
                $claim = DB::table('claims')
                            ->leftJoin('contacts', 'contacts.id', 'claims.collectible_id')
                            ->where('claims.id', $id)
                            ->select('claims.id', 'claims.code','claims.customer_id','contacts.akun_piutang', 'claim_type', 'claims.vendor_id', 'claims.driver_id', 'contacts.name AS collectible_name', 'claims.company_id', 'claims.collectible_id')
                            ->first();
                $akun_piutang = $claim->akun_piutang;
                if(!$claim->akun_piutang || $claim->akun_piutang == null) {
                    if(!$account_default->piutang) {
                        throw new Exception('Akun piutang harus di-setting terlebih dahulu');
                    }
                    $akun_piutang = $account_default->piutang;
                }
                if($claim->claim_type == 1) {
                    $granted = $claim->driver_id;
                } else {
                    $granted = $claim->vendor_id;
                }
                $suspect = DB::table('contacts')
                            ->whereId($granted)
                            ->first();
                if($suspect != null){
                    $akun_hutang = $suspect->akun_hutang;
                 }
                if(!isset($akun_hutang)) {
                    if(!$account_default->hutang) {
                        throw new Exception('Akun hutang harus di-setting terlebih dahulu');
                    }
                    $akun_hutang = $account_default->hutang;
                }
                $hutang = DB::table('claim_details')
                            ->whereHeaderId($id)
                            ->sum('total_price');
                $piutang = DB::table('claim_details')
                            ->whereHeaderId($id)
                            ->sum('claim_total_price');
                $left = $hutang - $piutang;
                $j=J::create([
                    'company_id' => $claim->company_id,
                    'type_transaction_id' => $type_transaction_id,
                    'date_transaction' => Carbon::now()->format('Y-m-d'),
                    'created_by' => auth()->id(),
                    'code' => $claim->code,
                    'relation_id' => $claim->id,
                    'description' => "Klaim - ".$claim->code
                  ]);
                  //credit klaim
                JournalDetail::create([
                    'header_id' => $j->id,
                    'account_id' => $account_default->biaya_klaim,
                    'debet' => round($piutang),
                    'description' => 'Biaya klaim atas '.$claim->code,
                ]);
                $company= DB::table('companies')->where('id',$claim->company_id)->first();
                $credit_kas= $company->cash_account_id;
                if($credit_kas == null){
                  $credit_kas= $account_default->account_cash;
                }
                if(!$credit_kas){
                  throw new Exception('Akun Kas pada default Belum Dipilih!');
                }
                //kas klaim
                JournalDetail::create([
                    'header_id' => $j->id,
                    'account_id' =>  $credit_kas,
                    'credit' => round($piutang),
                    'description' => 'Kas klaim atas '.$claim->code,
                ]);
                $update= DB::table('claims')->where('id',$id)->first();
                $updateC=Claim::find($update->id);
                $updateC->journal_id= $j->id;
                $updateC->save();
                DB::table('claims')
                ->whereId($id)
                ->update(['journal_id' => $j->id]);
            // Generate hutang
            $codex = new TransactionCode($claim->company_id, 'creditNote');
            $codex->setCode();
            $credit_note = $codex->getCode();
            $params = [];
            $params['company_id'] = $claim->company_id;
            $params['contact_id'] = $claim->customer_id;
            $params['type_transaction_id'] = $type_transaction_id;
            $params['journal_id'] = $j->id;
            $params['credit_note'] = $credit_note;
            $params['relation_id'] = $id;
            $params['created_by'] = auth()->user()->id;
            $params['code'] = $claim->code;
            $params['date_transaction'] = Carbon::now()->format('Y-m-d');
            $params['created_at'] = Carbon::now()->format('Y-m-d');
            $supplier = DB::table('contacts')
                            ->whereId($claim->collectible_id)
                            ->select('term_of_payment')
                            ->first();
            $tempo = $supplier->term_of_payment ?? 1;
            $params['date_tempo'] = Carbon::now()->addDays($tempo)->format('Y-m-d');
            $params['credit'] = $hutang;
            $params['debet'] = 0;
            $params['description'] = 'Klaim - ' . $claim->code;
            $payable_id = DB::table('payables')
                            ->insertGetId($params);
            $params = [];
            $claimDetails  = DB::table('claim_details')
                            ->join('commodities', 'commodities.id', 'claim_details.commodity_id')
                            ->whereHeaderId($id)
                            ->select('commodities.name AS commodity_name', 'claim_details.total_price', 'claim_details.claim_total_price')
                            ->get();
            foreach ($claimDetails as $item) {
                $detail = [];
                $detail['header_id'] = $payable_id;
                $detail['type_transaction_id'] = $type_transaction_id;
                $detail['journal_id'] = $j->id;
                $detail['created_at'] = Carbon::now()->format('Y-m-d');
                $detail['description'] = 'Klaim - ' . $claim->code . ' - atas barang ' . $item->commodity_name;
                $detail['code'] = $claim->code;
                $detail['debet'] = 0;
                $detail['credit'] = $item->total_price;
                $params[] = $detail;
            }
            DB::table('payable_details')
                ->insert($params);
            DB::table('claims')
                ->whereId($id)
                ->update([
                    'payable_id' => $payable_id
                ]);
                if($claim->claim_type != 3) {
            // Generate piutang
            $codex = new TransactionCode($claim->company_id, 'debetNote');
            $codex->setCode();
            $debet_note = $codex->getCode();
            $params = [];
            $params['company_id'] = $claim->company_id;
            $params['contact_id'] = $granted;
            $params['type_transaction_id'] = $type_transaction_id;
            $params['journal_id'] = $j->id;
            $params['debet_note'] = $debet_note;
            $params['relation_id'] = $id;
            $params['created_by'] = auth()->user()->id;
            $params['code'] = $claim->code;
            $params['date_transaction'] = Carbon::now()->format('Y-m-d');
            $params['created_at'] = Carbon::now()->format('Y-m-d');
            $supplier = DB::table('contacts')
                        ->whereId($granted)
                        ->select('term_of_payment')
                        ->first();
            $tempo = $supplier->term_of_payment ?? 1;
            $params['date_tempo'] = Carbon::now()->addDays($tempo)->format('Y-m-d');
            $params['debet'] = $hutang;
            $params['credit'] = 0;
            $params['description'] = 'Klaim - ' . $claim->code;
            $receivable_id = DB::table('receivables')
                                ->insertGetId($params);
            $params = [];
            foreach ($claimDetails as $item) {
                $detail = [];
                $detail['header_id'] = $receivable_id;
                $detail['type_transaction_id'] = $type_transaction_id;
                $detail['journal_id'] = $j->id;
                $detail['created_at'] = Carbon::now()->format('Y-m-d');
                $detail['description'] = 'Klaim - ' . $claim->code . ' - atas barang ' . $item->commodity_name;
                $detail['code'] = $claim->code;
                $detail['credit'] = 0;
                $detail['debet'] = $item->claim_total_price;
                $params[] = $detail;
            }
            DB::table('receivable_details')
                ->insert($params);
            DB::table('claims')
                ->whereId($id)
                ->update([
                    'receivable_id' => $receivable_id
                ]);
            }
            break;
            case 55 :
                $createJournal=false;
                $account_default = DB::table('account_defaults')->first();
                $default=$account_default->item_usage;
                $uitem=DB::table('using_items')->where('id',$id)->first();
                $vh=!empty($uitem->vehicle_id)?DB::table('vehicles')->whereId($uitem->vehicle_id)->first():'';
                $detailitem=DB::table('using_item_details')->where('using_item_details.header_id',$uitem->id)
                ->leftJoin('items','items.id','using_item_details.item_id')
                ->leftJoin('warehouse_receipt_details','warehouse_receipt_details.id','using_item_details.warehouse_receipt_detail_id')
                ->leftJoin('warehouse_receipts','warehouse_receipts.id','warehouse_receipt_details.header_id')
                ->leftJoin('purchase_orders','purchase_orders.id','warehouse_receipts.purchase_order_id')
                ->selectRaw('
                items.account_usage AS account_usage,
                items.account_id AS account_id,
                items.name AS name,
                items.id AS item_id,
                items.is_stock AS is_stock,
                items.harga_beli AS harga_beli,
                using_item_details.total AS total,
                using_item_details.qty AS qty,
                using_item_details.cost AS cost,
                purchase_orders.id as po_id,
                items.is_service
                ')
                ->get();
                //create
                $tp=TypeTransaction::find($type_transaction_id);
                $code = new TransactionCode($uitem->company_id, $tp->slug);
                $code->setCode();
                $trx_code = $code->getCode();
                $j=J::create([
                    'company_id' => $uitem->company_id,
                    'type_transaction_id' => $type_transaction_id,
                    'date_transaction' => Carbon::now()->format('Y-m-d'),
                    'created_by' => auth()->id(),
                    'code' => $uitem->code,
                    'relation_id' => $uitem->id,
                    'description' => !empty($uitem->vehicle_id)?"Perawatan Kendaraan atas nopol ".$vh->nopol:"Penggunaan Item",
                    'debet' => 0,
                    'credit' => 0,
                    'source' => 1,
                    'is_audit' => 0
                  ]);
                  foreach($detailitem as $detail){
                      if($detail->is_stock == 1){
                      $po_detail= DB::table('purchase_order_details')->where('header_id',$detail->po_id)->where('item_id',$detail->item_id)->first();
                    //account debet
                    if($detail->account_usage != null){
                        $debet_account=$detail->account_usage;
                    }
                    else{
                        $debet_account=$account_default->item_usage;
                    }
                    if($debet_account== null) {
                        throw new Exception('Akun penggunaan item belum di-set pada setting default akun');
                    }
                    $price=$detail->cost;
                    if($po_detail != null){
                        $price= $po_detail->price;
                    }
                    if($detail->is_service == 0){
                        $name_pengguna = 'Penggunaan Item - ';
                    } else {
                        $name_pengguna = 'Penggunaan Jasa - ';
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $debet_account,
                        'cash_category_id' => null,
                        'debet' => $price*$detail->qty,
                        'description' => !empty($uitem->vehicle_id)?$name_pengguna.$detail->name." atas vehicle maintenance nopol ".$vh->nopol:$name_pengguna.$detail->name,
                      ]);
                      //account credit
                    if($detail->account_id != null){
                        $credit_account=$detail->account_id;
                    }
                    else{
                        $credit_account=$account_default->persediaan;
                    }
                    if($credit_account== null) {
                        throw new Exception('Akun persediaan belum di-set pada setting default akun');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $credit_account,
                        'cash_category_id' => null,
                        'credit' => $price*$detail->qty,
                        'description' => !empty($uitem->vehicle_id)?$name_pengguna.$detail->name." atas vehicle maintenance nopol ".$vh->nopol:$name_pengguna.$detail->name,
                      ]);
                    }
                  }
                  $search_detail= JournalDetail::where('header_id',$j->id)->get();
                  $grandtotal= DB::table('journal_details')->where('header_id',$j->id)->sum('debet');
                  $updatejournal= J::find($j->id);
                  if(count($search_detail) >= 1){
                    $updatejournal->debet= $grandtotal;
                    $updatejournal->credit= $grandtotal;
                    $updatejournal->save();
                  }else{
                      $updatejournal->delete();
                  }
            break;
            case 56 :
                $createJournal=false;
                $account_default = DB::table('account_defaults')->first();
                $ivendor=DB::table('invoice_vendors')->where('id',$id)->first();
                $ivendor_detail=DB::table('invoice_vendor_details')->where('header_id', $id)->get();
                $ivendor_detail_count=DB::table('invoice_vendor_details')->where('header_id', $id)->count();
                $type= DB::table('type_transactions')->where('slug','maintenance')->first();

                // dd($ivendor, $ivendor_detail, $vm_count);

                $j=J::create([
                    'company_id' => $ivendor->company_id,
                    'type_transaction_id' => $type->id,
                    'date_transaction' => Carbon::now()->format('Y-m-d'),
                    'created_by' => auth()->id(),
                    'code' => $ivendor->code,
                    'relation_id' => $ivendor->id,
                    'description' => "Perawatan Kendaraan atas Invoice ".$ivendor->code,
                    'status' => 1
                  ]);
                  
                  foreach($ivendor_detail as $iv){
                    $vm = DB::table('vehicle_maintenance_items')
                    ->leftJoin('vehicle_maintenances','vehicle_maintenances.id','vehicle_maintenance_items.vehicle_maintenance_id')
                    ->leftJoin('vehicles','vehicles.id','vehicle_maintenances.vehicle_id')
                    ->leftJoin('items','items.id','vehicle_maintenance_items.item_id')
                    ->where('vehicle_maintenance_id', $iv->vehicle_maintenance_id)
                    ->select('vehicle_maintenance_items.*', 'items.name AS item_name', 'items.is_service', 'items.account_usage', 'items.account_id', 'vehicles.nopol')
                    ->get();

                    $vm_count = DB::table('vehicle_maintenance_items')
                    ->where('vehicle_maintenance_id', $iv->vehicle_maintenance_id)
                    ->count();
                    // dd($vm);
                    foreach($vm as $val){

                        if($val->account_usage != null){
                            $debet_account=$val->account_usage;
                        } else {
                            $debet_account=$account_default->item_usage;
                        }

                        if($debet_account== null) {
                            throw new Exception('Akun penggunaan item belum di-set pada setting default akun');
                        }

                        if($val->account_id != null){
                            $credit_account=$val->account_id;
                        }else{
                            $credit_account=$account_default->persediaan;
                        }
                        if($credit_account== null) {
                            throw new Exception('Akun persediaan belum di-set pada setting default akun');
                        }

                        //debet
                        if($ivendor->ppn_is_faktur == 1){
                            JournalDetail::create([
                                'header_id' => $j->id,
                                'account_id' => $debet_account,
                                'cash_category_id' => null,
                                'debet' => round($val->total_price + ($iv->diskon == 0 ? 0 : $iv->diskon / (int)$vm_count) - ($ivendor->total_pph == 0 ? 0 : ($ivendor->total_pph / (int)$ivendor_detail_count) / (int)$vm_count)),
                                'description' => $val->is_service == 0?"Penggunaan Item - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol:"Penggunaan Jasa - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol,
                            ]);
                        } else {
                            JournalDetail::create([
                                'header_id' => $j->id,
                                'account_id' => $debet_account,
                                'cash_category_id' => null,
                                'debet' => round($val->total_price + ($iv->diskon == 0 ? 0 : $iv->diskon / (int)$vm_count) + ($iv->ppn == 0 ? 0 : $iv->ppn / (int)$vm_count)  - ($ivendor->total_pph == 0 ? 0 : ($ivendor->total_pph / (int)$ivendor_detail_count) / (int)$vm_count)),
                                'description' => $val->is_service == 0?"Penggunaan Item - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol:"Penggunaan Jasa - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol,
                            ]);
                        }

                        // credit
                        if($ivendor->ppn_is_faktur == 1){
                            JournalDetail::create([
                                'header_id' => $j->id,
                                'account_id' => $credit_account,
                                'cash_category_id' => null,
                                'credit' => round($val->total_price + ($iv->diskon == 0 ? 0 : $iv->diskon / (int)$vm_count) + ($iv->ppn == 0 ? 0 : $iv->ppn / (int)$vm_count)),
                                'description' => $val->is_service == 0?"Penggunaan Item - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol:"Penggunaan Jasa - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol,
                            ]);
                        } else {
                            JournalDetail::create([
                                'header_id' => $j->id,
                                'account_id' => $credit_account,
                                'cash_category_id' => null,
                                'credit' => round($val->total_price + ($iv->diskon == 0 ? 0 : $iv->diskon / (int)$vm_count) + ($iv->ppn == 0 ? 0 : $iv->ppn / (int)$vm_count) - ($ivendor->total_pph == 0 ? 0 : ($ivendor->total_pph / (int)$ivendor_detail_count) / (int)$vm_count)),
                                'description' => $val->is_service == 0?"Penggunaan Item - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol:"Penggunaan Jasa - ".$val->item_name." atas vehicle maintenance nopol ".$val->nopol,
                            ]);
                        }
                    }
                  }


                  if($ivendor->ppn_is_faktur == 1){
                    if ($ivendor->total_pph != 0) {
                        //pph
                        JournalDetail::create([
                          'header_id' => $j->id,
                          'account_id' => $account_default->bukti_potong,
                          'credit' => round($ivendor->total_pph),
                          'description' => 'PPH atas invoice '.$ivendor->code,
                        ]);
                    }

                    if ($ivendor->ppn != 0) {
                        //ppn
                        JournalDetail::create([
                          'header_id' => $j->id,
                          'account_id' => $account_default->ppn_in,
                          'debet' => round($ivendor->ppn),
                          'description' => 'PPN atas invoice  '.$ivendor->code,
                        ]);
                      }
                  }
                

                  $search_detail= JournalDetail::where('header_id',$j->id)->get();
                  $grandtotal= DB::table('journal_details')->where('header_id',$j->id)->sum('debet');
                  $updatejournal= J::find($j->id);
                  if(count($search_detail) >= 1){
                    $updatejournal->debet= $grandtotal;
                    $updatejournal->credit= $grandtotal;
                    $updatejournal->save();
                  }else{
                    $updatejournal->delete();
                  }
                  return $j->id;
            break;
            case 102:
                $createJournal=false;
                $account_default = DB::table('account_defaults')->first();
                // $default=$account_default->item_usage;
                $uitem=DB::table('register_bbm')->where('id',$id)->first();
                $vh=!empty($uitem->vehicle_id)?DB::table('vehicles')->whereId($uitem->vehicle_id)->first():'';
                $detailitem=DB::table('register_bbm_details')->where('register_bbm_details.header_id',$uitem->id)
                ->leftJoin('items','items.id','register_bbm_details.item_id')
                ->leftJoin('warehouse_receipt_details','warehouse_receipt_details.id','register_bbm_details.warehouse_receipt_detail_id')
                ->leftJoin('warehouse_receipts','warehouse_receipts.id','warehouse_receipt_details.header_id')
                ->leftJoin('purchase_orders','purchase_orders.id','warehouse_receipts.purchase_order_id')
                ->selectRaw('
                items.account_usage AS account_usage,
                items.account_id AS account_id,
                items.name AS name,
                items.id AS item_id,
                items.is_stock AS is_stock,
                items.harga_beli AS harga_beli,
                register_bbm_details.total AS total,
                register_bbm_details.qty AS qty,
                purchase_orders.id as po_id
                ')
                ->get();
                //create
                // $tp=TypeTransaction::find($type_transaction_id);
                // $code = new TransactionCode($uitem->company_id, $tp->slug);
                // $code->setCode();
                // $trx_code = $code->getCode();
                $j=J::create([
                    'company_id' => $uitem->company_id,
                    'type_transaction_id' => $type_transaction_id,
                    'date_transaction' => Carbon::now()->format('Y-m-d'),
                    'created_by' => auth()->id(),
                    'code' => $uitem->code,
                    'relation_id' => $uitem->id,
                    'description' => !empty($uitem->vehicle_id)?"Penggunaan BBM atas vehicle nopol ".$vh->nopol:"Penggunaan BBM",
                    'debet' => 0,
                    'credit' => 0,
                    'source' => 1,
                    'is_audit' => 0
                  ]);
                  foreach($detailitem as $detail){
                      if($detail->is_stock == 1){
                      $po_detail= DB::table('purchase_order_details')->where('header_id',$detail->po_id)->where('item_id',$detail->item_id)->first();
                    //account debet
                    if($detail->account_usage != null){
                        $debet_account=$detail->account_usage;
                    }
                    else{
                        $debet_account=$account_default->item_usage;
                    }
                    if($debet_account== null) {
                        throw new Exception('Akun penggunaan item bbm belum di-set pada setting default akun');
                    }
                    $price=$detail->harga_beli;
                    if($po_detail != null){
                        $price= $po_detail->price;
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $debet_account,
                        'cash_category_id' => null,
                        'debet' => $price*$detail->qty,
                        'description' => !empty($uitem->vehicle_id)?"Penggunaan BBM - ".$detail->name." atas vehicle nopol ".$vh->nopol:"Penggunaan BBM - ".$detail->name,
                      ]);
                      //account credit
                    if($detail->account_id != null){
                        $credit_account=$detail->account_id;
                    }
                    else{
                        $credit_account=$account_default->persediaan;
                    }
                    if($credit_account== null) {
                        throw new Exception('Akun persediaan belum di-set pada setting default akun');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $credit_account,
                        'cash_category_id' => null,
                        'credit' => $price*$detail->qty,
                        'description' => !empty($uitem->vehicle_id)?"Penggunaan BBM - ".$detail->name." atas vehicle nopol ".$vh->nopol:"Penggunaan BBM - ".$detail->name,
                      ]);
                    }
                  }
                  $search_detail= JournalDetail::where('header_id',$j->id)->get();
                  $grandtotal= DB::table('journal_details')->where('header_id',$j->id)->sum('debet');
                  $updatejournal= J::find($j->id);
                  if(count($search_detail) >= 1){
                    $updatejournal->debet= $grandtotal;
                    $updatejournal->credit= $grandtotal;
                    $updatejournal->save();
                  }else{
                      $updatejournal->delete();
                  }
            break;
            case 109 :
                $createJournal=false;
                $account_default = DB::table('account_defaults')->first();
                $default=$account_default->item_usage;
                $uitem=DB::table('using_items')->where('id',$id)->first();
                $vh=!empty($uitem->vehicle_id)?DB::table('vehicles')->whereId($uitem->vehicle_id)->first():'';
                $detailitem=DB::table('using_item_details')->where('using_item_details.header_id',$uitem->id)
                ->leftJoin('items','items.id','using_item_details.item_id')
                ->leftJoin('warehouse_receipt_details','warehouse_receipt_details.id','using_item_details.warehouse_receipt_detail_id')
                ->leftJoin('warehouse_receipts','warehouse_receipts.id','warehouse_receipt_details.header_id')
                ->leftJoin('purchase_orders','purchase_orders.id','warehouse_receipts.purchase_order_id')
                ->selectRaw('
                items.account_usage AS account_usage,
                items.account_id AS account_id,
                items.name AS name,
                items.id AS item_id,
                items.is_stock AS is_stock,
                items.harga_beli AS harga_beli,
                using_item_details.total AS total,
                using_item_details.qty AS qty,
                purchase_orders.id as po_id
                ')
                ->get();
                //create
                $tp=TypeTransaction::find($type_transaction_id);
                $j=J::create([
                    'company_id' => $uitem->company_id,
                    'type_transaction_id' => $type_transaction_id,
                    'date_transaction' => Carbon::now()->format('Y-m-d'),
                    'created_by' => auth()->id(),
                    'code' => $uitem->code,
                    'relation_id' => $uitem->id,
                    'description' => "Penggunaan Part atas ".$uitem->code." atas Nopol ".$vh->nopol,
                    'debet' => 0,
                    'credit' => 0,
                    'source' => 1,
                    'is_audit' => 0
                  ]);
                  foreach($detailitem as $detail){
                    if($detail->is_stock == 1){
                        $po_detail= DB::table('purchase_order_details')->where('header_id',$detail->po_id)->where('item_id',$detail->item_id)->first();
                        //account debet
                        if($detail->account_usage != null){
                            $debet_account=$detail->account_usage;
                        }
                        else{
                            $debet_account=$account_default->item_usage;
                        }
                        if($debet_account== null) {
                            throw new Exception('Akun penggunaan item belum di-set pada setting default akun');
                        }
                        $price=$detail->harga_beli;
                        if($po_detail != null){
                            $price= $po_detail->price;
                        }
                        JournalDetail::create([
                            'header_id' => $j->id,
                            'account_id' => $debet_account,
                            'cash_category_id' => null,
                            'debet' => $price*$detail->qty,
                            'description' => "Penggunaan Part - ".$detail->name." -  ".$vh->nopol,
                        ]);
                        //account credit
                        if($detail->account_id != null){
                            $credit_account=$detail->account_id;
                        }
                        else{
                            $credit_account=$account_default->persediaan;
                        }
                        if($credit_account== null) {
                            throw new Exception('Akun persediaan belum di-set pada setting default akun');
                        }
                        JournalDetail::create([
                            'header_id' => $j->id,
                            'account_id' => $credit_account,
                            'cash_category_id' => null,
                            'credit' => $price*$detail->qty,
                            'description' => "Penggunaan Part - ".$detail->name." -  ".$vh->nopol,
                        ]);
                    }
                  }
                  $search_detail= JournalDetail::where('header_id',$j->id)->get();
                  $grandtotal= DB::table('journal_details')->where('header_id',$j->id)->sum('debet');
                  $updatejournal= J::find($j->id);
                  if(count($search_detail) >= 1){
                    $updatejournal->debet= $grandtotal;
                    $updatejournal->credit= $grandtotal;
                    $updatejournal->save();
                  }else{
                      $updatejournal->delete();
                  }
            break;
        }
        if($createJournal == true) {
            $journal = new \App\Http\Controllers\Finance\JournalController();
            $journal_id = $journal->save(new Request($params));
            if($type_transaction_id == 14) {
                $codex = new TransactionCode($purchaseOrder->company_id, 'creditNote');
                $codex->setCode();
                $credit_note = $codex->getCode();
                DB::table('purchase_orders')
                ->whereId($id)
                ->update(['journal_id' => $journal_id]);
                $params = [];
                $params['company_id'] = $purchaseOrder->company_id;
                $params['contact_id'] = $purchaseOrder->supplier_id;
                $params['type_transaction_id'] = $type_transaction_id;
                $params['journal_id'] = $journal_id;
                $params['credit_note'] = $credit_note;
                $params['relation_id'] = $id;
                $params['created_by'] = auth()->user()->id;
                $params['code'] = $purchaseOrder->code;
                $params['date_transaction'] = Carbon::now()->format('Y-m-d');
                $params['created_at'] = Carbon::now()->format('Y-m-d');
                $supplier = DB::table('contacts')
                ->whereId($purchaseOrder->supplier_id)
                ->select('term_of_payment')
                ->first();
                $tempo = $supplier->term_of_payment ?? 1;
                $params['date_tempo'] = Carbon::now()->addDays($purchaseOrder->due_date)->format('Y-m-d');
                $params['credit'] = $grandtotal;
                $params['debet'] = $grandtotal;
                $params['description'] = 'Purchase order - ' . $purchaseOrder->code;
                $payable_id = DB::table('payables')
                ->insertGetId($params);
                $params = [];
                foreach ($purchaseOrderDetails as $item) {
                    $detail = [];
                    $detail['header_id'] = $payable_id;
                    $detail['type_transaction_id'] = $type_transaction_id;
                    $detail['journal_id'] = $journal_id;
                    $detail['created_at'] = Carbon::now()->format('Y-m-d');
                    $detail['description'] = 'Purchase order - ' . $purchaseOrder->code . ' - atas barang ' . $item->item_name;
                    $detail['code'] = $purchaseOrder->code;
                    $detail['debet'] = 0;
                    $detail['credit'] = $item->total;
                    $params[] = $detail;
                }
                DB::table('payable_details')
                ->insert($params);
                DB::table('purchase_orders')
                ->whereId($id)
                ->update([
                    'payable_id' => $payable_id
                ]);
            } else if($type_transaction_id == 122 || $type_transaction_id == 67) {
                DB::table('claims')
                    ->whereId($id)
                    ->update(['journal_id' => $journal_id]);
                // Generate hutang
                $codex = new TransactionCode($claim->company_id, 'creditNote');
                $codex->setCode();
                $credit_note = $codex->getCode();
                $params = [];
                $params['company_id'] = $claim->company_id;
                $params['contact_id'] = $claim->collectible_id;
                $params['type_transaction_id'] = $type_transaction_id;
                $params['journal_id'] = $journal_id;
                $params['credit_note'] = $credit_note;
                $params['relation_id'] = $id;
                $params['created_by'] = auth()->user()->id;
                $params['code'] = $claim->code;
                $params['date_transaction'] = Carbon::now()->format('Y-m-d');
                $params['created_at'] = Carbon::now()->format('Y-m-d');
                $supplier = DB::table('contacts')
                                ->whereId($claim->collectible_id)
                                ->select('term_of_payment')
                                ->first();
                $tempo = $supplier->term_of_payment ?? 1;
                $params['date_tempo'] = Carbon::now()->addDays($tempo)->format('Y-m-d');
                $params['credit'] = $hutang;
                $params['debet'] = 0;
                $params['description'] = 'Klaim - ' . $claim->code;
                $payable_id = DB::table('payables')
                                ->insertGetId($params);
                $params = [];
                $claimDetails  = DB::table('claim_details')
                                ->join('commodities', 'commodities.id', 'claim_details.commodity_id')
                                ->whereHeaderId($id)
                                ->select('commodities.name AS commodity_name', 'claim_details.total_price', 'claim_details.claim_total_price')
                                ->get();
                foreach ($claimDetails as $item) {
                    $detail = [];
                    $detail['header_id'] = $payable_id;
                    $detail['type_transaction_id'] = $type_transaction_id;
                    $detail['journal_id'] = $journal_id;
                    $detail['created_at'] = Carbon::now()->format('Y-m-d');
                    $detail['description'] = 'Klaim - ' . $claim->code . ' - atas barang ' . $item->commodity_name;
                    $detail['code'] = $claim->code;
                    $detail['debet'] = 0;
                    $detail['credit'] = $item->total_price;
                    $params[] = $detail;
                }
                DB::table('payable_details')
                    ->insert($params);
                DB::table('claims')
                    ->whereId($id)
                    ->update([
                        'payable_id' => $payable_id
                    ]);
                // Generate piutang
                $codex = new TransactionCode($claim->company_id, 'debetNote');
                $codex->setCode();
                $debet_note = $codex->getCode();
                $params = [];
                $params['company_id'] = $claim->company_id;
                $params['contact_id'] = $granted;
                $params['type_transaction_id'] = $type_transaction_id;
                $params['journal_id'] = $journal_id;
                $params['debet_note'] = $debet_note;
                $params['relation_id'] = $id;
                $params['created_by'] = auth()->user()->id;
                $params['code'] = $claim->code;
                $params['date_transaction'] = Carbon::now()->format('Y-m-d');
                $params['created_at'] = Carbon::now()->format('Y-m-d');
                $supplier = DB::table('contacts')
                            ->whereId($granted)
                            ->select('term_of_payment')
                            ->first();
                $tempo = $supplier->term_of_payment ?? 1;
                $params['date_tempo'] = Carbon::now()->addDays($tempo)->format('Y-m-d');
                $params['debet'] = $hutang;
                $params['credit'] = 0;
                $params['description'] = 'Klaim - ' . $claim->code;
                $receivable_id = DB::table('receivables')
                                    ->insertGetId($params);
                $params = [];
                foreach ($claimDetails as $item) {
                    $detail = [];
                    $detail['header_id'] = $receivable_id;
                    $detail['type_transaction_id'] = $type_transaction_id;
                    $detail['journal_id'] = $journal_id;
                    $detail['created_at'] = Carbon::now()->format('Y-m-d');
                    $detail['description'] = 'Klaim - ' . $claim->code . ' - atas barang ' . $item->commodity_name;
                    $detail['code'] = $claim->code;
                    $detail['credit'] = 0;
                    $detail['debet'] = $item->claim_total_price;
                    $params[] = $detail;
                }
                DB::table('receivable_details')
                    ->insert($params);
                DB::table('claims')
                    ->whereId($id)
                    ->update([
                        'receivable_id' => $receivable_id
                    ]);
            }
        }
        return $journal_id;
    }
    /*
      Date : 29-08-2020
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validateWasPosted($id);
        self::validateWasApproved($id);
        self::validateMCinvoiced($id);
        $dt = self::show($id);
        Closing::preventByDate($dt->date_transaction);
        DB::table(self::$table)->whereId($id)->delete();
    }
    public static function validateMCinvoiced($id) { //validasi is_invoice=>manifest_cost.journal_cost_manifest
        $joc = DB::table('manifest_costs')->where('journal_realisasi', $id)->first();
        if(!empty($joc)){
            if(empty($joc->journal_cost_manifest)){// belom di invoice maka boleh update kembali ke status 1 draft manifest
                DB::table('manifest_costs')
                ->where('journal_realisasi',$id)
                ->update(['status' =>1,'journal_realisasi'=>NULL]);
                JobOrderCost::where('manifest_cost_id',$joc->id)->update([
                    'status'=>1
                ]);
            }else{
                throw new Exception('Manifest Cost Invoiced');
            }
        }
    }
}
