<?php

namespace App\Http\Controllers\Api\v4\Operational;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\CustomerJobOrder;
use App\Model\CustomerJobOrderDetail;
use App\Model\CustomerJobOrderDetailSerial;
use App\Model\DepoContainer;
use App\Model\Dock;
use App\Model\JobOrder;
use App\Model\Piece;
use App\Model\PriceList;
use App\Model\QuotationDetail;
use App\Model\Service;
use App\Model\WorkOrder;
use Exception;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use App\Http\Controllers\Operational\JobOrderController;


class CustomerOrderApiController extends Controller
{
    public function getDataTable()
    {
        $item = CustomerJobOrder::all();

        return DataTables::of($item)->make(true);
    }

    public function getDataTableCustomer($id)
    {
        $item = CustomerJobOrder::where('customer_id', $id);

        return DataTables::of($item)->make(true);
    }

    public function store(Request $request)
    {
        $request->validate([
          'company_id' => 'required',
          'create_by' => 'required',
          'customer_id' => 'required',
          'code' => 'required',
          'sender_id' => 'required',
          'receiver_id' => 'required',
          'shipment_date' => 'required',
        ]);

        $wo = DB::table('work_order_details')
        ->where('id', $request->work_order_detail_id)
        ->first();

        if($wo != null){
          if($wo->price_list_id){
            $type_tarif = 2;
          } else {
            $type_tarif = 1;
          }
        } else {
          if($request->price_list_id){
            $type_tarif = 2;
          } else {
            $type_tarif = 1;
          }
        }


        DB::beginTransaction();
            $custJO = CustomerJobOrder::create([
              "type_tarif" => $type_tarif,
              "company_id" => $request->company_id,
              "create_by" => $request->create_by,
              "customer_id" => $request->customer_id,
              "code" => $request->code,
              "sender_id" => $request->sender_id,
              "receiver_id" => $request->receiver_id,
              "description" => $request->description,
              'shipment_date' => dateDB($request->shipment_date),
              "route_id" => $request->route_id,
              "transaction_type_id" => $request->transaction_type_id,
              "service_type_id" => $request->service_type_id,
              "service_id" => $request->service_id,
              "collectible_id" => $request->collectible_id,
              "vehicle_type_id" => $request->vehicle_type_id,
              "commodity_id" => $request->commodity_id,
              "kpi_id" => $request->kpi_id,
              "price" => 0,
              "total_price" => 0,
              "status" => 1,
              "total_unit" => $request->total_unit,
              "total_item" => 0,
              "no_po_customer" => $request->wo_customer,
              "no_bl" => $request->no_bl,
              "moda_id" => $request->moda_id,
              "container_type_id" => $request->container_type_id,
              "reff_no" => $request->reff_no,
              "docs_reff_no" => $request->docs_reff_no,
              "piece_name" => $request->piece_name,
              "piece_id" => $request->piece_id,
              "aju_number" => $request->aju_number,
              "warehouse_id" => $request->warehouse_id,
              "warehouse_staff_id" => $request->warehouse_staff_id,
              "imposition" => $request->imposition,
              "is_operational_done" => 0,
              "vessel_name" => $request->vessel_name,
              "voyage_no" => $request->voyage_no,
              "document_name" => $request->document_name,
              "is_handling" => $request->is_handling ?? 0,
              "is_warehouse" => $request->is_warehouse ?? 0,
              "price_list_id" => $request->price_list_id,
              "agent_name" => $request->agent_name,
              "awb_number" => $request->awb_number,
              "flight_code" => $request->flight_code,
              "flight_route" => $request->flight_route,
              "flight_date" => $request->flight_date,
              "cargo_ready_date" => $request->cargo_ready_date,
              "house_awb" => $request->house_awb,
              "hs_code" => $request->hs_code,
              "sales_order_code" => $request->sales_order_code,
              "depo_awal_id" => $request->depo_awal_id,
              "depo_tujuan_id" => $request->depo_tujuan_id,
              "dock_awal_id" => $request->dock_awal_id,
              'uniqid' => str_random(100),
              "dock_tujuan_id" => $request->dock_tujuan_id,
              'start_window_time' => $request->start_window_date && $request->start_window_time ? createTimestamp($request->start_window_date, $request->start_window_time) : null,
              'end_window_time' => $request->end_window_date && $request->end_window_time ? createTimestamp($request->end_window_date,$request->end_window_time) : null,
            ]);

            $total_item=0;
            foreach ($request->detail as $valueDetail) {
              $detailData = CustomerJobOrderDetail::create([
                "header_id"     =>  $custJO->id,
                "item_name"     =>  $valueDetail['item_name'] ?? null,
                "item_id"       =>  $valueDetail['item_id'] ?? null,
                "piece_id"      =>  $valueDetail['piece_id'] ?? null,
                "sender_id"     =>  $request->sender_id,
                "create_by"     =>  $request->create_by,
                "receiver_id"   =>  $request->receiver_id,
                "qty"           =>  $valueDetail['total_item'] ?? 0,
                "weight"        =>  $valueDetail['total_tonase'] ?? 0,
                "volume"        =>  $valueDetail['total_volume'] ?? 0,
                "long"          =>  $valueDetail['length'] ?? 0,
                "wide"          =>  $valueDetail['width'] ?? 0,
                "high"          =>  $valueDetail['height'] ?? 0,
                "picking_detail_id" =>  $valueDetail['picking_detail_id'] ?? null,
                "commodity_id" =>  $valueDetail['commodity_id'] ?? null,
                "price" =>  0,
                "total_price" =>  0,
                "transported" =>  0,
                "leftover" =>  $valueDetail['total_item'] ?? null,
                "imposition" =>  $valueDetail['imposition'] ?? null,
                "v1" =>  $valueDetail['v1'] ?? 0,
                "tebal_v1" =>  $valueDetail['tebal_v1'] ?? 0,
                "v2" =>  $valueDetail['v2'] ?? 0,
                "tebal_v2" =>  $valueDetail['tebal_v2'] ?? 0,
                "actual_weight" =>  $valueDetail['actual_weight'] ?? 0,
                "no_reff" =>  $valueDetail['reff_no'] ?? null,
                "no_manifest" =>  $valueDetail['manifest_no'] ?? null,
                "description" =>  $valueDetail['description'] ?? null,
                "receive_date" =>  $valueDetail['receive_date'] ?? null,
                "weight_type" =>  $valueDetail['weight_type'] ?? null,
                "volumetric_weight" =>  $valueDetail['volumetric_weight'] ?? null,
                "load_date" =>  $valueDetail['load_date'] ?? null,
              ]);
              $total_item++;
              // dd($valueDetail);
              // foreach ($valueDetail['detail'] as $value) {
              //     $newDetail_serial = new CustomerJobOrderDetailSerial;

              //     $newDetail_serial->detail_id     =  $detailData->id;
              //     $newDetail_serial->item_id     =  $value['item_id'];

              //     if (isset($value['serial_no'])) {
              //       $newDetail_serial->serial_no     =  $value['serial_no'];
              //     }
              //     if (isset($value['order_number'])) {
              //       $newDetail_serial->order_number  =  $value['order_number'];
              //     }
              //     if (isset($value['batch'])) {
              //       $newDetail_serial->batch         =  $value['batch'];
              //     }
              //     $newDetail_serial->save();
              // }  

            }

          CustomerJobOrder::find($custJO->id)
          ->update([
            'total_item' => $total_item,
          ]);

        DB::commit();
        
        $data['message'] = "Data Berhasil Dibuat";
        $data['status'] = 200;
 
        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function update(Request $request, $id)
    {
        // dd($request);
        DB::beginTransaction();
        $findData = CustomerJobOrder::find($id);
        $findData->customer_id = $request->customer_id;
        $findData->code = $request->code;
        $findData->sender_id = $request->sender_id;
        $findData->receiver_id = $request->receiver_id;
        $findData->description = $request->description;
        $findData->shipment_date = dateDB($request->shipment_date);
        $findData->update();

        // foreach ($request->serial_detail as $value) {
        //   $findSerialDetail = CustomerJobOrderDetailSerial::find($value['id']);

        //   if ($findSerialDetail) {
        //     $findSerialDetail->serial_no     =  $value['serial_no'];
        //     $findSerialDetail->order_number  =  $value['order_number'];
        //     $findSerialDetail->batch         =  $value['batch'];
        //     $findSerialDetail->update();
        //   }
        // }  

          $total_item=0;
          if(count($request->detail) > 0){
            foreach ($request->detail as $valueDetail) {
              $detailData = CustomerJobOrderDetail::create([
                "header_id"     =>  $id,
                "item_name"     =>  $valueDetail['item_name'] ?? null,
                "item_id"       =>  $valueDetail['item_id'] ?? null,
                "piece_id"      =>  $valueDetail['piece_id'] ?? null,
                "sender_id"     =>  $request->sender_id,
                "create_by"     =>  $request->create_by,
                "receiver_id"   =>  $request->receiver_id,
                "qty"           =>  $valueDetail['total_item'] ?? 0,
                "weight"        =>  $valueDetail['total_tonase'] ?? 0,
                "volume"        =>  $valueDetail['total_volume'] ?? 0,
                "long"          =>  $valueDetail['length'] ?? 0,
                "wide"          =>  $valueDetail['width'] ?? 0,
                "high"          =>  $valueDetail['height'] ?? 0,
                "picking_detail_id" =>  $valueDetail['picking_detail_id'] ?? null,
                "commodity_id" =>  $valueDetail['commodity_id'] ?? null,
                "price" =>  0,
                "total_price" =>  0,
                "transported" =>  0,
                "leftover" =>  $valueDetail['total_item'] ?? null,
                "imposition" =>  $valueDetail['imposition'] ?? null,
                "v1" =>  $valueDetail['v1'] ?? 0,
                "tebal_v1" =>  $valueDetail['tebal_v1'] ?? 0,
                "v2" =>  $valueDetail['v2'] ?? 0,
                "tebal_v2" =>  $valueDetail['tebal_v2'] ?? 0,
                "actual_weight" =>  $valueDetail['actual_weight'] ?? 0,
                "no_reff" =>  $valueDetail['reff_no'] ?? null,
                "no_manifest" =>  $valueDetail['manifest_no'] ?? null,
                "description" =>  $valueDetail['description'] ?? null,
                "receive_date" =>  $valueDetail['receive_date'] ?? null,
                "weight_type" =>  $valueDetail['weight_type'] ?? null,
                "volumetric_weight" =>  $valueDetail['volumetric_weight'] ?? null,
                "load_date" =>  $valueDetail['load_date'] ?? null,
              ]);
              $total_item++;
            }
          }

          $count_item = CustomerJobOrderDetail::whereHeaderId($id)->count();
          $grand_total_item = (int)$total_item + (int)$count_item;

          CustomerJobOrder::find($id)
          ->update([
            'total_item' => $grand_total_item,
          ]);
       
        DB::commit();

        $data['message'] = "Data Berhasil diupdate";
        $data['status'] = 200;
        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function getDataDetail($id)
    {
        $item = CustomerJobOrder::with('detail','customer:id,name','sender:id,name','receiver:id,name','service:id,name','service_type:id,name', 'trayek:id,code,name')
        ->leftJoin('job_orders', 'job_orders.customer_job_order_id', 'customer_job_orders.id')
        ->leftJoin('price_lists', 'price_lists.id', 'customer_job_orders.price_list_id')
        ->leftJoin('quotation_details', 'quotation_details.id', 'customer_job_orders.quotation_detail_id')
        ->select('customer_job_orders.*', 'price_lists.name as price_list_name', 'quotation_details.name as quotation_detail_name', 'job_orders.code as jo_code')
        ->where('customer_job_orders.id', $id)->first();

        return response()->json($item, 200, [], JSON_NUMERIC_CHECK);
    }

    public function destroy($id)
    {
        DB::beginTransaction();
          $detail = CustomerJobOrderDetail::where('header_id', $id)->get();

          CustomerJobOrderDetail::where('header_id', $id)->delete();

          foreach ($detail as $value) {
            CustomerJobOrderDetailSerial::where('detail_id', $value->id)->delete();
          }

          CustomerJobOrder::where('id', $id)->delete();
        DB::commit();
        $data['message'] = "Data Berhasil Dihapus";
        $data['status'] = 200;
        return response()->json($data);
    }

    public function deleteDetail($id)
    {
        DB::beginTransaction();
          CustomerJobOrderDetail::where('id', $id)->delete();
          CustomerJobOrderDetailSerial::where('detail_id', $id)->delete();
        DB::commit();
        $data['message'] = "Data Berhasil Dihapus";
        $data['status'] = 200;
        return response()->json($data);
    }

    public function getPieces()
    {
        $item = Piece::all();

        return response()->json($item, 200, [], JSON_NUMERIC_CHECK);
    }

    public function reject($id){
      try {
        DB::beginTransaction();
        CustomerJobOrder::find($id)->update([
          'status' => 3
        ]);
        DB::commit();
        $data['message'] = 'Data Berhasil Disimpan!';
        $http_code = 200;
      } catch (Exception $e) {
        DB::rollBack();
        $data['message'] = $e->getMessage();
        $http_code = $e->getCode() == 422 ? 422 : 421;
      }
      return response()->json($data,$http_code);

    }

    public function approve($id){
      try {
        DB::beginTransaction();
        $cjo = DB::table('customer_job_orders')->where('id', $id)->first();
        $cjod = DB::table('customer_job_order_details')->where('header_id', $id)->get();
        // dd($cjo, $cjod);
        $request = new Request();

        $request->merge([
          "company_id" => (int)$cjo->company_id,
          "create_by" => (int)$cjo->create_by,
          "customer_id" => (int)$cjo->customer_id,
          "code" => $cjo->code,
          "sender_id" => (int)$cjo->sender_id,
          "receiver_id" => (int)$cjo->receiver_id,
          "description" => $cjo->description,
          'shipment_date' => dateDB($cjo->shipment_date),
          "route_id" => (int)$cjo->route_id,
          "transaction_type_id" => $cjo->transaction_type_id,
          "service_type_id" => (int)$cjo->service_type_id,
          "service_id" => (int)$cjo->service_id,
          "collectible_id" => (int)$cjo->collectible_id,
          "vehicle_type_id" => (int)$cjo->vehicle_type_id,
          "commodity_id" => (int)$cjo->commodity_id,
          // "kpi_id" => (int)$cjo->kpi_id,
          "price" => 0,
          "total_price" => 0,
          "status" => 1,
          "total_unit" => (int)$cjo->total_unit,
          "total_item" => 0,
          "wo_customer" => $cjo->code,
          "no_bl" => $cjo->no_bl,
          "moda_id" => (int)$cjo->moda_id,
          "container_type_id" => (int)$cjo->container_type_id,
          "reff_no" => $cjo->reff_no,
          "docs_reff_no" => $cjo->docs_reff_no,
          "piece_name" => $cjo->piece_name,
          "piece_id" => $cjo->piece_id,
          "aju_number" => $cjo->aju_number,
          "warehouse_id" => (int)$cjo->warehouse_id,
          "warehouse_staff_id" => (int)$cjo->warehouse_staff_id,
          "imposition" => (int)$cjo->imposition,
          "is_operational_done" => 0,
          "vessel_name" => $cjo->vessel_name,
          "voyage_no" => $cjo->voyage_no,
          "document_name" => $cjo->document_name,
          "is_handling" => $cjo->is_handling ?? 0,
          "is_warehouse" => $cjo->is_warehouse ?? 0,
          "price_list_id" => (int)$cjo->price_list_id,
          "agent_name" => $cjo->agent_name,
          "awb_number" => $cjo->awb_number,
          "flight_code" => $cjo->flight_code,
          "flight_route" => $cjo->flight_route,
          "flight_date" => $cjo->flight_date,
          "cargo_ready_date" => $cjo->cargo_ready_date,
          "house_awb" => $cjo->house_awb,
          "hs_code" => $cjo->hs_code,
          "sales_order_code" => $cjo->sales_order_code,
          "depo_awal_id" => (int)$cjo->depo_awal_id,
          "depo_tujuan_id" => (int)$cjo->depo_tujuan_id,
          "dock_awal_id" => (int)$cjo->dock_awal_id,
          'uniqid' => $cjo->uniqid,
          "dock_tujuan_id" => (int)$cjo->dock_tujuan_id,
          "type_tarif" => $cjo->type_tarif,
          'customer_job_order_id' => $id,
          'work_order_id' => (int)$cjo->work_order_id,
          'work_order_detail_id' => (int)$cjo->work_order_detail_id == 0 ? -1 : (int)$cjo->work_order_detail_id
        ]);

        $cjodDetails = [];
        foreach ($cjod as $detail) {
            $cjodDetails[] = [
              // "header_id"     =>  $id,
              "item_name"     =>  $detail->item_name ?? null,
              "item_id"       =>  (int)$detail->item_id ?? null,
              "piece_id"      =>  $detail->piece_id ?? null,
              "sender_id"     =>  (int)$detail->sender_id,
              "create_by"     =>  (int)$detail->create_by,
              "receiver_id"   =>  (int)$detail->receiver_id,
              "total_item"    =>  $detail->qty ?? 0,
              "total_tonase"        =>  $detail->weight ?? 0,
              "total_volume"        =>  $detail->volume ?? 0,
              "long"          =>  $detail->long ?? 0,
              "wide"          =>  $detail->wide ?? 0,
              "high"          =>  $detail->high ?? 0,
              "picking_detail_id" =>  (int)$detail->picking_detail_id ?? null,
              "commodity_id" =>  (int)$detail->commodity_id ?? null,
              "transported" =>  0,
              "leftover" =>  $detail->qty ?? null,
              "imposition" =>  $detail->imposition ?? null,
              "v1" =>  $detail->v1 ?? 0,
              "tebal_v1" =>  $detail->tebal_v1 ?? 0,
              "v2" =>  $detail->v2 ?? 0,
              "tebal_v2" =>  $detail->tebal_v2 ?? 0,
              "actual_weight" =>  $detail->actual_weight ?? 0,
              "reff_no" =>  $detail->no_reff ?? null,
              "manifest_no" =>  $detail->no_manifest ?? null,
              "description" =>  $detail->description ?? null,
              "receive_date" =>  $detail->receive_date ?? null,
              "weight_type" =>  $detail->weight_type ?? null,
              "volumetric_weight" =>  $detail->volumetric_weight ?? null,
              "load_date" =>  $detail->load_date ?? null,
            ];
        }

        $request->merge(['detail' => $cjodDetails]);

        $jobOrderController = new JobOrderController();
        $jobOrderController->store($request);

        DB::commit();
        $data['message'] = 'Data Berhasil Disimpan!';
        $http_code = 200;
      } catch (Exception $e) {
        DB::rollBack();
        $data['message'] = $e->getMessage();
        $http_code = $e->getCode() == 422 ? 422 : 421;
      }
      return response()->json($data,$http_code);

    }

    public function getAllSetting()
    {
        $data['services'] = DB::table('services')
        ->select('id', 'name', 'service_type_id')
        ->get();

        $data['company'] = DB::table('companies')
        ->select('id', 'name')
        ->get();

        $data['moda'] = DB::table('modas')
        ->select('id', 'name')
        ->get();

        $data['routes'] = DB::table('routes')
        ->select('id', 'name', 'code')
        ->get();

        $data['commodity'] = DB::table('commodities')
        ->select('id', 'name')
        ->get();

        $data['container_type'] = DB::table('container_types')
        ->select('id', 'name')
        ->get();
        
        $data['pieces'] = DB::table('pieces')
        ->select('id', 'name')
        ->get();
        
        $data['vehicle_type'] = DB::table('vehicle_types')
        ->select('id', 'name')
        ->get();

        $data['depo'] = DepoContainer::select('id', 'name')->where('is_active', 1)->orderBy('id')->get();

        $data['dock'] = Dock::select('id', 'name')->where('is_active', 1)->orderBy('id')->get();

        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function cari_wo(Request $request, $id)
    {
      // dd($request);
      if ($request->type_tarif==1) {
        $quot=QuotationDetail::find($request->quotation_detail_id);
        $data['wo']=WorkOrder::whereRaw("customer_id = $id AND quotation_id = $quot->header_id")->get();
      } else {
        $data['wo']=WorkOrder::where('customer_id', $id)->where('quotation_id',null)->select('id','code')->get();
      }
      return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function cari_price_list($id)
    {
      $data=PriceList::find($id);
      return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function item_datatable(Request $request)
    {
        $wr="1=1";
        if ($request->category_id) {
          $wr.=" and items.category_id = $request->category_id";
        }
        if ($request->start_price) {
          $wr.=" and items.harga_beli >= $request->start_price";
        }
        if ($request->end_price) {
          $wr.=" and items.harga_beli <= $request->end_price";
        }
        $wr2 = '';
        if($request->filled('warehouse_id')) {
            $wr2 .= 'AND warehouse_id = ' . $request->warehouse_id;
        }
        $stockTransaction = DB::raw("(SELECT SUM(qty_masuk - qty_keluar) AS qty, item_id FROM stock_transactions WHERE warehouse_receipt_detail_id IS NULL $wr2 GROUP BY item_id) AS stock_transactions");
        $item=DB::table('items')
        ->leftJoin('categories','categories.id','items.category_id')
        ->leftJoin('contacts','contacts.id','items.customer_id')
        ->leftJoin('categories as parents','parents.id','categories.parent_id')
        ->leftJoin($stockTransaction, 'stock_transactions.item_id', 'items.id')
        ->whereRaw($wr)
        // ->whereRaw('customer_id is null')
        ->selectRaw('
          items.id,
          items.code,
          items.name,
          items.part_number,
          items.harga_beli,
          items.description,
          contacts.id as customer_id,
          contacts.name as customer_name,
          COALESCE(stock_transactions.qty, 0) AS qty,
          categories.name as category,
          parents.name as parent
        ');
        if($request->default_rack_id) {
            $item = $item->where('items.default_rack_id', $request->default_rack_id);
        }
        $quotation_id = null;
        if($request->show_quotation_price) {
            if($request->quotation_id) {
                $quotation_id = $request->quotation_id;
                $quotation_id = DB::table('quotations')->select(DB::raw("ifnull(parent_id,$quotation_id) as quotation_id"))->where('id',$quotation_id)->first()->quotation_id;
                $quotation_items = DB::raw("(SELECT item_id, price FROM quotation_items WHERE quotation_id = $quotation_id GROUP BY item_id) AS quotation_items");
                $item->join($quotation_items, 'quotation_items.item_id', 'items.id');
            }
        }
        if(!$quotation_id) {
            $item->addSelect(['items.harga_jual']);
            if($request->harga_jual_greater_than && is_int((int)$request->harga_jual_greater_than)) {
                $item->where('items.harga_jual', '>=', $request->harga_jual_greater_than);
            }
        } else {
            $item->addSelect([DB::raw('COALESCE(quotation_items.price, items.harga_jual) AS harga_jual')]);
            if($request->harga_jual_greater_than && is_int((int)$request->harga_jual_greater_than)) {
                $item->where(DB::raw('COALESCE(quotation_items.price, items.harga_jual)'), '>=', $request->harga_jual_greater_than);
            }
        }
        if($request->is_container_part == 1) {
            $item = $item->where(function($query){
                $query->where('parents.is_container_part', 1);
                $query->orWhere('categories.is_container_part', 1);
            });
        }
        if($request->is_merchandise == 1) {
            $item->where('items.is_merchandise', 1);
        }
        if($request->is_service == 1) {
          $item->where(function($query){
            $query->where('categories.is_jasa', 1);
            $query->where('items.is_service', 1);
            $query->where('items.is_stock', 1);
        });
        }
        if($request->customer_id != 0){
          $itemdata=Quotation::where('customer_id',$request->customer_id)->get();
          $idQuotation= $itemdata->pluck('id')->toArray();
          $quotationitem= QuotationItem::whereIn('quotation_id',$idQuotation)->get();
          $itemID= $quotationitem->pluck('item_id')->toArray();
         $item->whereIn('items.id',$itemID);
        }
        if($request->is_container_yard == 1) {
            $item = $item->where(function($query){
                $query->where('parents.is_container_yard', 1);
                $query->orWhere('categories.is_container_yard', 1);
            });
        }
        if($request->is_pallet == 1) {
            $item = $item->where(function($query){
                $query->where('parents.is_pallet', 1);
                $query->orWhere('categories.is_pallet', 1);
            });
        }
        if($request->filled('isTire')) {
          $item = $item->where(function($query){
            $query->where('categories.is_tire', 1);
          });
        }
        if($request->filled('isBbm')) {
          $item = $item->where(function($query){
            $query->where('categories.is_bbm', 1);
          });
        }
        if($request->filled('is_outer_tire') && $request->is_outer_tire == 1) {
          $item = $item->where(function($query){
            $query->orWhere('categories.is_ban_luar', 1);
          });
        }
        if($request->filled('is_inner_tire') && $request->is_inner_tire == 1) {
          $item = $item->where(function($query){
            $query->orWhere('categories.is_ban_dalam', 1);
          });
        }
        if($request->filled('is_marset_tire') && $request->is_marset_tire == 1) {
          $item = $item->where(function($query){
            $query->orWhere('categories.is_marset', 1);
          });
        }
        // print_r($sql);
        return DataTables::of($item)->make(true);
    }

    public function general_index($slug = '') {
      $settings = DB::table('settings');
      if($slug) {
          $settings->whereSlug($slug);
      }
      $settings = $settings->get()->map(function($e){
          $e->content = json_decode($e->content);
          return $e;
      });
      return response()->json($settings);
    }

    public function show_pieces($id){
        $data = Piece::find($id);

        return response()->json($data);
    }

    public function get_price($service_type_id){
        $data = PriceList::whereServiceTypeId($service_type_id)
        ->select('id' ,'name')->get();

        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }

}
