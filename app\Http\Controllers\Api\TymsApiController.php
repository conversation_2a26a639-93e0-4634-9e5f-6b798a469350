<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\TireOnOff;
use App\Model\TireType;
use App\Model\Storing;
use App\Model\VehicleTire;
use App\Model\VulkanisirRequest;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

class TymsApiController extends Controller
{
    public function tire_type_datatable(Request $request)
    {
        $item = TireType::all();

        return DataTables::of($item)
          ->make(true);
    }

    public function vehicle_tire_single(Request $request, $id)
    {
        $item = VehicleTire::find($id);
        
        return response()->json($item);
    } 

    public function lepas_pasang_ban_datatable(Request $request)
    {
        // dd($request->all());
        $item = TireOnOff::join('companies', 'companies.id', 'tire_on_offs.company_id')
                ->leftJoin('tire_on_off_categories', 'tire_on_off_categories.id', 'tire_on_offs.tire_on_off_category_id');
        if($request->start_date){
          $item->whereDate('tire_on_offs.request_date','>=',dateDB($request->start_date));
        }
        if($request->end_date){
          $item->whereDate('tire_on_offs.request_date','<=',dateDB($request->end_date));
        }
        if($request->approve_start_date){
          $item->whereDate('tire_on_offs.approve_date','>=',dateDB($request->approve_start_date));
        }
        if($request->approve_end_date){
          $item->whereDate('tire_on_offs.approve_date','<=',dateDB($request->approve_end_date));
        }
        $item->select('tire_on_offs.*', 'companies.name as company_name', 'tire_on_off_categories.name as type_name');

        return DataTables::of($item)
            ->editColumn('status', function($item){
                $status = '-';
                if($item->status == 1){
                    $status = 'Draft';
                } else if($item->status == 2){
                    $status = 'Approved';
                } else if($item->status == 0){
                    $status = 'Rejected';
                }
                return $status;
            })
            ->make(true);
    }

    public function vulkanisir_request_datatable(Request $request)
    {
        $item = VulkanisirRequest::join('companies', 'companies.id', 'vulkanisir_requests.company_id')
                ->leftJoin('contacts as vendor', 'vendor.id', 'vulkanisir_requests.vendor_id')
                ->select('vulkanisir_requests.*', 'companies.name as company_name', 'vendor.name as vendor_name');

        if($request->is_approved && $request->is_approved == 1){
            $item = $item->where('vulkanisir_requests.vulkanisir_status', 2);
        }

        return DataTables::of($item)
            ->editColumn('status', function($item){
                $status = '-';
                if($item->vulkanisir_status == 1){
                    $status = 'Draft';
                } else if($item->vulkanisir_status == 2){
                    $status = 'Approved';
                } else if($item->vulkanisir_status == 0){
                    $status = 'Rejected';
                } else if($item->vulkanisir_status == 3){
                    $status = 'Receipt Finished';
                }
                return $status;
            })
            ->make(true);
    }

    public function storing_datatable(Request $request){

        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) { 
            {
            $wr .= " AND storings.company_id = " . auth()->user()->company_id;
            }
        }
    
        if ($request->company_id) {
            $wr .= " AND storings.company_id = $request->company_id";
        }
    
        if ($request->vehicle_id) {
            $wr .= " AND storings.vehicle_id = $request->vehicle_id";
        }
    
        if ($request->date_check) {
            $req = Carbon::parse($request->date_check)->format('Y-m-d');
            $wr .= " AND storings.date_check = '$req'";
        }
    
    // dd($wr);
    
    
          $item = Storing::with('vehicle:id,nopol','company:id,name')->whereRaw($wr)->select('storings.*');
          return DataTables::of($item)
          ->addColumn('action', function($item){
              $html="<a ng-click=\"show($item->id)\"><i class='fa fa-folder-o'></i></a>&nbsp&nbsp";
              $html.="<a ui-sref=\"tyms.storing.show.edit({id:$item->id})\"><i class='fa fa-edit'></i></a>&nbsp ";
              $html.="<a ng-click=\"delete($item->id)\"><i class='fa fa-trash' data-toggle='tooltip' title='Hapus Data'></i></a>";
              return $html;
          })
          ->rawColumns(['action'])
          ->make(true);
        }
}
