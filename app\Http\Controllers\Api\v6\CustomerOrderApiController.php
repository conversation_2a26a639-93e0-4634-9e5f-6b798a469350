<?php

namespace App\Http\Controllers\Api\v4\Operational;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\CustomerJobOrder;
use App\Model\CustomerJobOrderDetail;
use App\Model\CustomerJobOrderDetailSerial;
use App\Model\Piece;
use Exception;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

class CustomerOrderApiController extends Controller
{

    public function getDataDetail($id)
    {
        $item = CustomerJobOrder::with('detail','customer:id,name','sender:id,name','receiver:id,name')->where('id', $id)->first();

        return response()->json($item, 200, [], JSON_NUMERIC_CHECK);
    }

}
