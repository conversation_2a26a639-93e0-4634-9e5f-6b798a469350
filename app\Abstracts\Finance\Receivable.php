<?php

namespace App\Abstracts\Finance;

use App\Model\Invoice;
use App\Model\SalesOrder;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class Receivable
{
    protected static $table = 'receivables';

    /*
      Date : 22-06-2021
      Description : Menangkap parameter untuk filter data
      Developer : Didin
      Status : Create
    */
    public static function fetchFilter($args = []) {
        $params = [];
        $params["status"] = $args["status"] ?? null;
        $params["customer_id"] = $args["customer_id"] ?? null;
        $params["company_id"] = $args["company_id"] ?? null;
        $params["start_date_invoice"] = $args["start_date_invoice"] ?? null;
        $params["end_date_invoice"] = $args["end_date_invoice"] ?? null;
        $params["start_due_date"] = $args["start_due_date"] ?? null;
        $params["end_due_date"] = $args["end_due_date"] ?? null;
        $params["year_date"] = $args["year_date"] ?? null;

        return $params;
    }

    /*
      Date : 05-03-2021
      Description : Menangkap parameter input filter data
      Developer : Didin
      Status : Create
    */
    public static function query($request = []) {
        $request = self::fetchFilter($request);

        // $wr="(receivables.type_transaction_id = 26 OR receivables.type_transaction_id = 2) ";
        $wr = "1=1";
        if ($request['customer_id']) {
            $wr.=" and receivables.contact_id = " . $request['customer_id'];
        }
        if ($request['company_id']) {
            $wr.=" and receivables.company_id = " . $request['company_id'];
        }

        $sqlDb = DB::raw('(select group_concat(distinct aju_number) as aju, group_concat(distinct no_bl) as no_bl, inv.id as invoice_id from job_orders jo
            join invoice_job_order ijo ON jo.id = ijo.job_order_id
            JOIN invoices inv ON inv.id = ijo.invoice_id
            group by inv.id) as i');

        $item=DB::table('receivables')
        ->leftJoin('invoices','invoices.id','receivables.relation_id')
        ->leftJoin($sqlDb, 'i.invoice_id','invoices.id')
        ->leftJoin('contacts','contacts.id','receivables.contact_id')
        ->leftJoin('bill_details','bill_details.receivable_id','receivables.id')
        ->whereRaw($wr)
        ->selectRaw('
            receivables.*,
            invoices.code as code_invoice,
            invoices.id as invoice_id,
            i.aju,
            i.no_bl,
            receivables.debet as total_receivable,
            receivables.credit as total_paid,
            (receivables.debet-receivables.credit) as sisa,
            contacts.name as customer,
            datediff(date(now()),receivables.date_tempo) as umur,
            if(receivables.is_temporary=1,0,
                if(receivables.debet-receivables.credit=0,1,
                    if(datediff(date(now()),receivables.date_tempo)>0 && bill_details.id is null,
                        2,3
                    ) 
                ) 
            ) as status_piutang
            ')->groupBy('receivables.id');

        if ($request['start_date_invoice']) {
            $start=Carbon::parse($request['start_date_invoice'])->format('Y-m-d');
            $item = $item->where("receivables.date_transaction" , ">=", $start);
        }

        if ($request['end_date_invoice']) {
            $end=Carbon::parse($request['end_date_invoice'])->format('Y-m-d');
            $item = $item->where("receivables.date_transaction" , "<=", $end);
        }

        if ($request['year_date']) {
            $year = Carbon::parse($request['year_date'])->year;
            $item = $item->whereYear("receivables.date_transaction", '=' , $year);
        }

        if ($request['start_due_date']) {
            $start=Carbon::parse($request['start_due_date'])->format('Y-m-d');
            $item = $item->where("receivables.date_tempo" , ">=", $start);
        }

        if ($request['end_due_date']) {
            $end=Carbon::parse($request['end_due_date'])->format('Y-m-d');
            $item = $item->where("receivables.date_tempo" , "<=", $end);
        }

        if(isset($request['status'])){
            $item->havingRaw("status_piutang = {$request['status']}");
        }

        return $item;
    }

    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();

        if(!$dt) {
            throw new Exception('Data not found');
        }
    }

    /*
      Date : 29-08-2021
      Description : Menampilkan detail kategori barang
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);

        $dt = DB::table(self::$table);
        $dt = $dt->whereId($id);
        $dt = $dt->first();

        return $dt;
    }

    /*
      Date : 14-03-2021
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    // public static function destroy($id) {
    //     self::validate($id);
    //     PayableDetail::clear($id);
    //     DB::table(self::$table)
    //     ->whereId($id)
    //     ->delete();
    // }

    /*
      Date : 14-03-2021
      Description : Mendapatkan total sisa piutang
      Developer : Didin
      Status : Create
    */
    /*
      Date : 30-08-2021
      Description : Penyesuaian SO dengan Deposit
      Developer : Hendra
      Status : Update
    */
    public static function getSisa($params = [], $withSo = false) {
        // dd($withSo);
        if($withSo == true){
            $sum = 0;
            $so = SalesOrder::join('job_orders', 'job_orders.id', 'sales_orders.job_order_id');
            $querySo = SalesOrder::join('job_orders', 'job_orders.id', 'sales_orders.job_order_id')
                        ->join('job_order_details', 'job_orders.id', 'job_order_details.header_id');
            $queryInv= Invoice::leftJoin('invoice_details', 'invoices.id', 'invoice_details.header_id')
                        ->leftJoin('receivables', 'receivables.relation_id', 'invoices.id');
            $queryRec= Invoice::join('invoice_details', 'invoices.id', 'invoice_details.header_id')
                        ->join('receivables', 'receivables.relation_id', 'invoices.id');

            if($params['customer_id'] ?? null){
                $so = $so->where('job_orders.customer_id', $params['customer_id']);
                $querySo = $querySo->where('job_orders.customer_id', $params['customer_id']);
                $queryInv = $queryInv->where('invoices.customer_id', $params['customer_id']);
                $queryRec = $queryRec->where('invoices.customer_id', $params['customer_id']);
            }

            $jodIdInv = $queryInv->pluck('invoice_details.job_order_detail_id')
                                    ->filter(function($x){
                                        if($x != null){
                                            return $x;
                                        }
                                    });

            $sumRec = $queryRec->sum(DB::raw('receivables.debet - receivables.credit'));
            $sumInv = $queryInv->sum('invoice_details.total_price');
            $sumSO = $querySo->whereNotIn('job_order_details.id', $jodIdInv->all())->sum(DB::raw('job_order_details.total_price')) - $so->get()->sum('jumlah_deposit');
            // dd($sumRec,$sumInv,$sumSO);
            return $sumRec + $sumInv + $sumSO;
        } else {
            $raw = self::query($params);
            $dt = DB::query()->fromSub($raw, "raw");
            $r = $dt->sum(DB::raw("IF(status_piutang = 1, 0, sisa)"));
            $r = round($r);

            return $r;
        }
    }
}
