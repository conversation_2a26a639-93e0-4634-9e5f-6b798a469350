<?php

namespace App\Http\Controllers\Api;

use App\Abstracts\Operational\DeliveryOrderDriver as OperationalDeliveryOrderDriver;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Operational\V2\SendMobileNotification;
use App\Http\Controllers\Api\v5\DeliveryOrderController;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Operational\ManifestFTLController;
use App\Jobs\HitungJoCostManifestJob;
use App\Model\Commodity;
use App\Model\Contact;
use App\Model\ContainerType;
use App\Model\DeliveryManifest;
use App\Model\DeliveryOrderDriver;
use App\Model\JobStatusHistory;
use App\Model\DeliveryRejectHistory;
use App\Model\Manifest;
use App\Model\DriverMessage;
use App\Model\Route;
use App\Model\Vehicle;
use App\Model\VehicleType;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VendorMobileController extends Controller
{
    public function get_user(Request $request)
    {
      $user=DB::table('contacts')
      ->leftJoin('vehicles','vehicles.id','contacts.vehicle_id')
      ->leftJoin('delivery_order_drivers as dod','dod.driver_id','contacts.id')
      ->where('contacts.id',$request->user()->id)
      ->selectRaw('
        contacts.id,
        contacts.name,
        contacts.address,
        contacts.api_token,
        contacts.is_driver,
        contacts.vehicle_id,
        contacts.lat as latitude,
        contacts.lng as longitude,
        vehicles.nopol,
        sum(if(dod.is_finish=1,1,0)) as job_finish,
        sum(if(dod.is_finish=0,1,0)) as job_proses
      ')->groupBy('contacts.id')
      ->first();
      $data=[
        'status' => 'OK',
        'message' => 'Your User Data',
        'data' => $user
      ];
      return response()->json($data,200,[],JSON_NUMERIC_CHECK);
    }
    public function send_message(Request $request)
    {
      $request->validate([
        'message' => 'required',
        'driver_id' => 'required',
      ]);

      DB::beginTransaction();
      $input=$request->only(['driver_id', 'message']);
      DriverMessage::create($input);
      DB::commit();

      return response()->json([
        'status' => 'OK',
        'message' => 'Pesan Terkirim'
      ]);
    }
    public function login(Request $request)
    {
      $login = new VendorApiController;
      $resp = $login->login($request);

      return $resp;

      // $request->validate([
      //   'email' => 'required',
      //   'password' => 'required',
      // ],[
      //   'email.required' => 'Email harus diisi!',
      //   'password.required' => 'Password harus diisi!',
      // ]);

      // $auth=Auth::guard('contact')->attempt([
      //   'email' => $request->email,
      //   'password' => $request->password,
      // ]);
      // if ($auth) {
      //   $item=DB::table('contacts')
      //   ->where('contacts.email',$request->email)
      //   ->leftJoin('vehicles','vehicles.id','contacts.vehicle_id')
      //   ->selectRaw('contacts.id,contacts.name,contacts.address,contacts.api_token,contacts.is_vendor,contacts.vehicle_id,vehicles.nopol')
      //   ->first();

      //   Contact::find($item->id)->update([
      //     'last_login' => Carbon::now(),
      //     // 'is_login' => 1
      //   ]);
      //   $dt=[];
      //   foreach ($item as $key => $value) {
      //     $dt[$key]=$value;
      //   }
      //   if ($item->is_vendor) {
      //     return response()->json(['status' => 'OK','message' => 'Login Berhasil','data' => $dt],200,[],JSON_NUMERIC_CHECK);
      //   } else {
      //     return response()->json(['status' => 'ERROR','message' => 'Login Gagal! Bukan Akun Driver.','data' => null],500,[],JSON_NUMERIC_CHECK);
      //   }
      // } else {
      //   return response()->json(['status' => 'ERROR','message' => 'Login Gagal! Akun tidak ditemukan / password salah','data' => null],500,[],JSON_NUMERIC_CHECK);
      // }
    }

    /**
     * Description : list vehicle type
     */
    public function get_list_vehicle_type(Request $request)
    {
      $perPage = 20;
      if($request->per_page && is_integer($request->per_page)){
        $perPage = $request->per_page;
      }

      $vehicle_type = DB::table('vehicle_types');

      if($request->search){
        $vehicle_type = $vehicle_type->where('name', 'LIKE', '%'.$request->search.'%');
      }
      $vehicle_type = $vehicle_type->selectRaw('id,name')
                  ->paginate($perPage);

      return response()->json($vehicle_type,200,[],JSON_NUMERIC_CHECK);
    }

    /**
     * Description : list Container type
     */
    public function get_list_container_type(Request $request)
    {
      $perPage = 20;
      if($request->per_page && is_integer($request->per_page)){
        $perPage = $request->per_page;
      }

      $container_type = DB::table('container_types');

      if($request->search){
        $container_type = $container_type->where('code', 'LIKE', '%'.$request->search.'%');
        $container_type = $container_type->orWhere('name', 'LIKE', '%'.$request->search.'%');
        $container_type = $container_type->orWhere('size', 'LIKE', '%'.$request->search.'%');
        $container_type = $container_type->orWhere('unit', 'LIKE', '%'.$request->search.'%');
      }
      $container_type = $container_type->selectRaw('id,code,name,size,unit')
                  ->paginate($perPage);

      return response()->json($container_type,200,[],JSON_NUMERIC_CHECK);
    }

    /**
     * Description : list Route type
     */
    public function get_list_route(Request $request)
    {
      $perPage = 20;
      if($request->per_page && is_integer($request->per_page)){
        $perPage = $request->per_page;
      }

      $route = DB::table('routes')
                ->leftJoin('cities as city_from', 'city_from.id', 'routes.city_from')
                ->leftJoin('cities as city_to', 'city_to.id', 'routes.city_to');

      if($request->search){
        $route = $route->where('name', 'LIKE', '%'.$request->search.'%');
      }
      $route = $route->selectRaw('routes.id,routes.name,city_from.name as city_from, city_to.name as city_to')
                  ->paginate($perPage);

      return response()->json($route,200,[],JSON_NUMERIC_CHECK);
    }

    /**
     * Description: list driver
     */
    public function get_list_driver(Request $request)
    {
      $request->validate([
        'driver_id' => 'nullable'
      ],[
      ]);

      $perPage = 10;
      if($request->per_page && is_integer($request->per_page)){
        $perPage = $request->per_page;
      }

      $drivers = DB::table('contacts')
                  ->where('parent_id', auth()->user()->id)
                  ->where('is_driver', 1);

      if($request->search){
        $drivers = $drivers->where('contacts.name', 'LIKE', '%'.$request->search.'%');
      }
      $drivers = $drivers->selectRaw('id,name,address,email,last_login')
                  ->paginate($perPage);

      $data=[
        'status' => 'OK',
        'message' => 'List Driver',
        'data' => $drivers
      ];
      return response()->json($data,200,[],JSON_NUMERIC_CHECK);
    }

    /**
     * Description: list kendaraan
     * Params : driver_id, per_page, search
     *
     */
    public function get_list_vehicle(Request $request)
    {
      $request->validate([
        'driver_id' => 'nullable'
      ],[
      ]);

      $perPage = 10;
      if($request->per_page && is_integer($request->per_page)){
        $perPage = $request->per_page;
      }

      $vehicle=array();
      $vehicle = DB::table('vehicles')
      ->leftJoin('companies','companies.id','vehicles.company_id')
      ->orderBy('vehicles.id');
      if($request->search){
        $vehicle = $vehicle->where('vehicles.nopol', 'LIKE', '%'.$request->search.'%');
        $vehicle = $vehicle->orWhere('vehicles.code', 'LIKE', '%'.$request->search.'%');
        $vehicle = $vehicle->orWhere('companies.name', 'LIKE', '%'.$request->search.'%');
      }
      if($request->driver_id){
        $vehicle = $vehicle->where('contact_id', $request->driver_id);
      } else {
        $vehicle = $vehicle->groupBy('vehicles.id');
      }

      $vehicle = $vehicle->where('vehicles.supplier_id', auth()->user()->id);
      $vehicle = $vehicle->selectRaw('vehicles.id,vehicles.nopol, vehicles.code, companies.name as company_name')
      ->paginate($perPage);

      $data=[
        'status' => 'OK',
        'message' => 'List Kendaraan',
        'data' => $vehicle
      ];
      return response()->json($data,200,[],JSON_NUMERIC_CHECK);
    }

    public function post_vehicle(Request $request)
    {
      $request->validate([
        'driver_id' => 'required',
        'vehicle_id' => 'required'
      ],[
        'driver_id.required' => 'Id Driver harus dimasukkan',
        'vehicle_id.required' => 'Id Kendaraan harus dimasukkan',
      ]);
      DB::beginTransaction();
      Contact::find($request->driver_id)->update([
        'vehicle_id' => $request->vehicle_id
      ]);
      DB::commit();

      return response()->json(['status' => 'OK','message' => 'Kendaraan telah dipilih','data' => null],200,[],JSON_NUMERIC_CHECK);
    }

    public function get_list_job(Request $request)
    {
      $request->validate([
        'driver_id' => 'nullable',
        'is_finish' => 'nullable|numeric',
        'from_date' => 'nullable|date',
        'to_date' => 'nullable|date',
        'job_status_id' => 'nullable|numeric',
      ]);

      $drivers = DB::table('contacts')
                  ->where('parent_id', auth()->user()->id)
                  ->where('is_driver', 1)->get()->pluck('id')->toArray();

      $dateStart = null;
      $dateFinish = null;
      if ($request->from_date) {
        $dateStart=Carbon::parse($request->from_date);
      }
      if ($request->to_date) {
        $dateFinish=Carbon::parse($request->to_date);
      }
      $item = OperationalDeliveryOrderDriver::query([
        'start_date' => $dateStart,
        'end_date' => $dateFinish,
        'status' => $request->job_status_id,
        'driver_id' => $request->driver_id,
        'is_active' => 1,
        'vendor_id' => auth()->user()->id
      ])
          ->select('delivery_order_drivers.id','delivery_order_drivers.vehicle_id','delivery_order_drivers.driver_id','delivery_order_drivers.driver_name', 'delivery_order_drivers.code','driver.name as driver_name', 'vehicles.nopol', 'routes.name as route_name', 'manifests.id as manifest_id', 'delivery_order_drivers.status as delivery_order_status')
          ->paginate();
      return response()->json($item,200,[],JSON_NUMERIC_CHECK);
    }

    public function detail_job(Request $request)
    {
      $request->validate([
        'delivery_order_id' => 'required',
      ],[
        'delivery_order_id.required' => 'Id Job harus dimasukkan',
      ]);

      $item = OperationalDeliveryOrderDriver::show($request->delivery_order_id);
      $history = DeliveryOrderStatusLog::index($request->delivery_order_id);

      $data=json_decode(json_encode($item),true);

      foreach ($history as $key => $value) {
        $data['status_history'][]=[
          'status' => $value->name,
          'update' => $value->created_at,
        ];
      }

      $data['details'] = DeliveryOrderController::indexManifest($request->delivery_order_id, false);

      $tot=[
        'status' => 'OK',
        'message' => 'Detail Job.',
        'data' => $data
      ];
      return response()->json($tot,200,[],JSON_NUMERIC_CHECK);
    }

    public function get_list_manifest(Request $request)
    {
      $request->merge([
        'supplier_id' => auth()->user()->id
      ]);

      $dt = new OperationalApiController();
      $dt = $dt->manifest_datatable($request, false);

      return response()->json($dt, 200);
    }

    public function get_detail_manifest(Request $request)
    {
      $request->validate([
        'manifest_id' => 'required'
      ]);

      $id = $request->manifest_id;

      $data = new ManifestFTLController();
      $data = $data->show($id, true);
      $data = $data->getData();

      return response()->json($data,200,[],JSON_NUMERIC_CHECK);
    }

    public function update_status_job(Request $request)
    {
      // $request->validate([
      //   'delivery_order_id' => 'required'
      // ],[
      //   'delivery_order_id.required' => 'Id Job harus dimasukkan'
      // ]);

      // DB::beginTransaction();
      // $i=DeliveryOrderDriver::findOrFail($request->delivery_order_id);

      // $mn=DB::table('manifests')->where('id', $i->manifest_id)->first();
      // if ($i->is_finish) {
      //   return response()->json(['status' => 'ERROR','message' => 'Job ini sudah finish!','data' => null],500,[],JSON_NUMERIC_CHECK);
      // }
      // $next=DB::select("select job_status_id, (select concat(id,'|',name,'|',is_finish) from job_statuses where urut > js.urut order by urut asc limit 1) as nextstatus from delivery_order_drivers as dod
      // left join job_statuses as js on dod.job_status_id = js.id
      // where dod.id = $request->delivery_order_id
      // ")[0];
      // $xp=explode("|",$next->nextstatus);
      // $update=[
      //   'updated_at' => Carbon::now()
      // ];
      // if ($xp[0]!="") {
      //   $update['job_status_id']=$xp[0];
      //   if ($xp[2]=="1") {
      //     $update['is_finish']=1;
      //   }
      //   JobStatusHistory::create([
      //     'delivery_id' => $request->delivery_order_id,
      //     'job_status_id' => $xp[0],
      //     'driver_id' => $i->driver_id,
      //     'vehicle_id' => $i->vehicle_id,
      //     'vendor_id' => $i->vendor_id,
      //   ]);
      // }
      // $i->update($update);
      // // dd($status);
      // DB::commit();

      // return response()->json(['status' => 'OK','message' => 'Status berhasil diupdate!','data' => null],200,[],JSON_NUMERIC_CHECK);
    }

    /**
     * Description: mengcancel/tolak job
     * params: delivery_order_id, description
     */
    public function reject_job(Request $request)
    {
      $request->validate([
        'delivery_order_id' => 'required',
        'description' => 'required'
      ],[
        'delivery_order_id.required' => 'Id Job diisi',
        'description.required' => 'Alasan Penolakan harus diisi'
      ]);
      DB::beginTransaction();
      $dod=DeliveryOrderDriver::find($request->delivery_order_id);
      if ($dod->job_status_id>=3) {
        return response()->json([
          'status' => 'ERROR',
          'message' => 'Job Order sudah diterima / dalam pengangkutan, tidak dapat membatalkan job.'
        ],500);
      }
      // DeliveryRejectHistory::create([
      //   'delivery_order_id' => $request->delivery_order_id,
      //   'driver_id' => auth()->id(),
      //   'description' => $request->description
      // ]);
      $stt=DB::table('job_statuses')->where('is_reject', 1)->first();
      $updates=[
        'vehicle_id' => null,
        'driver_id' => null,
      ];
      $dm = DB::table('delivery_manifests')->where('delivery_order_driver_id', $dod->id)->first();
      if($dm){
        Manifest::find($dm->manifest_id)
                ->update($updates);
      }
      $updates['job_status_id'] = $stt->id;
      $updates['status'] = 3; // dibatalkan
      $updates['cancel_reason'] = $request->description;
      $dod->update($updates);
      DB::commit();

      return response()->json(['status' => 'OK','message' => 'Job telah ditolak!','data' => null],200,[],JSON_NUMERIC_CHECK);
    }

    public function set_vehicle_driver_form(Request $request, $dodId)
    {
      $dm = DeliveryManifest::where('delivery_order_driver_id', $dodId)->first();

      if(!$dm){
        throw new Exception('Data not found!');
      }
      $vehicle_type_id = Manifest::where('manifests.id', $dm->manifest_id)
                      ->join('vehicle_types as vt', 'vt.id', 'manifests.vehicle_type_id')
                      ->select('vt.id')
                      ->first();
      $data['item'] = Manifest::select('id', 'code')->find($dm->manifest_id);
      $data['commodity'] = Commodity::select('id', 'name')->get();
      $data['vehicle_eksternal'] = Vehicle::select('vehicles.id', 'vehicles.nopol', 'vehicles.code')
                                  ->join('vehicle_variants as variant', 'variant.id', 'vehicles.vehicle_variant_id')
                                  ->join('vehicle_types as type', 'type.id', 'variant.vehicle_type_id')
                                  ->where('supplier_id', auth()->id())
                                  ->where('type.id', $vehicle_type_id->id)
                                  ->get();
      $data['driver'] = Contact::where([
                            'is_driver' => 1,
                            'parent_id' => auth()->id()
                          ])->select('id','name')->get();
      return response()->json($data, 200);
    }

    public function set_vehicle_driver(Request $request, $dodId)
    {
      // $request->validate([
      //   'delivery_order_id' => 'required',
      //   'driver_id' => 'required',
      //   'vehicle_id' => 'required',
      // ],[
      //   'delivery_order_id.required' => 'Id Job harus dimasukkan',
      //   'driver_id.required' => 'Driver harus dimasukkan',
      //   'vehicle_id.required' => 'Kendaraan harus dimasukkan',
      // ]);
      // DB::beginTransaction();
      // $dod=DeliveryOrderDriver::find($request->delivery_order_id);
      // $dod->vehicle_id = $request->vehicle_id;
      // $dod->driver_id = $request->driver_id;
      // $dod->save();

      // // Send Notification to DriverApps
      // $userDriver = User::where('contact_id', $request->driver_id)->first();
      // if($userDriver && !empty($userDriver->fcm_token)){
      //   SendMobileNotification::sendPrivateNotification($userDriver->fcm_token, 'Anda memiliki Job baru', $dod->code);
      // }

      // DB::commit();

      // return response()->json(['status' => 'OK','message' => 'Job berhasil ditugaskan!','data' => null],200,[],JSON_NUMERIC_CHECK);

      $request->validate([
        'delivery_order_number' => 'required',
        'driver_eksternal_id' => 'required',
        'vehicle_eksternal_id' => 'required',
      ], [
        'delivery_order_number.required' => 'No. Surat Jalan wajib diisi',
        'driver_eksternal_id.required' => 'Driver wajib diisi',
        'vehicle_eksternal_id.required' => 'Kendaraan wajib diisi',
      ]);

      $dm = DeliveryManifest::where('delivery_order_driver_id', $dodId)->first();

      if(!$dm){
        throw new Exception('Data not found!');
      }

      $request->merge([
        'vendor_id' => auth()->id(),
        'is_vendor_channel' => 1
      ]);
      $dt = new ManifestFTLController;
      $resp = $dt->store_delivery($request, $dm->manifest_id);
      // Update ManifestCost
      HitungJoCostManifestJob::dispatch($dm->manifest_id);

      return $resp;
    }

    public function reject_vehicle_driver(Request $request, $dodId)
    {
      $dm = DeliveryManifest::where('delivery_order_driver_id', $dodId)->first();

      if(!$dm){
        throw new Exception('Data not found!');
      }

      $dt = new ManifestFTLController($request, $dm->manifest_id);
      $dt = $dt->reject_vehicle($request, $dm->manifest_id);
      return $dt;
    }

    public function cancel_vehicle_driver(Request $request, $dodId)
    {
      $dm = DeliveryManifest::where('delivery_order_driver_id', $dodId)->first();

      if(!$dm){
        throw new Exception('Data not found!');
      }

      $dt = new ManifestFTLController($request, $dm->manifest_id);
      $dt = $dt->cancel_vehicle($request, $dm->manifest_id);
      return $dt;
    }

    /**
     * PRICE LIST
     */
    public function get_list_pricelist(Request $request)
    {
      $wr="1=1";

        if ($request->company_id) {
            $wr.=" AND vendor_prices.company_id = $request->company_id";
        } else {
            if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
                $wr.=" AND vendor_prices.company_id = ".auth()->user()->company_id;
            }
        }

        if($request->filled('is_approve')) {
          $wr.=" AND vendor_prices.is_approve = $request->is_approve";
        }

        $user = $request->user();

        // $item = VendorPrice::with('vendor','company','commodity','service','piece','route','moda','vehicle_type','service_type')->whereRaw($wr)->select('vendor_prices.*');
        $item = DB::table('vendor_prices')
        ->leftJoin('contacts','contacts.id','vendor_prices.vendor_id')
        ->leftJoin('companies','companies.id','vendor_prices.company_id')
        ->leftJoin('routes','routes.id','vendor_prices.route_id')
        ->leftJoin('cost_types','cost_types.id','vendor_prices.cost_type_id')
        ->leftJoin('vehicle_types','vehicle_types.id','vendor_prices.vehicle_type_id')
        ->leftJoin('container_types','container_types.id','vendor_prices.container_type_id')
        ->whereRaw($wr)
        ->where('vendor_prices.vendor_id', auth()->user()->id)
        ->whereIsUsed(1)
        ->selectRaw('
          vendor_prices.id,
          vendor_prices.date,
          vendor_prices.vendor_id,
          vendor_prices.cost_category,
          vendor_prices.price_full,
          if(vehicle_types.name is not null, vehicle_types.name, container_types.code) as vtype,
          cost_types.name as cost_type_name,
          cost_types.id as cost_type_id,
          routes.name as trayek,
          companies.name as cabang,
          contacts.name as vendor,
          is_approve
          ');

          $item = $item->paginate();

          return response()->json($item, 200);
    }

    public function detail_pricelist(Request $request, $id)
    {
      $data = DB::table('vendor_prices')->where('vendor_prices.id', $id)
      ->join('companies as com', 'com.id', 'vendor_prices.company_id')
      ->leftJoin('cost_types as ct', 'ct.id', 'vendor_prices.cost_type_id')
      ->leftJoin('container_types as cont', 'cont.id', 'vendor_prices.container_type_id')
      ->leftJoin('vehicle_types as veht', 'veht.id', 'vendor_prices.vehicle_type_id')
      ->leftJoin('routes as rot', 'rot.id', 'vendor_prices.route_id')
      ->selectRaw('
      vendor_prices.*,
      com.name as company_name,
      ct.name as cost_name,
      cont.name as container_name,
      veht.name as vehicle_name,
      rot.name as route_name
      ')
      ->first();

      return response()->json($data);
    }

    public function create_pricelist_form()
    {
      $data = [];
      $vehicle_types = VehicleType::select('id', 'name')->where('type', 1)->get()->toArray();
      $container_types = ContainerType::get()->toArray();
      $routes = Route::select('id', 'name')->get()->toArray();

      $data['vehicle_types'] = $vehicle_types;
      $data['container_types'] = $container_types;
      $data['routes'] = $routes;

      return response()->json($data, 200);

    }

    public function store_pricelist(Request $request)
    {
      $request->validate([
        'name' => 'required',
        'code' => 'required',
        'biaya' => 'required',
        'tgl_mulai_berlaku' => 'required',
        'route_id' => 'nullable',
        'vehicle_type_id' => 'required_if:biaya,2',
        'container_type_id' => 'required_if:biaya,3',
        'unit_price' => 'required'
      ], [
        'vehicle_type_id.required_if' => 'Tipe kendaraan wajib diisi jika tipe Trucking',
        'container_type_id.required_if' => 'Tipe kontainer wajib diisi jika tipe Container',
      ]);
      if($request->biaya == 2 || $request->biaya == 3){
        $request->validate([
          'route_id' => 'required'
        ]);
      }
      DB::beginTransaction();
      $msg = 'Data successfully saved';
      $status_code = 200;
      $contact = DB::table('contacts')->where('id', auth()->user()->id)->first();

      $params = [
        // 'cost_type_id' => $request->cost_id,
        'cost_category' => 1,
        'service_id' => 0,
        'service_type_id' => 0,
        'name' => $request->name,
        'code' => $request->code,
        'price_full' => $request->unit_price,
        'date' => date("Y-m-d", strtotime($request->tgl_mulai_berlaku)),
        'route_id' => $request->route_id ?? null,
        'vehicle_type_id' => $request->vehicle_type_id ?? null,
        'container_type_id' => $request->container_type_id ?? null,
        'is_approve' => 0,
        'vendor_id' => auth()->user()->id,
        'company_id' => $contact->company_id,
        'created_by' => auth()->user()->id
      ];

      DB::table('vendor_prices')->insert($params);

      DB::commit();

      $data['message'] = $msg;
      return response()->json($data, $status_code);
    }
    /** END PRICELIST */

    public function logout(Request $request)
    {
      DB::beginTransaction();
      $c=Contact::find(auth()->id())->update([
        'vehicle_id' => null,
        'is_login' => 0,
        'api_token' => str_random(100),
        'lat' => null,
        'lng' => null,
      ]);
      DB::commit();

      return response()->json(['status' => 'OK','message' => 'Anda telah Logout!','data' => null],200,[],JSON_NUMERIC_CHECK);
    }

    public function change_password(Request $request)
    {
      $request->validate([
        'old_password' => 'required',
        'new_password' => 'required',
      ]);
      $find=Contact::find(auth()->id());
      $attempt=Auth::guard('contact')->attempt([
        'email' => $find->email,
        'password' => $request->old_password
      ]);

      if ($attempt) {
        DB::beginTransaction();
        $find->update([
          'password' => bcrypt($request->new_password)
        ]);
        DB::commit();

        return response()->json(['status' => 'OK','message' => 'Password Berhasil Diganti!','data' => null],200,[],JSON_NUMERIC_CHECK);
      } else {
        return response()->json(['status' => 'ERROR','message' => 'Password Lama Anda tidak cocok!','data' => null],500,[],JSON_NUMERIC_CHECK);
      }
    }

    public function update_location(Request $request)
    {
      $request->validate([
        'lat' => 'required',
        'lng' => 'required',
      ]);

      DB::beginTransaction();
      Contact::where('id',auth()->id())->update([
        'lat' => $request->lat,
        'lng' => $request->lng,
      ]);
      DB::commit();

      return response()->json(['status' => 'OK','message' => 'Lokasi telah diupdate!','data' => null],200,[],JSON_NUMERIC_CHECK);

    }

}
