<?php

namespace App\Exports\Operational;

use App\Http\Controllers\Operational\ReportController;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class ExportWorkOrderSales implements FromView
{
    protected $request;
    public function __construct($request)
    {
        $this->request = $request;
    }
    public function view(): View
    {
        $request = $this->request;
        $resp['start_date'] = $request->start_date ? date('Y-m', strtotime($request->start_date)) : date('Y-01');
        $resp['end_date'] = $request->end_date ? date('Y-m', strtotime($request->end_date)) : date('Y-m');
        $resp['ytd'] = $request->end_date ? date('y', strtotime($request->end_date)) : date('y');
        $resp['data'] = (new ReportController)->getWorkOrderSales($request);
        return view('operational_report.work_order_sales', $resp);
    }
}
