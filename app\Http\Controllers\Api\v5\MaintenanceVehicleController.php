<?php

namespace App\Http\Controllers\Api\v5;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Contact;
use App\Abstracts\Vehicle\VehicleDriver;
use App\Model\Vehicle;
use App\Model\VehicleBody;
use App\Model\VehicleChecklist;
use App\Model\VehicleChecklistDetailBody;
use App\Model\VehicleChecklistDetailItem;
use App\Model\VehicleChecklistItem;
use App\Model\VehicleMaintenance;
use App\Model\VehicleMaintenanceDetail;
use App\Model\VehicleMaintenanceDocument;
use App\Model\VehicleMaintenanceType;
use App\Utils\TransactionCode;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\File;
use Response;
use Validator;
use Barryvdh\DomPDF\Facade as PDF;
use Auth;
use Illuminate\Support\Facades\DB;

class MaintenanceVehicleController extends Controller
{
    /*
        Auth -- start
    */

    public function __construct()
    {
        $auth = new \App\Http\Controllers\Api\v4\CustomerController();
        $this->auth = $auth;

        $this->middleware(function ($request, $next) {
            $this->remark = DB::table('print_remarks')
            ->where('company_id', 40)
            ->leftJoin('companies', 'companies.id', 'print_remarks.company_id')
            ->leftJoin('cities', 'cities.id', 'companies.city_id')
            ->select('print_remarks.*', 'companies.code', 'companies.address as alamat', 'companies.name as company_name', 'cities.name as kota')
            ->first();
            return $next($request);
          });
    }

    public function login(Request $request)
    {
        $params = [];
        $params['username'] = $request->username;
        $params['password'] = $request->password;
        $params = new Request($params);
        $user = $this->auth->login_user($params);
        $dt = $user->getData();
        $resp = [];
        $resp['message'] = 'OK';
        $statusCode = 200;
        try {
            if (($dt->id ?? null)) {
                if (!$dt->contact_id) {
                    throw new Exception('Only driver can be login');
                } else {
                    $contact = Contact::show($dt->contact_id);
                    if (!$contact->is_driver) {
                        throw new Exception('Only driver can be login');
                    }
                }

                $resp['access_token'] = [];
                $resp['access_token']['access_token'] = $dt->api_token;
                $resp['access_token']['token'] = [];
                $resp['access_token']['token']['id'] = $dt->id;
                $resp['access_token']['token']['user_id'] = $dt->id;
                $resp['access_token']['token']['client_id'] = $dt->id;
                $resp['access_token']['token']['name'] = $dt->name;
                $resp['access_token']['token']['expires_date'] = $dt->due_date;
                $resp['group_id'] = $dt->group_id;
            } else {
                throw new Exception($dt->message);
            }
        } catch (\Exception $e) {
            $statusCode = 421;
            $resp['message'] = $e->getMessage();
        }

        return response()->json($resp, $statusCode);
    }

    public function logout(Request $request)
    {
        $resp = $this->auth->logout($request);

        return $resp;
    }
    /*
        Auth -- end
    */

    /*
        Master
    */
    public function getVehicle(Request $request)
    {
        $data['data'] = DB::table('vehicles')
            ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->leftJoin('companies', 'companies.id', 'vehicles.company_id')
            ->select('vehicles.id', 'vehicles.nopol', 'vehicle_variants.name as variant_name', 'companies.name as company_name')
            ->where('is_active', 1)
            ->get();

        $data['msg'] = 'Kendaraan berhasil di tampilkan';

        return response()->json($data);
    }

    public function getBengkel(Request $request)
    {
        $data['data'] = DB::table('contacts')
            ->select('contacts.id', 'contacts.name', 'contacts.address')
            ->where('is_bengkel', 1)
            ->get();

        $data['msg'] = 'Bengkel berhasil di tampilkan';

        return response()->json($data);
    }

    public function getItem(Request $request)
    {
        $data['data'] = DB::table('items')
            ->select('items.id', 'items.name')
            ->get();

        $data['msg'] = 'Barang berhasil di tampilkan';

        return response()->json($data);
    }

    /*
        Master End
    */

    /*
        inspection section
    */
    public function createVehicleChecklist()
    {
        $data['checklist'] = VehicleChecklist::where('is_active', 1)->select('id', 'is_active', 'name')->get();
        $data['body'] = VehicleBody::where('is_active', 1)->select('id', 'is_active', 'name')->get();
        $data['message'] = 'Ok';

        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function storeChecklist(Request $request)
    {
        $request->validate([
            // 'company_id' => 'required',
            'vehicle_id' => 'required',
            'officer' => 'required',
            'date_transaction' => 'required',
            'time_transaction' => 'required'
        ]);

        DB::beginTransaction();

        $i = VehicleChecklistItem::create([
            'company_id' => auth()->user()->company_id,
            'vehicle_id' => $request->vehicle_id,
            'officer' => $request->officer,
            'date_transaction' => dateDB($request->date_transaction),
            'time_transaction' => $request->time_transaction,
            'create_by' => auth()->id(),
            'score' => $request->score ?? 0,
        ]);

        $checkItemsHasIsExist = false;
        $checkBodysHasIsExist = false;
        if (is_array($request->items)) {
            foreach ($request->items as $key => $value) {
                VehicleChecklistDetailItem::create([
                    'header_id' => $i->id,
                    'vehicle_checklist_id' => $value['id'],
                    'is_exist' => $value['is_exist'],
                    'is_function' => $value['is_function'],
                    'condition' => $value['condition'],
                ]);

                if ($value['is_exist'] == 1) {
                    $checkItemsHasIsExist = true;
                }
            }
        }


        if (is_array($request->body)) {
            foreach ($request->body as $key => $value) {
                VehicleChecklistDetailBody::create([
                    'header_id' => $i->id,
                    'vehicle_body_id' => $value['id'],
                    'is_exist' => $value['is_exist'],
                    'is_function' => $value['is_function'],
                    'condition' => $value['condition'],
                ]);

                if ($value['is_exist'] == 1) {
                    $checkBodysHasIsExist = true;
                }
            }
        }

        $vehicle_checklist_detail_item = DB::table('vehicle_checklist_detail_items')
            ->whereHeaderId($i->id)
            ->selectRaw('100 * SUM( IF(is_exist = 0, 0, is_exist + is_function + IF(`condition` = 1, 1, 0)) ) / (3 * IFNULL( (SELECT COUNT("id") FROM vehicle_checklists) , 0)) AS nilai')
            ->first();

        $nilai_body = DB::table('vehicle_checklist_detail_bodies')
            ->whereHeaderId($i->id)
            ->selectRaw('100 * SUM( IF(is_exist = 0, 0, is_exist + is_function + IF(`condition` = 1, 1, 0)) ) / (3 * IFNULL( (SELECT COUNT("id") FROM vehicle_bodies) , 0)) AS nilai')->first();
        $score = ($vehicle_checklist_detail_item->nilai + $nilai_body->nilai) / 2;
        $i->update(['score' => $score ?? 0]);

        $vc = Vehicle::find($request->vehicle_id);
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_checklist_items_id' => $i->id,
                'create_by' => auth()->id(),
                'title' => 'Inspeksi atas kendaraan',
                'description' => "$vc->nopol sudah selesai",
                'created_at' => Carbon::now()
            ]);

        DB::commit();

        return Response::json(['message' => 'Inspeksi kendaraan berhasil dibuat!'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function vehicleChecklistAll(Request $request)
    {
        $search = $request->get('search');
        $item = DB::table('vehicle_checklist_items')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_checklist_items.vehicle_id')
            ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->leftJoin('companies', 'companies.id', 'vehicle_checklist_items.company_id')
            ->select('vehicle_checklist_items.id', 'vehicle_checklist_items.created_at as dibuat', 'vehicles.nopol', 'vehicles.code', 'vehicles.color', 'vehicle_variants.name as variant_name', 'companies.name as company_name')
            ->orderBy('vehicle_checklist_items.created_at', 'desc')
            ->when($search, function ($query, $search) {
                return $query->where(function ($query) use ($search) {
                    $query->where('vehicles.nopol', 'LIKE', "%$search%")
                        ->orWhere('vehicles.color', 'LIKE', "%$search%")
                        ->orWhere('companies.name', 'LIKE', "%$search%")
                        ->orWhere('vehicle_variants.name', 'LIKE', "%$search%");
                });
            });

        $data['data'] = $item->get();
        $data['total'] = $item->count();

        return response()->json($data);
    }

    public function vehicleChecklistDetail(Request $request, $id)
    {
        // $data['data'] = VehicleChecklistItem::find($id);
        $data['data'] = DB::table('vehicle_checklist_items')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_checklist_items.vehicle_id')
            ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->leftJoin('companies', 'companies.id', 'vehicle_checklist_items.company_id')
            ->leftJoin('users', 'users.id', 'vehicle_checklist_items.create_by')
            ->select('vehicle_checklist_items.id', 'vehicle_checklist_items.id', 'vehicle_checklist_items.company_id', 'vehicle_checklist_items.vehicle_id', 'vehicle_checklist_items.officer', 'vehicle_checklist_items.created_at as dibuat', 'vehicles.nopol', 'vehicles.code', 'vehicles.color', 'vehicle_variants.name as variant_name', 'companies.name as company_name', 'vehicle_checklist_items.date_transaction', 'vehicle_checklist_items.time_transaction', 'users.name as pembuat')
            ->where('vehicle_checklist_items.id', $id)->first();
        $data['checklist'] = VehicleChecklistDetailItem::where('header_id', $id)->get();
        $data['body'] = VehicleChecklistDetailBody::where('header_id', $id)->get();
        $data['total'] = DB::table('vehicle_checklist_items')->count();

        return response()->json($data);
    }

    public function updateChecklist(Request $request, $id)
    {
        $request->validate([
            'vehicle_id' => 'required',
            'officer' => 'required',
            'date_transaction' => 'required',
            'time_transaction' => 'required'
        ]);
        DB::beginTransaction();
        $i = VehicleChecklistItem::find($id);

        $i->update([
            'vehicle_id' => $request->vehicle_id,
            'officer' => $request->officer,
            'date_transaction' => dateDB($request->date_transaction),
            'time_transaction' => $request->time_transaction,
        ]);

        $checkItemsHasIsExist = false;
        $checkBodysHasIsExist = false;

        if (is_array($request->items)) {

            foreach ($request->items as $key => $value) {
                // dd(array_key_exists('id', $value));

                if (array_key_exists('id', $value)) {
                    $detailItem = VehicleChecklistDetailItem::find($value['id']);
                    $detailItem->update([
                        'is_exist' => $value['is_exist'],
                        'is_function' => $value['is_function'],
                        'condition' => $value['condition']
                    ]);
                } else {
                    if (isset($value['is_exist']) || isset($value['is_function']) || isset($value['condition'])) {
                        VehicleChecklistDetailItem::create([
                            'header_id' => $id,
                            'vehicle_checklist_id' => $value['vehicle_checklist_id'],
                            'is_exist' => $value['is_exist'] ?? 0,
                            'is_function' => $value['is_function'] ?? 0,
                            'condition' => $value['condition'] ?? 0
                        ]);
                    }
                }

                if (isset($value['is_exist'])) {
                    if ($value['is_exist'] == 1) {
                        $checkItemsHasIsExist = true;
                    }
                }
            }
        }

        if (is_array($request->body)) {
            foreach ($request->body as $key => $value) {
                if (!empty($value)) {
                    if (array_key_exists('id', $value)) {
                        $detailBody = VehicleChecklistDetailBody::find($value['id']);
                        $detailBody->update([
                            'is_exist' => $value['is_exist'],
                            'is_function' => $value['is_function'],
                            'condition' => $value['condition']
                        ]);
                    } else {
                        if (array_key_exists('vehicle_body_id', $value)) {
                            VehicleChecklistDetailBody::create([
                                'header_id' => $id,
                                'vehicle_body_id' => $value['vehicle_body_id'],
                                'is_exist' => $value['is_exist'] ?? 0,
                                'is_function' => $value['is_function'] ?? 0,
                                'condition' => $value['condition'] ?? 0
                            ]);
                        }
                    }
                }

                if (isset($value['is_exist'])) {
                    if ($value['is_exist'] == 1) {
                        $checkBodysHasIsExist = true;
                    }
                }
            }
        }
        $vehicle_checklist_detail_item = DB::table('vehicle_checklist_detail_items')
            ->whereHeaderId($id)
            ->selectRaw('100 * SUM( IF(is_exist = 0, 0, is_exist + is_function + IF(`condition` = 1, 1, 0)) ) / (3 * IFNULL( (SELECT COUNT("id") FROM vehicle_checklists) , 0)) AS nilai')
            ->first();

        $nilai_body = DB::table('vehicle_checklist_detail_bodies')
            ->whereHeaderId($id)
            ->selectRaw('100 * SUM( IF(is_exist = 0, 0, is_exist + is_function + IF(`condition` = 1, 1, 0)) ) / (3 * IFNULL( (SELECT COUNT("id") FROM vehicle_bodies) , 0)) AS nilai')->first();
        $score = ($vehicle_checklist_detail_item->nilai + $nilai_body->nilai) / 2;
        $i->update(['score' => $score]);
        DB::commit();
        return Response::json(null);
    }

    /*
        inspection end section
    */

    /*
        Issue section
    */
    public function vehicleIssue(Request $request)
    {
        $query = $request->get('search');
        $item['data'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->leftJoin('vehicle_maintenance_details', 'vehicle_maintenance_details.header_id', 'vehicle_maintenances.id')
            ->when($query, function ($query, $q) {
                return $query->where(function ($query) use ($q) {
                    $query->where('vehicles.nopol', 'LIKE', "%$q%")
                        ->orWhere('vehicle_maintenances.issue', 'LIKE', "%$q%")
                        ->orWhere('users.name', 'LIKE', "%$q%")
                        ->orWhere('bengkel.name', 'LIKE', "%$q%")
                        ->orWhere('vehicle_maintenances.name', 'LIKE', "%$q%");
                });
            })
            ->selectRaw('
            vehicle_maintenances.id,
            vehicle_maintenances.name,
            vehicle_maintenances.issue,
            vehicle_maintenances.date_rencana,
            vehicle_maintenances.km_rencana,
            vehicle_maintenances.status,
            vehicles.nopol,
            users.name as creator_name,
            bengkel.name as bengkel_name,
            maintenance_statuses.name as status_name,
            sum(vehicle_maintenance_details.cost_rencana) as biaya_rencana ,
            sum(vehicle_maintenance_details.total_rencana)+sum(distinct vehicle_maintenances.cost_rencana) as total_rencana,
            sum(vehicle_maintenance_details.total_realisasi)+sum(distinct vehicle_maintenances.cost_realisasi) as total_realisasi
            ')->groupBy('vehicle_maintenances.id')
            ->get();

        $item['message'] = "Ok";

        return response()->json($item);
    }

    public function createVehicleIssue()
    {
        $data['vehicle_maintenance_type'] = VehicleMaintenanceType::all();
        $data['message'] = 'ok';
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function storeVehicleIssue(Request $request)
    {
        $request->validate([
            'vehicle_id' => 'required',
            'name' => 'required',
            'issue' => 'required',
            'date_pengajuan' => 'required',
            'km_rencana' => 'required',
        ], [
            'vehicle_id.required' => 'Kendaraan tidak boleh kosong',
            'name.required' => 'Name tidak boleh kosong',
            'issue.required' => 'Issue tidak boleh kosong',
            'date_pengajuan.required' => 'Tanggal Issue tidak boleh kosong',
            'km_rencana.required' => 'Kilometer Kendaraan tidak boleh kosong',
        ]);

        DB::beginTransaction();

        $vehicle = Vehicle::find($request->vehicle_id);

        $code = new TransactionCode($vehicle->company_id, 'maintenance');
        $code->setCode();
        $trx_code = $code->getCode();

        $input = [
            'company_id' => $vehicle->company_id,
            'vehicle_id' => $vehicle->id,
            'code' => $trx_code,
            'name' => $request->name,
            'issue' => $request->issue,
            'date_pengajuan' => dateDB($request->date_pengajuan),
            'km_rencana' => $request->km_rencana,
            'description' => $request->description,
            'is_internal' => $request->is_internal,
            'create_by' => $request->create_by,
            'status' => 1
        ];

        $i = VehicleMaintenance::create($input);

        if (isset($request->detail)) {
            foreach ($request->detail as $key => $value) {
                if (isset($value)) {
                    if (!($value['vehicle_maintenance_type_id'] ?? null)) {
                        throw new Exception('Jenis Issue tidak boleh kosong');
                    }

                    $value['qty'] = $value['qty'] ?? 0;
                    $value['price'] = $value['price'] ?? 0;

                    $vmd = VehicleMaintenanceDetail::create([
                        'header_id' => $i->id,
                        'vehicle_maintenance_type_id' => $value['vehicle_maintenance_type_id'],
                        'tipe_kegiatan' => $value['tipe_kegiatan'],
                        'cost_rencana' => $value['price'],
                        'qty_rencana' => $value['qty'],
                        'total_rencana' => $value['qty'] * $value['price'],
                    ]);
                }
            }
        } else {
            return response()->json(['message' => 'Silahkan Pilih issue terlebih dahulu!'], 200, [], JSON_NUMERIC_CHECK);
        }

        if (isset($request->document)) {
            foreach ($request->document as $key => $v) {
                if (isset($v)) {
                    if (!($v ?? null)) {
                        throw new Exception('file tidak boleh kosong');
                    }

                    $file = $v;
                    $filename = "VEHICLE_ISSUE_" . $i->id . "_" . date('Ymd_His') . '__' . $file->getClientOriginalName();
                    $file->move(public_path('files'), $filename);

                    DB::table('vehicle_maintenance_documents')->insert([
                        'header_id' => $i->id,
                        'create_by' => auth()->id(),
                        'name' => $file->getClientOriginalName(),
                        'file_name' => $filename,
                        'is_issue' => 1,
                        'extension' => $file->getClientOriginalExtension(),
                        'upload_date' => Carbon::now(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }
        }

        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $i->id,
                'create_by' => auth()->id(),
                'title' => 'Pengajuan Issue kendaraan baru',
                'description' => "Dengan nopol $vehicle->nopol dibuat oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();

        return response()->json(['message' => 'Issue berhasil disimpan!'], 200);
    }

    public function showVehicleIssue($id)
    {
        $data['detail'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->select(
                'vehicle_maintenances.id',
                'vehicle_maintenances.name',
                'vehicle_maintenances.issue',
                'vehicle_maintenances.is_internal',
                'vehicle_maintenances.km_rencana',
                'vehicle_maintenances.date_pengajuan',
                'vehicle_maintenances.date_rencana',
                'vehicle_maintenances.date_perawatan',
                'vehicle_maintenances.status',
                'vehicles.nopol',
                'users.name as creator_name',
                'maintenance_statuses.name as status_name',
                'vehicle_maintenances.description',
                'vehicle_maintenances.created_at',
                'bengkel.name as bengkel_name'
            )
            ->where('vehicle_maintenances.id', $id)
            ->first();

        $data['jenis'] = DB::table('vehicle_maintenance_details')
            ->leftJoin('vehicle_maintenance_types', 'vehicle_maintenance_types.id', 'vehicle_maintenance_details.vehicle_maintenance_type_id')
            ->where('vehicle_maintenance_details.header_id', $id)
            ->select('vehicle_maintenance_details.id', 'vehicle_maintenance_details.header_id', 'vehicle_maintenance_types.name as type_name', 'vehicle_maintenance_details.tipe_kegiatan')
            ->get();

        $data['document'] = DB::table('vehicle_maintenance_documents')
            ->where('vehicle_maintenance_documents.header_id', $id)
            ->where('is_issue', 1)
            ->get();

        $data['message'] = 'OK';

        return response()->json($data);
    }

    public function updateVehicleIssue(Request $request, $vm_id)
    {
        $request->validate([
            'vehicle_id' => 'required',
            'name' => 'required',
            'issue' => 'required',
            'date_pengajuan' => 'required',
            'km_rencana' => 'required',
        ], [
            'vehicle_id.required' => 'Kendaraan tidak boleh kosong',
            'name.required' => 'Name tidak boleh kosong',
            'issue.required' => 'Issue tidak boleh kosong',
            'date_pengajuan.required' => 'Tanggal Issue tidak boleh kosong',
            'km_rencana.required' => 'Kilometer Kendaraan tidak boleh kosong',
        ]);

        if (count($request->detail) < 1) {
            return response()->json(['message' => 'Anda belum memasukkan jenis perawatan!'], 200, [], JSON_NUMERIC_CHECK);
        }

        DB::beginTransaction();
        $i = VehicleMaintenance::find($vm_id)->update([
            // 'vendor_id' => $request->vendor_id,
            'name' => $request->name,
            'km_rencana' => $request->km_rencana,
            'date_pengajuan' => dateDB($request->date_pengajuan),
            'description' => $request->description,
            'issue' => $request->issue,
            'is_internal' => $request->is_internal,
            'create_by' => $request->create_by
        ]);
        $ss = VehicleMaintenance::find($vm_id);
        $v = Vehicle::find($ss->vehicle_id);

        if (is_array($request->detail)) {
            $newVmdIds = [];
            foreach ($request->detail as $value) {
                $exist = null;
                if (($value['id'] ?? null)) {
                    $exist = DB::table('vehicle_maintenance_details')
                        ->whereId($value['id'])
                        ->first();
                }
                $qty_realisasi = $value['qty_realisasi'] ?? 0;
                $cost_realisasi = $value['cost_realisasi'] ?? 0;
                $qty_rencana = $value['qty_rencana'] ?? 0;
                $cost_rencana = $value['cost_rencana'] ?? 0;
                $total_realisasi = $qty_realisasi * $cost_realisasi;
                $total_pengajuan = $qty_rencana * $cost_rencana;

                if ($exist) {
                    DB::table('vehicle_maintenance_details')
                        ->whereId($value['id'])
                        ->update([
                            'vehicle_maintenance_type_id' => $value['vehicle_maintenance_type_id'],
                            'tipe_kegiatan' => $value['tipe_kegiatan'],
                            'item_id' => $value['item_id'] ?? null,
                            'rack_id' => $value['rack_id'] ?? null,
                            'warehouse_receipt_detail_id' => $value['warehouse_receipt_detail_id'] ?? null,
                            'qty_realisasi' => $qty_realisasi,
                            'cost_realisasi' => $cost_realisasi,
                            'qty_rencana' => $qty_rencana,
                            'cost_rencana' => $cost_rencana,
                            'total_realisasi' => $total_realisasi,
                            'total_rencana' => $total_pengajuan
                        ]);
                    $newVmdIds[] = $value['id'];
                } else {
                    $newVmdId =  DB::table('vehicle_maintenance_details')->insertGetId([
                        'header_id' => $vm_id,
                        'vehicle_maintenance_type_id' => $value['vehicle_maintenance_type_id'],
                        'tipe_kegiatan' => $value['tipe_kegiatan'],
                        'item_id' => $value['item_id'] ?? null,
                        'rack_id' => $value['rack_id'] ?? null,
                        'warehouse_receipt_detail_id' => $value['warehouse_receipt_detail_id'] ?? null,
                        'qty_rencana' => $qty_rencana,
                        'cost_rencana' => $cost_rencana,
                        'qty_realisasi' => $qty_realisasi,
                        'cost_realisasi' => $cost_realisasi,
                        'total_rencana' => $total_pengajuan,
                        'total_realisasi' => $total_realisasi
                    ]);
                    $newVmdIds[] = $newVmdId;
                }
            }

            $vmd = VehicleMaintenanceDetail::where('header_id', $vm_id)->get();
            foreach ($vmd as $v) {
                $d = in_array($v->id, $newVmdIds);
                if (!$d) {
                    $v->delete();
                }
            }
        }

        // if ($ss->is_internal == 0) {
        //     $pr = PurchaseRequest::create([
        //         'company_id' => $ss->company_id,
        //         'description' => 'Biaya Pemakaian Barang untuk Perawatan Kendaraan oleh Vendor - ' . $ss->code . ' - Nopol : ' . $v->nopol,
        //         'create_by' => auth()->id(),
        //         'status' => 2,
        //         'code' => $ss->code,
        //         'date_needed' => dateDB($request->date_pengajuan),
        //         'date_request' => date("Y-m-d"),
        //         'vehicle_maintenance_id' => $ss->id
        //     ]);

        //     $det = VehicleMaintenanceDetail::where('header_id', $ss->id)->get();
        //     foreach ($det as $key => $value) {
        //         PurchaseRequestDetail::create([
        //             'header_id' => $pr->id,
        //             'vehicle_id' => $ss->vehicle_id,
        //             'item_id' => $value->item_id,
        //             'qty' => $value->qty_rencana,
        //             'qty_approve' => $value->qty_rencana,
        //             'vehicle_maintenance_detail_id' => $value->id,
        //         ]);
        //     }
        // }
        DB::commit();

        return response()->json(['message' => 'Berhasil Di ubah!'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function approveVehicleIssue($id)
    {

        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Issue tidak ditemukan');
        }
        $vm->update([
            'status' => 2,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Issue kendaraan $vehicle->nopol",
                'description' => "Telah di approve oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Approved Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function upload_file_issue(Request $request, $id)
    {
        $request->validate([
            'file' => 'required',
        ]);

        try {
            $file = $request->file('file');
            $filename = "VEHICLE_ISSUE_" . $id . "_" . date('Ymd_His') . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('files'), $filename);

            DB::beginTransaction();
            DB::table('vehicle_maintenance_documents')->insert([
                'header_id' => $id,
                'create_by' => auth()->id(),
                'file_name' => $filename,
                'extension' => $file->getClientOriginalExtension(),
                'is_issue' => 1,
                'upload_date' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();

            return Response::json(["message" => "OK"]);
        } catch (Exception $e) {
            return Response::json(["message" => $e->getMessage()]);
        }
    }

    public function delete_file($id)
    {
        DB::beginTransaction();
        $item = VehicleMaintenanceDocument::find($id);
        File::delete(public_path() . '/' . $item->file_name);
        $item->delete();
        DB::commit();
        return Response::json(['message' => 'File Berhasil di hapus'], 200, [], JSON_NUMERIC_CHECK);
    }

    /*
        End issue section
    */

    /*
        Rencana section
    */

    public function vehicleService(Request $request)
    {
        $query = $request->get('search');
        $statusId = $request->get('status_id');

        $item['data'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->leftJoin('vehicle_maintenance_details', 'vehicle_maintenance_details.header_id', 'vehicle_maintenances.id')
            ->where('vehicle_maintenances.status', '!=', 1)
            ->when($statusId, function ($query, $statusId) {
                return $query->where('vehicle_maintenances.status', $statusId);
            })
            ->when($query, function ($query, $q) {
                return $query->where(function ($query) use ($q) {
                    $query->where('vehicles.nopol', 'LIKE', "%$q%")
                        ->orWhere('vehicle_maintenances.issue', 'LIKE', "%$q%")
                        ->orWhere('users.name', 'LIKE', "%$q%")
                        ->orWhere('bengkel.name', 'LIKE', "%$q%");
                });
            })
            ->selectRaw('
            vehicle_maintenances.id,
            vehicle_maintenances.name,
            vehicles.nopol,
            users.name as creator_name,
            bengkel.name as bengkel_name,
            maintenance_statuses.name as status_name,
            vehicle_maintenances.status,
            vehicle_maintenances.date_pengajuan,
            vehicle_maintenances.date_rencana,
            vehicle_maintenances.date_perawatan,
            vehicle_maintenances.created_at
            ')->groupBy('vehicle_maintenances.id')
            ->get();

        $item['message'] = 'ok';

        return response()->json($item);
    }

    public function showVehicleService($id)
    {
        $data['detail'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->select(
                'vehicle_maintenances.id',
                'vehicle_maintenances.name',
                'vehicle_maintenances.issue',
                'vehicle_maintenances.date_pengajuan',
                'vehicle_maintenances.date_rencana',
                'vehicle_maintenances.date_perawatan',
                'vehicle_maintenances.is_internal',
                'vehicle_maintenances.km_rencana',
                'maintenance_statuses.name as status_name',
                'vehicle_maintenances.status',
                'vehicles.nopol',
                'users.name as creator_name',
                'vehicle_variants.name as variants',
                'vehicle_maintenances.created_at',
                'bengkel.name as bengkel_name'
            )
            ->where('vehicle_maintenances.id', $id)
            ->first();

        $data['jenis'] = DB::table('vehicle_maintenance_details')
            ->leftJoin('vehicle_maintenance_types', 'vehicle_maintenance_types.id', 'vehicle_maintenance_details.vehicle_maintenance_type_id')
            ->where('vehicle_maintenance_details.header_id', $id)
            ->select('vehicle_maintenance_details.id', 'vehicle_maintenance_details.header_id', 'vehicle_maintenance_types.name as type_name', 'vehicle_maintenance_details.tipe_kegiatan')
            ->get();

        $data['spk'] = url('') . '/vehicle/maintenance/penerbitan_spk/' . $id;

        return response()->json($data);
    }
    // ajukan jadwal mobile interface
    public function ajukanVehicleService(Request $request, $id)
    {
        $request->validate([
            'date_rencana' => 'required'
        ]);

        DB::beginTransaction();
        $vm = VehicleMaintenance::find($id);
        if (!$vm) {
            throw new Exception('Perawatan tidak di temukan');
        }
        $vm->update([
            'status' => 3,
            'bengkel_id' => $request->bengkel_id,
            'date_rencana' => dateDB($request->date_rencana) // ajukan rencana
        ]);

        $vehicle = Vehicle::find($vm->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Rencana perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di ajukan oleh $user->name",
                'created_at' => Carbon::now()
            ]);

        DB::commit();
        return Response::json(['message' => 'Berhasil Di Ajukan'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function approveVehicleService($id)
    {

        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak di temukan');
        }
        $vm->update([
            'status' => 4,
        ]);

        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Rencana perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di setujui oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Approved Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function cancelVehicleService($id)
    {

        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'status' => 5,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Rencana perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di batalkan oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Cancel Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    /*
        End Rencana section
    */

    /*
        Perawatan section
    */

    public function storeVehicleServiceEstimation(Request $request, $id)
    {
        $request->validate([
            'km_realisasi' => 'required',
        ]);

        DB::beginTransaction();

        $vm = VehicleMaintenance::find($id);
        if (!$vm) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'km_realisasi' => $request->km_realisasi,
            'status' => 6,
        ]);

        if (isset($request->item)) {
            foreach ($request->item as $key => $i) {
                DB::table('vehicle_maintenance_items')
                    ->insert([
                        'vehicle_maintenance_id' => $id,
                        'item_id' => $i['item_id'],
                        'qty' => $i['qty'],
                        'price' => $i['price'],
                        'total_price' => $i['total_price']
                    ]);
            }
        }

        if (isset($request->document)) {
            foreach ($request->document as $key => $v) {
                if (isset($v)) {
                    if (!($v ?? null)) {
                        throw new Exception('file tidak boleh kosong');
                    }

                    $file = $v;
                    $filename = "ESTIMASI_" . $id . "_" . date('Ymd_His') . '__' . $file->getClientOriginalName();
                    $file->move(public_path('files'), $filename);

                    DB::table('vehicle_maintenance_documents')->insert([
                        'header_id' => $id,
                        'create_by' => auth()->id(),
                        'name' => $file->getClientOriginalName(),
                        'file_name' => $filename,
                        'is_rencana' => 1,
                        'extension' => $file->getClientOriginalExtension(),
                        'upload_date' => Carbon::now(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }
        }
        $vehicle = Vehicle::find($vm->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Estimasi perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di ajukan oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Estimasi Ajukan Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function showVehicleServiceEstimation($id)
    {
        $data['detail'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->select(
                'vehicle_maintenances.id',
                'vehicle_maintenances.name',
                'vehicle_maintenances.issue',
                'vehicle_maintenances.is_internal',
                'vehicle_maintenances.km_realisasi',
                'vehicle_maintenances.date_pengajuan',
                'vehicle_maintenances.date_rencana',
                'vehicle_maintenances.date_perawatan',
                'vehicle_maintenances.status',
                'vehicles.nopol',
                'users.name as creator_name',
                'maintenance_statuses.name as status_name',
                'vehicle_maintenances.description',
                'vehicle_maintenances.created_at',
                'bengkel.name as bengkel_name'
            )
            ->where('vehicle_maintenances.id', $id)
            ->first();

        $data['item'] = DB::table('vehicle_maintenance_items')
            ->leftJoin('items', 'items.id', 'vehicle_maintenance_items.item_id')
            ->where('vehicle_maintenance_items.vehicle_maintenance_id', $id)
            ->select('vehicle_maintenance_items.*', 'items.name as item_name')
            ->get();

        $data['document'] = DB::table('vehicle_maintenance_documents')
            ->where('vehicle_maintenance_documents.header_id', $id)
            ->where('is_rencana', 1)
            ->get();

        $data['message'] = 'OK';

        return response()->json($data);
    }

    public function approveVehicleServiceEstimation($id)
    {

        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Pengajuan Estimasi tidak di temukan');
        }
        $vm->update([
            'status' => 7,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Estimasi perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di setujui oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Approved Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function cancelVehicleServiceEstimation($id)
    {

        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Pengajuan Estimasi tidak ditemukan');
        }
        $vm->update([
            'status' => 8,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Estimasi perawatan kendaraan $vehicle->nopol",
                'description' => "Telah di batalkan oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Cancel Berhasil'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function startVehicleService($id)
    {
        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'status' => 9,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Perawatan kendaraan",
                'description' => " $vehicle->nopol, Telah dimulai",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Perawatan Kendaraan berhasil dimulai'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function handOverVehicle($id)
    {
        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'status' => 10,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Kendaraan $vehicle->nopol",
                'description' => "Telah diserahkan oleh $user->name, untuk perawatan",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Kendaraan berhasil diserahkan'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function finishVehicleService($id)
    {
        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'status' => 11,
        ]);
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Perawatan kendaraan $vehicle->nopol",
                'description' => "Telah selesai dirawat oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Perawatan telah selesai'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function receiptVehicle(Request $request, $id)
    {
        DB::beginTransaction();
        $vm = DB::table('vehicle_maintenances')
            ->where('id', $id);
        $exist = $vm->first();
        if (!$exist) {
            throw new Exception('Perawatan tidak ditemukan');
        }
        $vm->update([
            'status' => 12,
        ]);

        if (isset($request->document)) {
            foreach ($request->document as $key => $v) {
                if (isset($v)) {
                    if (!($v ?? null)) {
                        throw new Exception('file tidak boleh kosong');
                    }

                    $file = $v;
                    $filename = "SERVICE_DONE_" . $id . "_" . date('Ymd_His') . '__' . $file->getClientOriginalName();
                    $file->move(public_path('files'), $filename);

                    DB::table('vehicle_maintenance_documents')->insert([
                        'header_id' => $id,
                        'create_by' => auth()->id(),
                        'name' => $file->getClientOriginalName(),
                        'file_name' => $filename,
                        'is_vehicle' => 1,
                        'extension' => $file->getClientOriginalExtension(),
                        'upload_date' => Carbon::now(),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }
        }
        $vehicle = Vehicle::find($exist->vehicle_id);
        $user = DB::table('users')->where('id', auth()->id())->first();
        DB::table('vehicle_maintenance_logs')
            ->insert([
                'vehicle_maintenance_id' => $id,
                'create_by' => auth()->id(),
                'title' => "Perawatan kendaraan $vehicle->nopol",
                'description' => "Telah berakhir dan kendaraan telah diterima oleh $user->name",
                'created_at' => Carbon::now()
            ]);
        DB::commit();
        return response()->json(['message' => 'Kendaraan sudah diterima, Perawatan kendaraan sudah selesai'], 200, [], JSON_NUMERIC_CHECK);
    }

    public function showVehicleServiceFinish($id)
    {
        $data['detail'] = DB::table('vehicle_maintenances')
            ->leftJoin('users', 'users.id', 'vehicle_maintenances.create_by')
            ->leftJoin('contacts as bengkel', 'bengkel.id', 'vehicle_maintenances.bengkel_id')
            ->leftJoin('maintenance_statuses', 'maintenance_statuses.id', 'vehicle_maintenances.status')
            ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
            ->select(
                'vehicle_maintenances.id',
                'vehicle_maintenances.name',
                'vehicle_maintenances.issue',
                'vehicle_maintenances.is_internal',
                'vehicle_maintenances.km_realisasi',
                'vehicle_maintenances.date_pengajuan',
                'vehicle_maintenances.date_rencana',
                'vehicle_maintenances.date_perawatan',
                'vehicle_maintenances.status',
                'vehicles.nopol',
                'users.name as creator_name',
                'maintenance_statuses.name as status_name',
                'vehicle_maintenances.description',
                'vehicle_maintenances.created_at',
                'bengkel.name as bengkel_name'
            )
            ->where('vehicle_maintenances.id', $id)
            ->first();

        $data['item'] = DB::table('vehicle_maintenance_items')
            ->leftJoin('items', 'items.id', 'vehicle_maintenance_items.item_id')
            ->where('vehicle_maintenance_items.vehicle_maintenance_id', $id)
            ->select('vehicle_maintenance_items.*', 'items.name as item_name')
            ->get();

        $data['document'] = DB::table('vehicle_maintenance_documents')
            ->where('vehicle_maintenance_documents.header_id', $id)
            ->where('is_rencana', 1)
            ->get();

        $data['document_vehicle'] = DB::table('vehicle_maintenance_documents')
            ->where('vehicle_maintenance_documents.header_id', $id)
            ->where('is_vehicle', 1)
            ->get();

        $data['message'] = 'OK';

        return response()->json($data);
    }

    /*
        End Perawatan section
    */

    public function logVehicleMaintenance()
    {
        $data['data'] = DB::table('vehicle_maintenance_logs')
        ->leftJoin('users', 'users.id', 'vehicle_maintenance_logs.create_by')
        ->select('vehicle_maintenance_logs.id', 'users.name as creator_name', 'vehicle_maintenance_logs.title', 'vehicle_maintenance_logs.description', 'vehicle_maintenance_logs.created_at', 'vehicle_maintenance_logs.vehicle_checklist_items_id', 'vehicle_maintenance_logs.vehicle_maintenance_id' )
        ->get();

        $data['message'] = 'OK';

        return response()->json($data);
    }

    public function showVehicle($id)
    {
        $data['data'] = DB::table('vehicles')
            ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
            ->leftJoin('vehicle_owners', 'vehicle_owners.id', 'vehicles.vehicle_owner_id')
            ->where('vehicles.id', $id)
            ->select('vehicles.*', 'vehicle_variants.name as vehicle_variant_name', 'vehicle_owners.name as vehicle_owner_name')
            ->first();

        $data['message'] = 'OK';

        return response()->json($data);
    }

    public function penerbitan_spk($vm_id)
    {
      $data['maintenance']=VehicleMaintenance::where('vehicle_maintenances.id', $vm_id)
      ->leftJoin('vehicles', 'vehicles.id', 'vehicle_maintenances.vehicle_id')
      ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id')
      ->leftJoin('vehicle_types', 'vehicle_types.id', 'vehicle_variants.vehicle_type_id')
      ->selectRaw('
        vehicle_maintenances.*,
        vehicles.code as vehicle_code,
        vehicle_types.name as vehicle_type
      ')
      ->first();
      $data['remark']=$this->remark;
      $data['roman'] = $this->numberToRoman(date('m'));
      return PDF::loadView('pdf.vehicle.penerbitan_spk', $data)
                // ->stream();
                ->download('SPK.pdf');
    }

    function numberToRoman($number) {
        $map = array('X' => 10, 'IX' => 9, 'V' => 5, 'IV' => 4, 'I' => 1);
        $returnValue = '';
        while ($number > 0) {
            foreach ($map as $roman => $int) {
                if($number >= $int) {
                    $number -= $int;
                    $returnValue .= $roman;
                    break;
                }
            }
        }
        return $returnValue;
      }
}
