<?php

namespace App\Exports\Operational;

use App\Http\Controllers\Operational\ReportController;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class ExportLaporanPenjualanCustomer implements FromView
{
    protected $request;
    public function __construct($request)
    {
        $this->request = $request;
    }
    public function view(): View
    {
        $request = $this->request;
        $resp['data'] = (new ReportController)->getLaporanPenjualanCustomer($request);
        return view('operational_report.laporan_penjualan_customer', $resp);
    }
}
