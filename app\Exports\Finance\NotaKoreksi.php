<?php

namespace App\Exports\Finance;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class NotaKoreksi implements FromView, WithStyles, ShouldAutoSize
{
    protected $data;
    
    public function __construct($data) {
        $this->data = $data;
    }

    public function view(): View
    {
        $params = [];
        $params['columns'] = $this->data;
        return view('finance/nota_koreksi_excel', $params);
    }

    public function styles(Worksheet $sheet)
    {

        $sheet->getStyle('A1:'.$sheet->getHighestColumn().'1')->getFont()->setBold(true);
        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . $sheet->getHighestRow())
        ->getBorders()
        ->getAllBorders()
        ->setBorderStyle(Border::BORDER_THIN)
        ->setColor(new Color('000000'));

        $styleArray = [
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . 1)
        ->applyFromArray($styleArray);

        foreach (self::excelColumnRange('A', 'ZZ') as $value) {
            $sheet->getColumnDimension($value)
            ->setAutoSize(true);
        }
    }

    private function excelColumnRange($lower, $upper) {
        ++$upper;
        for ($i = $lower; $i !== $upper; ++$i) {
            yield $i;
        }
    }
}
    