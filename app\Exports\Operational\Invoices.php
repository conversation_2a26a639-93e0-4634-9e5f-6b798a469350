<?php
namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use DB;

class Invoices implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Export\ExportExcelController;
        return $report_controller->getInvoices($this->request);
    }
    public function headings(): array
    {
        return [
            'Client / Customer',
            'No. WO',
            'No. Invoice',
            'Date Invoice',
            'Terms (Days)',
            'Currency',
            'Rate Price',
            'Discount',
            'PPN',
            'PPH',
            'Total',
            'No. Faktur <PERSON>jak',
            'No. PO',
            'No. Manifest',
            'Post by',
            'Post Date',
            // 'No. SJ',
            // 'No. JO',
            // 'Route',
            // 'Total',
            'Status',
            'Remark',
        ];
    }
    public function map($data): array
    {
        if($data->status == 1){
            $status = 'Diajukan';
        }elseif($data->status == 2){
            $status = 'Draft';
        }elseif($data->status == 3){
            $status = 'Posting';
        }elseif($data->status == 4){
            $status = 'Terbayar Sebagian';
        }elseif($data->status == 5){
            $status = 'Lunas';
        }elseif($data->status == 6){
            $status = 'Void';
        }


        $nojo = array();
        $nosj = array();
        $trayek = array();
        $nowo = array();
        $nopo = array();
        $nomanifest = array();
        $rateprice = array();

        $discount_total = 0;
        $total_ppn = 0;
        $sub_total =  0;

        foreach ($data->invoice_detail as $in){ 
            if($in->job_order != null){
                $nopo[] = $in->job_order->no_po_customer;
                $nojo[] = $in->job_order->code;
                $rateprice[] = $in->price;
            }
            
            if($in->manifest != null){
                $nomanifest[] = $in->manifest->code;
                $nosj[] = $in->manifest->no_sj_customer;
            }

            $discount_total += $in->discount;
            $total_ppn += $in->total_tax;
            $sub_total += $in->price * $in->qty;

            // $as = $in->job_order->trayek ? $in->job_order->trayek : null;

            $as = $in->job_order['trayek'] ?? null;
            
            if($as != null){
                $trayek[] = $in->job_order['trayek']->name;
            }
            
            $wo = $in->job_order['work_order'] ?? null;
            
            if($wo != null){
                $nowo[] = $in->job_order['work_order']->code;
            }
        }
        
        $grand_total = $sub_total + $total_ppn - $discount_total;

        $nosjs = implode(", ", $nosj);
        $nojos = implode(", ", $nojo);
        $trayeks = implode(", ", $trayek);
        $nomanifests = implode(", ", $nomanifest);
        $nopos = implode(", ", $nopo);
        $nowos = implode(", ", $nowo);
        $rateprices = implode(", ", $rateprice);
        $creator = DB::table('users')->where('id', $data->create_by)->first();
        
        return [
            $data->customer->name ?? '-',
            $nowos,
            $data->code ?? '-',
            // $data->invoice_detail[0]->manifest->no_sj_customer ?? '-',
            dateView($data->date_invoice) ?? '-',
            $data->termin ?? '-',
            'IDR',
            $data->sub_total,
            $discount_total,
            $data->ppnAmount ?? 0,
            $data->pphAmount ?? 0,
            // formatPrice($grand_total),
            $data->total ?? '-',
            $data->tax_invoices->code ?? '-',
            $nopos ?? '-',
            $nomanifests ?? '-',
            $creator->name ?? '-',
            dateView($data->created_at) ?? '-',
            // $nosjs,
            // $nojos,
            // $trayeks,
            // $data->invoice_detail[0]->job_order->code ?? '-',
            // $data->invoice_detail[0]->job_order->trayek->name ?? '-',
            $status ?? '-',
            $data->description ?? '-',
        ];
    }
}
