<?php

namespace App\Exports\Operational;

use App\Http\Controllers\Operational\ReportController;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class ExportJobOrderDetail implements FromView
{
    protected $request;
    public function __construct($request)
    {
        $this->request = $request;
    }
    public function view(): View
    {
        $request = $this->request;
        $resp['data'] = (new ReportController)->getJobOrderDetail($request);
        return view('operational_report.job_order_detail', $resp);
    }
}
