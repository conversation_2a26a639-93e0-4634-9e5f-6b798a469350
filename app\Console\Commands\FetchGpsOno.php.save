<?php
namespace App\Console\Commands;
use DateTime;
use App\Model\{Company,Contact,Vehicle,VehicleOwner,VehicleVariant,VehicleKmGpsLog};
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
class FetchGpsOno extends Command{
    protected $signature = 'gps:fetch-gps-ono {--token=} {--imei=} {--filter=} {--date=} {--akun=}';
    protected $description = 'Get All Vehicle from vendor GPS ONO';
    protected $link='https://fleetapi-id.cartrack.com/rest';
    public function __construct(){
        parent::__construct();
    }
    public function handle(){
       $this->trips();
    //    $this->vehicles();
    }
    public function vehicles(){
        $link=$this->link.'/vehicles';
        $this->info('Starting connect to ' . $link);
        $client = new \GuzzleHttp\Client();
        $req = $client->get($link,[
            'query'=>['limit'=>200],
            'headers' => ['Authorization' => env('GPS_BASIC_AUTH')]
        ]);
        $res = (json_decode($req->getBody(), true))['data'];
        $res=(collect($res));
        $param=$res->map(// params to cek distance
            fn($e)=>[
                'registration'=>$e['registration'],
                'start_timestamp'=>date('Y-m-d 00:00:00'),
                'end_timestamp'=>date('Y-m-d H:i:s')]
        );
        $ar=[];
        foreach ($param as $key => $value) {
            $this->info('Starting connect to ' . json_encode($value));
            $dis=$this->distance($value);
            sleep(1);
            array_push($ar,['registration'=>$dis['registration'],'distance'=>$dis['distance']]);
        }
        print_r($ar);
    }
    public function distance($params=null){//vehicles/{registration}/odometer
        $link=$this->link.'/vehicles/'.$params['registration'].'/odometer';
        $this->info('Starting connect to ' . $link);
        $client = new \GuzzleHttp\Client();
        try {
            unset($params['registration']);
            $req = $client->get($link,[
                'query'=>$params,
                'headers' => ['Authorization' => env('GPS_BASIC_AUTH')]
            ]);
            return $res = collect(json_decode($req->getBody(), true))->first();// arrays
        } catch (\GuzzleHttp\Exception\BadResponseException $e) {
           if($e->getResponse()->getStatusCode()){
                $this->info($e->getMessage());
           }
        }
    }
    public function trip($params=null){//vehicles/{registration}/odometer
        $link=$this->link.'/trip/'.$params['registration'];
        $this->info('Starting connect to ' . $link);
        $client = new \GuzzleHttp\Client();
        try {
            unset($params['registration']);
            $req = $client->get($link,[
                'query'=>$params,
                'headers' => ['Authorization' => env('GPS_BASIC_AUTH')]
            ]);
            return $res = collect(json_decode($req->getBody(), true))->first();// arrays
        } catch (\GuzzleHttp\Exception\BadResponseException $e) {
           if($e->getResponse()->getStatusCode()){
                $this->info($e->getMessage());
           }
        }
    }
    public function trips(){//vehicles/{registration}/odometer
        $params=[
            'start_timestamp'=>date('Y-m-d 00:00:00'),
            'end_timestamp'=>date('Y-m-d 23:59:59'),
            'limit'=>20
        ];
        $link=$this->link.'/trips';
        $this->info('Starting connect to ' . $link);
        $client = new \GuzzleHttp\Client();
        try {
            $req = $client->get($link,[
                'query'=>$params,
                'headers' => ['Authorization' => env('GPS_BASIC_AUTH')]
            ]);
            $res=json_decode($req->getBody(), true)['data'];
            // return $res = collect(json_decode($req->getBody(), true))->first();// arrays

            foreach ($res as $unit){
                DB::table('vehicle_km_gps_logs')->insert([
                    'imei'=>$unit['vehicle_id'],
                    'gsm_no'=>$unit['registration'],
                    'odometer'=>$unit['last_location']['odometer'],
                    'log_date'=>Carbon::now(),
                    'updated_at'=>Carbon::now(),
                    'lattitude'=>$unit['last_location']['lat'],
                    'longitude'=>$unit['last_location']['lng']
                ]);
                $v = Vehicle::where('nopol',str_replace(' ','',$unit['registration']))->where('is_trailer',0)->first();
                if ($v != null) {
                    $v->last_latitude=$unit['last_location']['lat'] ?? $v->last_latitude;
                    $v->last_longitude=$unit['last_location']['lng'] ?? $v->last_longitude;
                    $v->last_km_gps_date=Carbon::now();
                    $v->last_update_latlng=Carbon::now();
                    $v->last_km_date=Carbon::now();
                    $v->save();
                }
                sleep(4);
            }
        } catch (\GuzzleHttp\Exception\BadResponseException $e) {
           if($e->getResponse()->getStatusCode()){
                $this->info($e->getMessage());
           }
        }
    }
}
