<div class="col-md-8">
  <div class="ibox">
    <div class="ibox-title">
      <h5>Lapor<PERSON> Kas Perbandingan Baru</h5>
    </div>
    <form class="form-horizontal" ng-submit="export()">
      <div class="ibox-content">
        <div class="form-group">
          <label class="col-md-3 control-label">Branch</label>
          <div class="col-md-9">
            <select class="form-control" data-placeholder-text-single="'Semua Branch'" chosen allow-single-deselect="true" data-placeholder="Choose Header Akun" ng-model="formData.company_id" ng-options="s.id as s.name for s in data.company">
              <option value=""></option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-3 control-label">Periode 1</label>
          <div class="col-md-9">
            <div class="input-daterange input-group" style="width:100%;">
              <input type="text" class="input-sm form-control" ng-model="formData.start_date" required datepick/>
              <span class="input-group-addon">to</span>
              <input type="text" class="input-sm form-control" ng-model="formData.end_date" required datepick />
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-3 control-label">Periode 2</label>
          <div class="col-md-9">
            <div class="input-daterange input-group" style="width:100%;">
              <input type="text" class="input-sm form-control" ng-model="formData.start_date2" required datepick/>
              <span class="input-group-addon">to</span>
              <input type="text" class="input-sm form-control" ng-model="formData.end_date2" required datepick />
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-3 control-label">Tipe Dokumen</label>
          <div class="col-md-9">
              <select class="form-control" data-placeholder-text-single="'Tipe Dokumen'" chosen allow-single-deselect="false" data-placeholder="Choose Header Akun" ng-model="formData.tipe_dokumen" ng-options="s.id as s.name for s in tipe_dokumen">
                <option value=""></option>
              </select>
          </div>
        </div>

        <div class="form-group">
          <div class="col-md-3"> </div>
          <div class="col-md-3">
              <label for="prevent_zero">
                
                <input type="checkbox" ng-model='formData.prevent_zero' name="prevent_zero" id="prevent_zero" style="margin-right: 1.2mm">
                Tampilkan Tidak Nol
              </label>
          </div>
        </div>

      </div>
      <div class="ibox-footer">
        <a ui-sref="finance.report" class="btn btn-sm btn-warning">Kembali</a>
        <button type="submit" class="btn btn-sm btn-primary" ng-disabled="isExporting">
          <span ng-if="!isExporting">Export</span>
          <span ng-if="isExporting">
            <i class="fa fa-spinner fa-spin"></i> Exporting...
          </span>
        </button>
      </div>
    </form>
  </div>
</div>
