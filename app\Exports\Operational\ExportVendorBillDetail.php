<?php

namespace App\Exports\Operational;

use App\Http\Controllers\Operational\ReportController;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class ExportVendorBillDetail implements FromView
{
    protected $request;
    public function __construct($request)
    {
        $this->request = $request;
    }
    public function view(): View
    {
        $request = $this->request;
        $resp['data'] = (new ReportController)->getVendorBillDetail($request);
        return view('operational_report.vendor_bill_detail', $resp);
    }
}
