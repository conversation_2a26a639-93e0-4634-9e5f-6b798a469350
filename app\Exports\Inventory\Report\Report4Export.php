<?php

namespace App\Exports\Inventory\Report;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromView;

class Report4Export implements FromView
{
    protected $data;
    public function __construct(Collection $data)
    {
        $this->data = $data;
    }
    public function view(): View
    {
        $data = $this->data;
        
        return view('export.inventory.report.report4', ['data'=>$data]);
    }
}
