<?php

namespace App\Http\Controllers\Api\v6;

use App\Abstracts\AdditionalField;
use App\Abstracts\JobOrder as JO;
use App\Abstracts\WorkOrder as WO;
use App\Http\Controllers\Controller;
use App\Abstracts\Contact;
use App\Model\CombinedPrice;
use App\Model\CustomerJobOrder;
use App\Model\Inquery;
use App\Model\InqueryCustomer;
use App\Model\Lead;
use App\Model\LeadActivity;
use App\Model\PriceList;
use App\Model\Quotation;
use App\Model\QuotationCost;
use App\Model\QuotationDetail;
use App\Model\Service;
use App\Model\WorkOrder;
use App\Model\VendorPrice;
use App\User;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Response;
use Yajra\DataTables\Facades\DataTables;

class MarketingApiController extends Controller
{
    protected $warehouseServices = [12, 13, 14, 15];

    public function inquery_customer_datatable(Request $request)
    {
        $wr = "1=1";
        $item = InqueryCustomer::with('customer')->whereRaw($wr);
        $customer_id = $request->customer_id;
        $customer_id = $customer_id != null ? $customer_id : '';
        $item = $customer_id != '' ? $item->where('customer_id', $customer_id) : $item;
        $status = $request->status;
        $status = $status != null ? $status : '';
        $item = $status != '' ? $item->where('is_done', $status) : $item;
        $start_date = $request->start_date;
        $start_date = $start_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $start_date) : '';
        $end_date = $request->end_date;
        $end_date = $end_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $end_date) : '';
        $item = $start_date != '' && $end_date != ' ' ? $item->whereBetween('created_at', [$start_date, $end_date]) : $item;
        $item = $item->select('inquery_customers.*');

        $item = $item->orderBy('created_at', 'desc');
        $item = $item->paginate($request->length);
        return response()->json($item);

        // return DataTables::of($item)
        //     ->addColumn('action', function ($item) {
        //         $html = '';
        //         if ($item->is_done == 0) {
        //             $html .= "<a ui-sref=\"marketing.inquery.create_inquery_customer({id:$item->id})\"><span class='fa fa-check'  data-toggle='tooltip' title='Approve'></span></a>&nbsp;&nbsp;";
        //         }
        //         $html .= "<a ui-sref=\"marketing.inquery_customer.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //         return $html;
        //     })
        //     ->addColumn('action_customer', function ($item) {
        //         $html = "<a ui-sref=\"main.inquery.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //         return $html;
        //     })
        //     ->editColumn('created_at', function ($item) {
        //         return dateView($item->created_at);
        //     })
        //     ->editColumn('is_done', function ($item) {
        //         $stt = [
        //             0 => 'Diajukan',
        //             1 => 'Diproses',
        //         ];
        //         return $stt[$item->is_done];
        //     })
        //     ->rawColumns(['action', 'action_customer'])
        //     ->make(true);
    }

    public function activity_work_order(Request $request)
    {
        $wr = "1=1";
        if ($request->customer_id) {
            $wr .= " AND wo.customer_id = $request->customer_id";
        }
        if ($request->company_id) {
            $wr .= " AND wo.company_id = $request->company_id";
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " and wo.date between '$start' and '$end'";
        }
        if ($request->is_admin == 0) {
            $wr .= " AND wo.company_id = $request->user_company_id";
        }

        $wr .= " AND wo.status != 3";

        $item = DB::table('work_orders as wo')
            ->leftJoin('contacts', 'contacts.id', 'wo.customer_id')
            ->leftJoin('companies', 'companies.id', 'wo.company_id')
            ->leftJoin(DB::raw("(select sum(distinct iv.grand_total) as grand_total,group_concat(distinct iv.code) as code,group_concat(distinct iv.date_invoice) as date_invoice, job_orders.work_order_id from invoices as iv left join invoice_details as ivd on ivd.header_id = iv.id left join job_orders on job_orders.id = ivd.job_order_id group by job_orders.work_order_id) as Y"), 'Y.work_order_id', 'wo.id')
            ->leftJoin(DB::raw("(select sum(if(joc.type=1,joc.total_price,0)) as operasional,sum(if(joc.type=2,joc.total_price,0)) as reimburse, jo.work_order_id from job_order_costs as joc left join job_orders as jo on jo.id = joc.header_id where joc.status in (3,5,8) group by jo.work_order_id) as X"), 'X.work_order_id', 'wo.id')
            ->leftJoin('view_work_order_costs', 'view_work_order_costs.id', 'wo.id')
            ->whereRaw($wr)
            ->selectRaw("
            distinct
            wo.code as code_wo,
            wo.date as date_wo,
            wo.id as wo_id,
            ifnull(Y.grand_total,0) as invoice_price,
            ifnull(X.operasional,0) as operational_price,
            ifnull(X.reimburse,0) as talangan_price,
            Y.code as code_invoice,
            Y.date_invoice,
            contacts.name as customer,
            concat('') as description,
            if(Y.grand_total is not null,ifnull(Y.grand_total,0)-ifnull(X.operasional,0)-ifnull(X.reimburse,0),0) as profit,
            if(Y.grand_total is not null,round((ifnull(Y.grand_total,0)-ifnull(X.operasional,0)-ifnull(X.reimburse,0))/ifnull(Y.grand_total,0)*100,2),0) as presentase,
            companies.name AS company,
            FORMAT(view_work_order_costs.operasional, 2) as operasional
            ");

        $item = $item->orderBy('date_invoice', 'desc');
        $item = $item->paginate($request->length);
        return response()->json($item);
    }

    public function activity_job_order(Request $request)
    {
        $wr = "1=1";
        JO::setNullableAdditionals();

        if ($request->customer_id) {
            $wr .= " AND job_orders.customer_id = $request->customer_id";
        }
        if ($request->company_id) {
            $wr .= " AND job_orders.company_id = $request->company_id";
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " and date(job_orders.shipment_date) between '$start' and '$end'";
        }
        if ($request->service_id) {
            $wr .= " and job_orders.service_id = $request->service_id";
        }

        $wr .= " and job_orders.is_cancel = 0";

        $params = [];
        $params['show_in_job_order_summary'] = 1;
        $jobOrderAdditional = AdditionalField::indexKey('jobOrder', $params);
        $additionalColumn = '';
        if (count($jobOrderAdditional) > 0) {
            foreach ($jobOrderAdditional as $i => $v) {
                $additionalColumn .= ',';
                $additionalColumn .= "REPLACE(JSON_EXTRACT(job_orders.additional, '$.$v'), '\"', '') AS $v";
            }
        }
        $sql = "
        SELECT
        job_orders.id,
        job_orders.shipment_date as date_jo,
        job_orders.code as code_jo,
        contacts.name as customer,
        services.name as service,
        froms.name as city_from,
        tos.name as city_to,
        ifnull(X.qty,0) as qty,
        job_orders.total_price as operational,
        ifnull(Y.total,0) as biaya,
        ifnull(Y.base_price,0) as base_price,
        job_orders.total_price - ifnull(Y.total,0) as profit,
        job_orders.description as description,
        null as satuan
        $additionalColumn
        FROM
        job_orders
        LEFT JOIN routes ON job_orders.route_id = routes.id
        LEFT JOIN cities as froms ON routes.city_from = froms.id
        LEFT JOIN cities as tos ON routes.city_to = tos.id
        LEFT JOIN services ON job_orders.service_id = services.id
        LEFT JOIN contacts ON job_orders.customer_id = contacts.id
        LEFT JOIN (select header_id, sum(total_price) as total, sum(qty) as qty from job_order_details group by header_id) X on X.header_id = job_orders.id
        LEFT JOIN (select joc.header_id, sum(joc.total_price) as total, sum(joc.qty) as qty, sum(ct.cost*joc.qty) as base_price from job_order_costs as joc inner join cost_types as ct on ct.id = joc.cost_type_id where joc.type = 1 group by joc.header_id) Y on Y.header_id = job_orders.id
        WHERE $wr
        ORDER BY job_orders.created_at desc
        ";
        $item = DB::select($sql);
        $page = $request->input('page', 1);
        $perPage = $request->input('length', 10);
        $items = new \Illuminate\Pagination\LengthAwarePaginator(
            array_slice($item, ($page - 1) * $perPage, $perPage),
            count($item),
            $perPage,
            $page,
            ['path' => url()->current()]
        );

        return response()->json($items);
    }

    public function customer_order_datatable(Request $request)
    {
        $query = CustomerJobOrder::query();

        if (isset($request->customer_id)) {
            $query->where('customer_id', $request->customer_id);
        }

        $results = $query->paginate($request->length);

        return response()->json($results);
    }

    public function work_order_datatable(Request $request)
    {
        WO::setNullableAdditionals();
        $wr = "1=1";
        // $start_date = $request->start_date;
        // $start_date = $start_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $start_date) : '';
        // $end_date = $request->end_date;
        // $end_date = $end_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $end_date) : '';
        // $wr .= ($start_date != '' && $end_date != ' ') ? " AND work_orders.date BETWEEN '$start_date' AND '$end_date'" : '';
        if ($request->customer_id) {
            $wr .= " AND work_orders.customer_id = $request->customer_id";
        }

        if (isset($request->is_invoice)) {
            $wr .= " AND work_orders.is_invoice = {$request->is_invoice}";
        }
        if ($request->wo_done) {
            $wr .= " AND work_orders.status = 2";
        }
        if ($request->status) {
            $wr .= " AND work_orders.status = $request->status";
        }

        if ($request->filled('company_id')) {
            $wr .= " AND work_orders.company_id = $request->company_id";
        }

        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $user_company_id = auth()->user()->company_id;
            $wr .= " AND work_orders.company_id = $user_company_id";
        }

        if ($request->filled('is_invoice')) {
            if ($request->is_invoice == 0) {
                $wr2 = 'work_orders.invoice_id IS NULL AND work_orders.is_job_packet = 1';
                if ($request->customer_id) {
                    $wr2 .= " AND work_orders.customer_id = $request->customer_id";
                }
                $wr = "($wr) OR ($wr2)";
            }
        }

        $work_order_jobs = DB::raw('(SELECT work_order_id, COUNT(id) AS total_jo FROM job_orders GROUP BY work_order_id) AS work_order_jobs');

        $item = WorkOrder::with('customer:id,name', 'company:id,name', 'quotation')
            ->leftJoin($work_order_jobs, 'work_order_jobs.work_order_id', 'work_orders.id')
            ->leftJoin('quotations', 'quotations.id', '=', 'work_orders.quotation_id')
            ->leftJoin(DB::raw("(select work_order_id, sum(IF(invoice_job_order.invoice_id is null,1,0)) as total_no_invoice from job_orders left join invoice_job_order ON invoice_job_order.job_order_id = job_orders.id group by work_order_id) jos"), "jos.work_order_id", "=", "work_orders.id")
            ->leftJoin(DB::raw('(select jo.work_order_id,group_concat(distinct no_po_customer) as po_customer, CONCAT("<ul>", group_concat(distinct "<li>", invoices.code, "</li>" SEPARATOR "<br>"), "</ul>") as invoice_code from job_orders as jo LEFT JOIN invoice_details ON invoice_details.job_order_id = jo.id LEFT JOIN invoices ON invoices.id = invoice_details.header_id group by jo.work_order_id) jo'), 'jo.work_order_id', 'work_orders.id')
            ->whereRaw($wr);

        if ($request->start_date) {
            $item->whereDate('work_orders.date', '>=', dateDB($request->start_date));
        }
        if ($request->end_date) {
            $item->whereDate('work_orders.date', '<=', dateDB($request->end_date));
        }
        $item->selectRaw('work_orders.*, jos.total_no_invoice, jo.po_customer, jo.invoice_code, COALESCE(work_order_jobs.total_jo, 0) AS total_jo, quotations.no_contract');

        $use_job_packet = \App\Http\Controllers\Setting\SettingController::fetch('work_order', 'use_job_packet');
        if (!$use_job_packet->value) {
            $item->where('work_orders.is_job_packet', 0);
        }

        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('workOrder', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(work_orders.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM work_orders) AS additional_work_orders";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_work_orders.id', 'work_orders.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_work_orders.' . $a]);
            }
        }

        $user = $request->user();
        
        $item = $item->orderBy('date', 'desc');
        $item = $item->paginate($request->length);
        return response()->json($item);

        // return DataTables::of($item)
        //     ->addColumn('action', function ($item) use ($user) {
        //         $html = '';

        //         if ($user->hasRole('marketing.work_order.detail')) {
        //             $html .= "<a ui-sref='marketing.work_order.show({id:$item->id})'><span class='fa fa-folder-o' data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //         }

        //         if ($item->status == 1 && $user->hasRole('marketing.work_order.edit')) {
        //             $html .= "<a ui-sref='marketing.work_order.edit({id:$item->id})'><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
        //             $html .= "<a ng-click='void($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Void Data'></span></a>";
        //         }

        //         // if ($item->total_job_order < 1 && $user->hasRole('marketing.work_order.delete')) {
        //         //     $html .= "<a ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
        //         // }

        //         return $html;
        //     })
        //     ->addColumn('action_operasional', function ($item) use ($user) {
        //         $html = '';

        //         if ($user->hasRole('operational.work_order.detail')) {
        //             $html = "<a ui-sref='marketing.work_order.show({id:$item->id})'><span class='fa fa-folder-o' data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //         }

        //         return $html;
        //     })
        //     /*
        //         Date : 03-11-2022
        //         Description : Membawa value id dan code pada checkbox
        //         Developer : Sendy
        //         Status : Edit
        //     */

        //     ->addColumn('checklist', function ($item) {
        //         return "<div class='checkbox checkbox-primary checkbox-inline' ><input type='checkbox' ng-model='myCheckbox.val$item->id' ng-true-value='(\"$item->id,$item->code\")' ng-false-value='0'><label for='tr-" . $item->id . "'></label></div>";
        //     })
        //     ->addColumn('action_choose', function ($item) {
        //         $html = "<a ng-click='selectWO($item->id,\"$item->code\")' class='btn btn-xs btn-success'>Pilih</a>";
        //         return $html;
        //     })
        //     ->editColumn('status', function ($item) {
        //         $stt = [
        //             1 => 'Proses',
        //             2 => 'Selesai',
        //             3 => 'Void',
        //         ];
        //         return $stt[$item->status];
        //     })
        //     ->editColumn('date', function ($item) {
        //         return dateView($item->date, true);
        //     })
        //     ->editColumn('no_bl', function ($item) {
        //         $str = "";
        //         $explode = explode(',', $item->no_bl);
        //         foreach ($explode as $key => $value) {
        //             $str .= "$value<br>";
        //         }
        //         return $str;
        //     })
        //     ->editColumn('aju_number', function ($item) {
        //         $str = "";
        //         $explode = explode(',', $item->aju_number);
        //         foreach ($explode as $key => $value) {
        //             $str .= "$value<br>";
        //         }
        //         return $str;
        //     })
        //     ->rawColumns(['checklist', 'action', 'action_choose', 'aju_number', 'no_bl', 'action_operasional', 'invoice_code'])
        //     ->make(true);
    }

}
