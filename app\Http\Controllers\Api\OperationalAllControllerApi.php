<?php

namespace App\Http\Controllers\Api;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

/*  Date : 10-09-2022 s/d 13-09-2022 By : <PERSON>
    Description : [Improve] Report Operational : Operational All [untuk dept Finance Accounting]
    Keterangan : Membuat Api untuk Controller Datatable Operational All[Finance Accounting]
*/

class OperationalAllControllerApi extends Controller {
public function get_all_report_datatable(Request $request) {
    $wr = "jo.id is not null";

    $costs = DB::raw("(
        SELECT SUM(IFNULL(mc.total_price,0)) as cost_total,mc.header_id as manifest_id
        FROM manifest_costs as mc
        GROUP BY mc.header_id
        ) as costs");

    if ($request->start_date && $request->end_date) {
        $startDate=Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate=Carbon::parse($request->end_date)->format('Y-m-d');
        $wr.=" and wo.date between '$startDate' and '$endDate'";
    }

    $data = DB::table('work_orders as wo')
    ->leftJoin('work_order_details as wod', 'wo.id', 'wod.header_id')
    ->leftJoin('quotations as qt', 'wo.quotation_id', 'qt.id')
    ->leftJoin('quotation_details as qd', 'wod.quotation_detail_id', 'qd.id')
    ->leftJoin('impositions as imp', 'imp.id', 'qd.imposition')
    ->leftJoin('pieces as p', 'p.id', 'qd.piece_id')
    ->leftJoin('contacts as c', 'wo.customer_id', 'c.id')
    ->leftJoin('job_orders as jo','jo.work_order_detail_id','wod.id')
    ->leftJoin('job_order_details as jod','jod.header_id','jo.id')
    ->leftJoin('job_order_costs as joc','joc.header_id','jo.id')
    ->leftJoin('services as s', 's.id', 'jo.service_id')
    ->leftJoin('service_groups as sg', 'sg.id', 's.service_group_id')
    ->leftJoin('manifest_costs AS mco', 'mco.id', 'joc.manifest_cost_id')
    ->leftJoin('users AS us','us.id','wo.updated_by')
    ->leftJoin('manifest_details as madet','madet.job_order_detail_id','jod.id')
    ->leftJoin('manifests as M','M.id','madet.header_id')
    ->leftJoin($costs,'costs.manifest_id','M.id')
    ->leftJoin('companies AS comp','comp.id','us.company_id')
    ->leftJoin('routes AS ro', 'ro.id','jo.route_id')
    // ->leftJoin('contacts as driver','driver.id','jod.sender_id')
    // ->leftJoin('contacts as driver2','driver2.id','jod.receiver_id')
    ->leftJoin('cities as city','city.id','ro.city_from')
    ->leftJoin('cities as city2','city2.id','ro.city_to')
    ->leftJoin('contacts as driver3','driver3.id','M.driver_id')
    ->leftJoin('vehicles as v','v.id','M.vehicle_id')
    ->leftJoin('contacts','contacts.id','wo.customer_id')
    ->leftJoin(DB::raw("(select sum(distinct iv.grand_total) as grand_total,group_concat(distinct iv.code) as code,group_concat(distinct iv.date_invoice) as date_invoice, job_orders.work_order_id from invoices as iv left join invoice_details as ivd on ivd.header_id = iv.id left join job_orders on job_orders.id = ivd.job_order_id group by job_orders.work_order_id) as Y"),'Y.work_order_id','wo.id')
    ->leftJoin(DB::raw("(select sum(if(joc.type=1,joc.total_price,0)) as operasional,sum(if(joc.type=2,joc.total_price,0)) as reimburse, jo.work_order_id from job_order_costs as joc left join job_orders as jo on jo.id = joc.header_id where joc.status in (3,5,8) group by jo.work_order_id) as X"),'X.work_order_id','wo.id')
    ->leftJoin('delivery_manifests AS deman', 'deman.manifest_id', 'M.id')
    ->leftJoin('delivery_order_drivers as dod', function($join){
        $join->on('dod.id','deman.delivery_order_driver_id');
        $join->where('dod.status', '<', 3);
        })
    ->whereRaw($wr)
    ->selectRaw("
        wo.*,
        wo.date AS date_wo,
        sum(if(joc.type=1 and joc.status in(3,5,8),joc.total_price,0)) as operational_price,
        if(qt.bill_type=2,(wo.qty*qt.price_full_contract),sum(distinct jo.total_price)) as invoice_price,
        c.name AS customer_name,
        s.name AS service_name,
        dod.code AS code_surat_jalan,
        dod.is_finish AS opr_jo,
        sum(joc.total_price) as operational_cost,
        joc.total_price AS base_price,
        jod.receive_date as dt,
        costs.cost_total AS cost_driver,
        sg.id as service_group_id,
        jo.price AS ma_rev,
        GROUP_CONCAT(DISTINCT M.code) AS manifest,
        M.created_at AS manifest_date,
        jo.code AS no_jo,
        jo.created_at AS date_jo,
        jo.shipment_date,
        city.name AS oad,
        city2.name AS dad,
        ro.name AS ron,
        mco.price AS mcop,
        comp.name AS branch,
        GROUP_CONCAT(DISTINCT driver3.name) AS drv,
        GROUP_CONCAT(DISTINCT v.nopol) AS nop,
        Y.code as code_invoice,
        Y.date_invoice AS datin,
        ifnull(X.reimburse,0) as talangan_price,
        if(Y.grand_total is not null,ifnull(Y.grand_total,0)-ifnull(X.operasional,0)-ifnull(X.reimburse,0),0) as profit,
        if(Y.grand_total is not null,round((ifnull(Y.grand_total,0)-ifnull(X.operasional,0)-ifnull(X.reimburse,0))/ifnull(Y.grand_total,0)*100,2),0) as presentase
        ")
    ->groupBy('wo.id','jo.id')
    ->orderBy('date_wo','desc')->get();

    return DataTables::of($data)
      ->make(true);
  }
}
// Close [Improve] Report Operational : Operational All [untuk dept Finance Accounting]
