<?php
namespace App\Abstracts;
use Carbon\Carbon;
use Exception;
use App\Model\WarehouseReceipt AS WR;
use App\Abstracts\Setting\Email;
use App\Abstracts\Inventory\WarehouseReceiptDetail;
use App\Abstracts\Inventory\WarehouseReceiptStatus;
use App\Abstracts\Inventory\ItemMigrationReceipt;
use App\Abstracts\Inventory\VoyageReceipt;
use App\Abstracts\Inventory\PurchaseOrderStatus;
use App\Abstracts\Finance\Payable;
use App\Abstracts\PurchaseOrder;
use App\Model\PurchaseOrder AS PO;
use App\Model\WarehouseStockDetail;
use App\Utils\TransactionCode;
use App\Abstracts\Inventory\StockTransaction;
use App\Abstracts\Inventory\WarehouseStockDetail as InventoryWarehouseStockDetail;
use App\Abstracts\Tyms\VulkanisirRequest;
use App\Model\PurchaseOrderDetail;
use App\Model\PurchaseRequestDetail;
use App\Model\WarehouseReceiptDetail as WRD;
use App\Model\WarehouseStockDetail as WSD;
use App\Model\TypeTransaction;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Model\CashTransaction;
use App\Model\CashTransactionDetail;
use App\Model\VulkanisirRequestDetail;
use Image;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Response;
class WarehouseReceipt
{
    protected static $table = 'warehouse_receipts';
    public static function setEmptyDescription() {
        $dt = DB::table(self::$table);
        $dt = $dt->where('description', 'null');
        $dt = $dt->orWhere('description', 'NULL');
        $dt = $dt->update([
            'description' => null
        ]);
    }
    public static function query($params = []) {
        $request = self::fetchFilter($params);
        $dt = DB::table(self::$table);
        if($request['code']) {
            $dt->where(self::$table . '.code', $request['code']);
        }
        return $dt;
    }
    public static function fetchFilter($args = []) {
        $params = [];
        $params['code'] = $args['code'] ?? null;
        return $params;
    }
    /*
      Date : 16-03-2021
      Description : Menampilkan foto
      Developer : Didin
      Status : Create
    */
    public static function showAttachment($receipt_id) {
        $url = asset('files');
        $attachment = DB::table('delivery_order_photos')
        ->whereReceiptId($receipt_id)
        ->selectRaw("id, CONCAT('$url/', `name`) AS `name`")
        ->get();
        return $attachment;
    }
    /*
      Date : 29-08-2020
      Description : Mengirim email
      Developer : Didin
      Status : Create
    */
    public static function sendEmail($id) {
        $emailSetting = Email::show();
        $dt = DB::table('warehouse_receipts')
        ->join('contacts', 'warehouse_receipts.customer_id', 'contacts.id')
        ->where('warehouse_receipts.id', $id)
        ->select('contacts.email', 'contacts.name AS customer_name')
        ->first();
        $email = $dt->email;
        $email = str_replace(',', ';', $email);
        $destination = $email;
        $destination_name = $dt->customer_name;
        $subject = $emailSetting->receipt_subject;
        $body = self::previewEmail($id);
        Email::send($subject, $destination, $destination_name, $body);
    }
    /*
      Date : 29-08-2020
      Description : Menampilkan preview email
      Developer : Didin
      Status : Create
    */
    public static function previewEmail($id) {
        $wr = DB::table('warehouse_receipts')
        ->whereId($id)
        ->first();
        $images = self::showAttachment($id)->pluck('name');
        $frame = '';
        foreach ($images as $image) {
            $frame .= "<a href='$image' style='display:inline-block;width:60mm;margin-right:50mm;margin-bottom:5mm'><img src='$image' style='height:60mm;width:auto' /></a>";
        }
        $email = Email::show();
        $warehouseReceipt = new \App\Http\Controllers\OperationalWarehouse\ReceiptController();
        $stocklist = $warehouseReceipt->print($id);
        $body = '<div>';
        $body .= $email->receipt_body;
        $body .= '<br>';
        $body .= '<hr>';
        $body .= $frame;
        $body .= '</div>';
        return $body;
    }
    /*
      Date : 05-03-2021
      Description : Mengambil parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($args) {
        $params = [];
        $params['detail'] = $args['detail'] ?? null;
        $params['files'] = $args['files'] ?? null;
        $params['ttd'] = $args['ttd'] ?? null;
        $params['warehouse_staff_id'] = $args['warehouse_staff_id'] ?? null;
        $params['create_by'] = $args['create_by'] ?? null;
        $params['company_id'] = $args['company_id'] ?? null;
        $params['purchase_order_id'] = $args['purchase_order_id'] ?? null;
        $params['purchase_order_retur_id'] = $args['purchase_order_retur_id'] ?? null;
        $params['customer_id'] = $args['customer_id'] ?? null;
        $params['receipt_type_id'] = $args['receipt_type_id'] ?? null;
        $params['is_seen'] = $args['is_seen'] ?? null;
        if(!$params['receipt_type_id']) {
            $receipt_type_code = $args['receipt_type_code'] ?? null;
            if($receipt_type_code) {
                $rt = ReceiptType::showByCode($receipt_type_code);
                if($rt) {
                    $params['receipt_type_id'] = $rt->id;
                }
            }
        }
        $params['is_overtime'] = $args['is_overtime'] ?? null ?? 0;
        $params['warehouse_id'] = $args['warehouse_id'] ?? null;
        $params['city_to'] = $args['city_to'] ?? null;
        $params['sender'] = $args['sender'] ?? null;
        $params['receiver'] = $args['receiver'] ?? null;
        $params['reff_no'] = $args['reff_no'] ?? null;
        $params['is_export'] = $args['is_export'] ?? null;
        $params['description'] = $args['description'] ?? null;
        $params['nopol'] = $args['nopol'] ?? null;
        $params['driver'] = $args['driver'] ?? null;
        $params['phone_number'] = $args['phone_number'] ?? null;
        $params['vehicle_type_id'] = $args['vehicle_type_id'] ?? null;
        $params['is_pallet'] = $args['is_pallet'] ?? null;
        $params['status'] = $args['status'] ?? null;
        $params['receive_date'] = $args['receive_date'] ?? null;
        $params['receive_time'] = $args['receive_time'] ?? null;
        $params['user_id'] = $args['user_id'] ?? null;
        $params['jabatan'] = $args['jabatan'] ?? null;
        $params['item_condition'] = $args['item_condition'] ?? null;
        $params['vulkanisir_request_id'] = $args['vulkanisir_request_id'] ?? null;
        if($params['receive_date'] && $params['receive_time']) {
            $params['receive_date'] = createTimestamp($params['receive_date'], $params['receive_time']);
        }
        return $params;
    }
    /*
      Date : 05-10-2021
      Description : Menyimpan data (29-08-2021, Didin)
      Description : Merapikan koding dan menyesuaikan if sebelum buat Purchase Order
      Developer : Lyo
      Status : Edit
    */
    public static function store($params) {
        $request = self::fetch($params);
        foreach($request as $key => $r){
            if(in_array($r, ['null', 'NULL', 'Null'])){
                $request[$key] = null;
            }
        }
        if(is_string($request['detail'])) {
            $detail = json_decode($request['detail']);
        }
        else {
            $detail = $request['detail'];
        }
        if($request['status'] == 1) {
            $option = [
                'warehouse_id' => $request['warehouse_id']
            ];
            $cek_kapasitas = WarehouseStockDetail::cek_kapasitas($detail, $option);
            $cek_pallet_keluar = WarehouseStockDetail::cek_pallet_keluar($detail, $option);
            if($cek_kapasitas !== true) {
                return $cek_kapasitas;
            }
            else if($cek_pallet_keluar !== true){
                return $cek_pallet_keluar;
            }
        }
        $attachment = [];
        if($request['files']) {
            $file=$request['files'];
            if(is_array($file)) {
                $c = 0;
                foreach($file as $image) {
                    $origin = $image->getClientOriginalName();
                    $filename = 'LAMPIRAN_PENERIMAAN_BARANG' . date('Ymd_His') . $c . $origin;
                    array_push($attachment, $filename);
                    $img = Image::make($image->getRealPath());
                    $img->resize(600, null, function ($constraint) {
                        $constraint->aspectRatio();
                    })->save(public_path('files/' . $filename));
                    $c++;
                }
            }
        }
        if($request['ttd']) {
            $ttd_file = $request['ttd'];
            $ext = $ttd_file->getClientOriginalExtension();
            if( $ext == null OR $ext == '') {
            $ext = 'png';
            }
            $ttd = 'TTD' . date('Ymd_His') . str_random(10) . '.' . $ext;
            Image::make( $ttd_file->getRealPath() )->save(public_path('files/' . $ttd));
        } else {
            $ttd = null;
        }
        if($request['receiver'] == 'undefined') {
            $request['receiver'] = null;
        }
        if($request['is_seen'] == 0) {
            $is_seen=0;
            $code_data= 'DRAFT - '.date('Ymd-His').rand(10,99);
        }
        else{
            $is_seen = 1;
            $code = new TransactionCode($request['company_id'], 'warehouseReceipt');
            $code->setCode();
            $trx_code = $code->getCode();
            $code_data=$trx_code;
        }
        $warehouse_receipt_id = DB::table(self::$table)->insertGetId([
            'company_id' => $request['company_id'],
            'purchase_order_id' => $request['purchase_order_id'],
            'customer_id' => $request['customer_id'],
            'receipt_type_id' => $request['receipt_type_id'],
            'is_overtime' => $request['is_overtime'] ?? 0,
            'warehouse_id' => $request['warehouse_id'],
            'city_to' => $request['city_to'],
            'sender' => $request['sender'],
            'receiver' => $request['receiver'],
            'warehouse_staff_id' => auth()->id(),
            // 'collectible_id' => $request['collectible_id'],
            'reff_no' => $request['reff_no'],
            'code' => $code_data,
            'receive_date' => $request['receive_date'],
            'is_export' => $request['is_export'],
            'description' => $request['description'],
            'create_by' => auth()->id(),
            'nopol' => $request['nopol'],
            'driver' => $request['driver'],
            'ttd' => $ttd,
            'phone_number' => $request['phone_number'],
            'vehicle_type_id' => $request['vehicle_type_id'],
            'status' => $request['status'],
            'user_id' => $request['user_id'],
            'jabatan' => $request['jabatan'],
            'item_condition' => $request['item_condition'],
            'vulkanisir_request_id' => $request['vulkanisir_request_id'],
            'is_seen' => $is_seen
        ]);
        self::storeShipmentStatus($warehouse_receipt_id);
        self::storePenerima(($request['receiver'] ?? 'N/C'), $request['city_to'], $request['company_id']);
        // Validasi handling area
        if(is_array($detail)) {
            if(count($detail) > 0) {
                foreach ($detail as $value) {
                    // dd($value);
                    // die();
                    if (empty($value)) {
                        continue;
                    }
                    $params = (array) $value;
                    $params["is_pallet"] = $request["is_pallet"] ?? 0;
                    $params["purchase_order_id"] = $request['purchase_order_id'] ?? null;
                    $params["status"] = $request['status'] ?? null;
                    $params["warehouse_stock_detail_id"] = $value->warehouse_stock_detail_id ?? null;
                    $params['is_initial_stock'] = $params['is_initial_stock'] ?? 0;
                    //check item for update customer order status
                    if($is_seen == 0){
                        $params["is_from_co"] = 1;
                    }
                    else{
                        $params["is_from_co"] = 0;
                    }
                    $purchase_order_detail= PurchaseOrderDetail::where('item_id',$value->item_id)->where('header_id',$request['purchase_order_id'])->first();
                    if($purchase_order_detail != null){
                        $purchase_req= PurchaseRequestDetail::where('id',$purchase_order_detail->purchase_request_detail_id)->first();
                        if($purchase_req && $purchase_req->customer_order_details_id != null){
                            $params["is_from_co"] = 1;
                        }
                    }
                    $wrd_id = WarehouseReceiptDetail::store($params, $warehouse_receipt_id);
                    if($request['status'] == 1 && isset($value->item_id)) {
                        DB::table('items')->whereId($value->item_id)->update([
                          'wide' => ($value->wide ?? 0) ,
                          'long' => ($value->long ?? 0) ,
                          'height' => ($value->high ?? 0) ,
                          'volume' => ($value->wide ?? 0) * ($value->long ?? 0) * ($value->high ?? 0) / 1000000,
                          'tonase' => $value->weight
                        ]);
                        // GENERATE EMBOSS BAN LUAR
                        $banLuar = WarehouseStockDetail::select('warehouse_stock_details.*')
                                                    ->join('items', 'items.id', 'warehouse_stock_details.item_id')
                                                    ->join('categories', 'items.category_id', 'categories.id')
                                                    ->where('warehouse_receipt_id', $warehouse_receipt_id)
                                                    ->where('warehouse_receipt_detail_id', $wrd_id)
                                                    ->where('categories.is_ban_luar', 1)
                                                    ->first();
                        //UPDATE NO SERI BAN LUAR
                        if($banLuar && $params['is_initial_stock']==0){
                            if(isset($value->no_seri)){
                                $seri=$value->no_seri;
                            }
                            else{
                                $seri=null;
                            }
                         DB::table('warehouse_stock_details')->where('id',$banLuar->id)->update([
                          'serial_no' => $seri
                            ]);
                        }
                        // dd($banLuar, $warehouse_receipt_id, $wrd_id);
                        if($banLuar && $params['is_initial_stock']==0 && $request['vulkanisir_request_id'] == null){
                            $setEmboss = WarehouseStockDetail::find($banLuar->id);
                            if($setEmboss){
                                InventoryWarehouseStockDetail::setEmbossBanLuar($setEmboss->id);
                            }
                        }
                        else if($banLuar && $request['vulkanisir_request_id']){ //if vulkanisir
                            $vr = VulkanisirRequestDetail::
                                        join('warehouse_stock_details', 'warehouse_stock_details.id', 'vulkanisir_request_details.wh_stock_ban_luar_id')
                                        ->where('vulkanisir_request_details.header_id', $request['vulkanisir_request_id'])
                                        ->where('wh_stock_ban_luar_id', $params['warehouse_stock_detail_id'])
                                        ->select('warehouse_stock_details.id','warehouse_stock_details.tire_batch_no')
                                        ->first();
                            if($vr){
                                $oldEmboss = $vr->tire_batch_no;
                                $oldBanLuar = WarehouseStockDetail::find($vr->id);
                                $wsdBanLuar = WarehouseStockDetail::find($banLuar->id);
                                $wsdBanLuar->tire_batch_no      = $oldEmboss;
                                $wsdBanLuar->vulkanisir_count   = $oldBanLuar->vulkanisir_count + 1;
                                $wsdBanLuar->save();
                                // update old stock
                                $oldBanLuar->qty = 0;
                                $oldBanLuar->available_qty = 0;
                                $oldBanLuar->onhand_qty = 0;
                                $oldBanLuar->save();
                            }
                        }
                    }
                    //if po retur
                    if($request['purchase_order_retur_id'] != "null" || $request['purchase_order_retur_id'] != null){
                        $retur_detail= DB::table('retur_details')->where('header_id',$request['purchase_order_retur_id'])->where('item_id',$value->item_id)->first();
                        if($retur_detail != null){
                            $update_wr_receipt= WRD::find($retur_detail->warehouse_receipt_detail_id);
                            $update_wr_receipt->return_qty= $update_wr_receipt->return_qty - $value->qty;
                            $update_wr_receipt->return_receipt_qty += $value->qty;
                            $update_wr_receipt->save();
                            // $wr_stok= WSD::where('warehouse_receipt_detail_id',$retur_detail->warehouse_receipt_detail_id)->first();
                            // $wr_stok->qty += $value->qty;
                            // $wr_stok->available_qty += $value->qty;
                            // $wr_stok->save();
                            //update po status
                            DB::table('purchase_orders')->whereId($request['purchase_order_retur_id'])->update([
                                'status' => PurchaseOrderStatus::getFinished()
                            ]);
                        }
                    }
                }
            }
        }
        // Simpan lampiran
        if(is_array($attachment)) {
            if(count($attachment) > 0) {
                foreach($attachment as $unit) {
                    DB::table('delivery_order_photos')->insert([
                        'receipt_id' => $warehouse_receipt_id,
                        'name' => $unit
                    ]);
                }
            }
        }
        if(($request['purchase_order_id'] ?? null) && $request['purchase_order_id']!=='null') {
            PurchaseOrder::finishReceipt($request['purchase_order_id']);
        }
        if($request['vulkanisir_request_id']){
            VulkanisirRequest::finishReceipt($request['vulkanisir_request_id']);
        }
        $setting_penjualan= null;
        // dd('tess');
        $dataWR= WR::find($warehouse_receipt_id);
        $dataPO= PO::find($dataWR->purchase_order_id);
        $data_detail= DB::table('warehouse_receipt_details')->where('header_id',$warehouse_receipt_id)->get();
        //create jurnal or cash bank transaction
        $setting = new \App\Http\Controllers\Setting\SettingController();
        $check= $setting->show_data('good_receipt');
        foreach($check->getData() as $c){
            // array_search('pengakuan_jatuh_tempo', $c->content->settings);
            // dd(array_search('pengakuan_jatuh_tempo', $c->content->settings));
            // die();
            foreach($c->content->settings as $s){
                if($s->slug == "pengakuan_jatuh_tempo"){
                    $setting_penjualan=$s->value;
                }
            }
        }
        // dd('tess');
        if($setting_penjualan != null && $setting_penjualan == "GOOD_RECEIPT"){
            if($dataPO != null){
                if($dataPO->payment_type == 2){
                    if(Journal::is_use_finance()){ // improve jika pake modul finance
                        self::journal_data_receipt($warehouse_receipt_id);
                    }
                }else{
                    if($dataPO->payment_type == 1){
                        self::set_cash_transaction($warehouse_receipt_id);
                    }
                }

                if($dataPO->payment_type == 2){
                    self::payable_accrual($warehouse_receipt_id);
                }
            }
        }
        elseif($setting_penjualan != null && $setting_penjualan == "VENDOR_BILL"){
            if($dataPO != null){
                
            }
        }
        return $warehouse_receipt_id;
    }
    /*
      Date : 24-03-2020
      Description : Membuat shipment status
      Developer : Didin
      Status : Create
    */
    public static function storeShipmentStatus($warehouse_receipt_id)
    {
        DB::table('shipment_statuses')
        ->insert([
            'warehouse_receipt_id' => $warehouse_receipt_id,
            'status_date' => DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")')
        ]);
    }
    /*
      Date : 20-03-2020
      Description : Menyimpan penerima di master kontak
      Developer : Didin
      Status : Create
    */
    public static function storePenerima($name, $address, $company_id)
    {
        $latest_contact = DB::table('contacts')
        ->whereName($name)
        ->whereIsPenerima(1)
        ->count('id');
        if($latest_contact == 0) {
            DB::table('contacts')
            ->insert([
              'company_id' => $company_id,
              'is_penerima' => 1,
              'name' => $name,
              'address' => $address ?? '-',
              'email' => Str::random(10) . '@gmail.com'
            ]);
        } else {
            DB::table('contacts')
            ->whereName($name)
            ->whereIsPenerima(1)
            ->update([
                  'address' => $address
            ]);
        }
    }
    /*
      Date : 29-08-2020
      Description : Menambah barang pada stock transaction
      Developer : Didin
      Status : Create
    */
    public static function bill($job_order_id) {
        try {
            $jobOrder = DB::table('job_orders')
            ->join('kpi_statuses', 'kpi_statuses.id', 'job_orders.kpi_id')
            ->where('job_orders.id', $job_order_id)
            ->select('job_orders.kpi_id', 'kpi_statuses.is_done')
            ->first();
            $jobOrderDetails = DB::table('job_order_details')
            ->join('warehouse_receipt_details', 'job_order_details.warehouse_receipt_detail_id', 'warehouse_receipt_details.id')
            ->where('job_order_details.header_id', $job_order_id)
            ->groupBy('warehouse_receipt_details.header_id')
            ->select('warehouse_receipt_details.header_id AS warehouse_receipt_id')
            ->get();
            if($jobOrder->is_done == 1) {
                $kpiLog = DB::table('kpi_logs')
                ->whereJobOrderId($job_order_id)
                ->whereKpiStatusId($jobOrder->kpi_id)
                ->select('date_update')
                ->first();
                foreach ($jobOrderDetails as $item) {
                    $warehouse_receipt_id = $item->warehouse_receipt_id;
                    $exist = DB::table('warehouse_receipt_billings')
                    ->whereWarehouseReceiptId($warehouse_receipt_id)
                    ->whereJobOrderId($job_order_id)
                    ->count('id');
                    $billing_date = Carbon::parse($kpiLog->date_update)->format('Y-m-d');
                    $new_receive_date = Carbon::parse($billing_date)
                    ->addDays(1)
                    ->format('Y-m-d');
                    if($exist == 0) {
                        $params = ['warehouse_receipt_id' => $warehouse_receipt_id, 'job_order_id' => $job_order_id, 'billing_date' => $billing_date, 'new_receive_date' => $new_receive_date, 'created_at' => Carbon::now()->format('Y-m-d')];
                        DB::table('warehouse_receipt_billings')
                        ->insert($params);
                    } else {
                        $params = ['billing_date' => $billing_date, 'new_receive_date' => $new_receive_date, 'updated_at' => Carbon::now()->format('Y-m-d')];
                        DB::table('warehouse_receipt_billings')
                        ->whereWarehouseReceiptId($warehouse_receipt_id)
                        ->whereJobOrderId($job_order_id)
                        ->update($params);
                    }
                }
            } else {
                foreach ($jobOrderDetails as $item) {
                    $warehouse_receipt_id = $item->warehouse_receipt_id;
                    DB::table('warehouse_receipt_billings')
                    ->whereWarehouseReceiptId($warehouse_receipt_id)
                    ->whereJobOrderId($job_order_id)
                    ->delete();
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage);
        }
    }
    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table('warehouse_receipts')
        ->whereId($id)
        ->first();
        if(!$dt) {
            throw new Exception('Warehouse receipt not found');
        }
    }
    /*
      Date : 05-03-2021
      Description : Menampilkan detail data
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);
        $dt = WR::with('customer:id,name,email', 'company:id,name', 'user:id,name', 'collectible','warehouse','staff')
        ->leftJoin('receipt_types', 'receipt_types.id', 'warehouse_receipts.receipt_type_id')
        ->leftJoin('purchase_orders', 'purchase_orders.id', 'warehouse_receipts.purchase_order_id')
        ->where('warehouse_receipts.id', $id)
        ->leftJoin('vehicle_types', 'vehicle_types.id', 'warehouse_receipts.vehicle_type_id')
        ->selectRaw('warehouse_receipts.*, vehicle_types.name AS vehicle_type_name, receipt_types.code AS receipt_type_code, receipt_types.name AS receipt_type_name, purchase_orders.code AS purchase_order_code')
        ->first();
        if($dt) {
            if($dt->customer) {
                if($dt->customer->email) {
                    $dt->customer->email = str_replace(',', ';', $dt->customer->email);
                }
            }
        }
        $dt->item_migration_id = ItemMigrationReceipt::getPrimaryId($id);
        $dt->voyage_schedule_id = VoyageReceipt::getPrimaryId($id);
        $dt->ttd = $dt->ttd != null ? asset('files') . '/' . $dt->ttd : null;
        return $dt;
    }
    /*
      Date : 05-03-2021
      Description : Memvalidasi apakah data sudah disetujui atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsApproved($id) {
        $dt = self::show($id);
        $status = WarehouseReceiptStatus::getApproved();
        if($dt->status == $status) {
            throw new Exception('Data was approved');
        }
    }
    /*
      Date : 05-03-2021
      Description : Memvalidasi apakah data sudah disetujui atau belum
      Developer : Didin
      Status : Create
    */
    public static function scanBarcode($code) {
        $code = preg_replace("/^(0+)([1-9]+0*)/", '${2}', $code);
        $warehouse_receipt_detail_id = $code;
        $dt = WarehouseReceiptDetail::query();
        $dt->where("warehouse_receipt_details.id", $warehouse_receipt_detail_id);
        $params = [];
        if($dt->count(self::$table . '.id') > 0) {
            $dt = $dt->leftJoin('purchase_orders', 'warehouse_receipts.purchase_order_id', 'purchase_orders.id');
            $dt = $dt->leftJoin('contacts', 'warehouse_receipts.customer_id', 'contacts.id');
            $dt = $dt->leftJoin('users', 'users.id', 'warehouse_receipts.create_by');
            $dt = $dt->leftJoin('vehicle_types', 'vehicle_types.id', 'warehouse_receipts.vehicle_type_id');
            $dt = $dt->select(
                self::$table . '.code',
                self::$table . '.description',
                self::$table . '.receive_date',
                self::$table . '.stripping_done',
                'racks.code AS rack_code',
                'purchase_orders.code AS purchase_order_code',
                'contacts.name AS customer_name',
                'warehouses.name AS warehouse_name',
                'warehouse_receipt_details.id AS warehouse_receipt_detail_id',
                'users.name AS creator_name',
                'warehouse_receipt_details.item_id',
                'warehouse_receipt_details.item_name',
                'vehicle_types.name AS vehicle_type_name',
                DB::raw('IF(warehouse_receipts.status = 0, "Draft", "Disetujui") AS status_name')
            )
            ->first();
            $dt->stock = StockTransaction::getAvailibity($dt->warehouse_receipt_detail_id);
            $params['code'] = $dt->code;
            $params['description'] = $dt->description;
            $params['receive_date'] = $dt->receive_date;
            $params['stripping_done'] = $dt->stripping_done;
            $params['purchase_order_code'] = $dt->purchase_order_code;
            $params['customer_name'] = $dt->customer_name;
            $params['warehouse_name'] = $dt->warehouse_name;
            $params['creator_name'] = $dt->creator_name;
            $params['vehicle_type_name'] = $dt->vehicle_type_name;
            $params['status_name'] = $dt->status_name;
            $params['item_name'] = $dt->item_name;
            $params['stock'] = $dt->stock;
            $params['rack_code'] = $dt->rack_code;
        }
        return $params;
    }
            /*
      Date : 23-11-2021
      Description : Set Journal Data
      Developer : Atika
      Status : Create
    */
    public static function journal_data_receipt($id){
        $data= DB::table('warehouse_receipts')->where('warehouse_receipts.id',$id)->first();
        $dataPO= DB::table('purchase_orders')->where('id',$data->purchase_order_id)->first();
        // dd($data);
        // die();
        $data_detail= DB::table('warehouse_receipt_details')->where('header_id',$id)->get();
        $qty=DB::table('warehouse_receipt_details')->where('header_id',$id)->sum('qty');
        $price= DB::table('purchase_order_details')->where('header_id',$dataPO->id)->sum('price');
        $tp=TypeTransaction::find(32);
        $code = new TransactionCode($dataPO->company_id, $tp->slug);
        $code->setCode();
        $trx_code = $code->getCode();
        //journal create
        $j=Journal::create([
            'company_id' => $dataPO->company_id,
            'type_transaction_id' => 32,
            'date_transaction' => Carbon::now()->format('Y-m-d'),
            'created_by' => auth()->id(),
            'code' => $trx_code,
            'description' => 'Penerimaan barang ' . $data->code,
            'debet' => 0,
            'credit' => 0
          ]);
        foreach($data_detail as $x){
            $item=DB::table('items')->where('id',$x->item_id)->first();
            $dataPODetail= DB::table('purchase_order_details')->where('header_id',$dataPO->id)->where('item_id',$x->item_id)->first();
            // create jurnal detail
            $account_default = DB::table('account_defaults')->first();
            //get account debet
            if($item->is_stock == 1){ //check if item stock
                if($item->account_id != null){
                    $account_debet=$item->account_id;
                    $account_ppn= $account_default->ppn_in;
                }
                else{
                    $account_debet= $account_default->persediaan;
                    $account_ppn= $account_default->ppn_in;
                }
                if($account_debet == null) {
                    throw new Exception('Akun persediaan belum di-set pada setting default akun');
                }
            }else{
                if($item->account_usage != null){
                    $account_debet=$item->account_usage;
                }
                else{
                    $account_debet= $account_default->item_usage;
                }
                if($account_debet == null) {
                    throw new Exception('Akun biaya penggunan item belum di-set pada setting default akun');
                }
            }
            if($x->item_id == $dataPODetail->item_id){
                $debet= $x->qty * $dataPODetail->price;
                $ppn= $x->qty * $dataPODetail->ppn;
            }
            $journalDetailData=JournalDetail::where('header_id',$j->id)->where('account_id',$account_debet)->first();
            $journalDetailDataPpn=JournalDetail::where('header_id',$j->id)->where('account_id',$account_default->ppn_in)->first();
            if($journalDetailData == null){
                JournalDetail::create([
                    'header_id' => $j->id,
                    'account_id' => $account_debet,
                    'debet' => $debet
                ]);
                JournalDetail::create([
                    'header_id' => $j->id,
                    'account_id' => $account_default->ppn_in,
                    'debet' => $ppn
                ]);
            }
            else{
                $updateJournalDebetDetail=JournalDetail::find($journalDetailData->id);
                $updateJournalDebetDetail->debet= $updateJournalDebetDetail->debet+$debet;
                $updateJournalDebetDetail->save();

                $updateJournalDebetDetailPpn=JournalDetail::find($journalDetailDataPpn->id);
                $updateJournalDebetDetailPpn->debet= $journalDetailDataPpn->debet+$ppn;
                $updateJournalDebetDetailPpn->save();
            }
        }
        $contact= DB::table('contacts')->where('id',$dataPO->supplier_id)->first();
            if($contact->akun_hutang != null){
                $account_credit= $contact->akun_hutang;
            }else{
                $account_credit= $account_default->hutang;
            }
        //get account credit
        if($account_credit == null) {
            throw new Exception('Akun credit belum di-set pada setting default akun');
        }
        $journalDataTotal= JournalDetail::where('header_id',$j->id)->sum('debet');
        JournalDetail::create([
            'header_id' => $j->id,
            'account_id' => $account_credit,
            'credit' => $journalDataTotal
        ]);
        //update debet credit total
        $journal= Journal::find($j->id);
        $journal->debet= $journalDataTotal;
        $journal->credit= $journalDataTotal;
        $journal->save();
        //payable
        $codex = new TransactionCode($dataPO->company_id, 'creditNote');
        $codex->setCode();
        $credit_note = $codex->getCode();
        $grandtotal = DB::table('purchase_order_details')
                ->whereHeaderId($dataPO->id)
                ->sum('total');
        $params['company_id'] = $dataPO->company_id;
        $params['contact_id'] = $dataPO->supplier_id;
        $params['type_transaction_id'] = 32;
        $params['journal_id'] = $j->id;
        $params['credit_note'] = $credit_note;
        $params['relation_id'] = $dataPO->id;
        $params['created_by'] = auth()->user()->id;
        $params['code'] = $data->code;
        $params['date_transaction'] = Carbon::now()->format('Y-m-d');
        $params['created_at'] = Carbon::now()->format('Y-m-d');
        $supplier = DB::table('contacts')
            ->whereId($dataPO->supplier_id)
            ->select('term_of_payment')
            ->first();
        $tempo = $supplier->term_of_payment ?? 1;
        $params['date_tempo'] = Carbon::now()->addDays($dataPO->due_date)->format('Y-m-d');
        $params['credit'] = $grandtotal;
        $params['debet'] = $grandtotal;
        $params['description'] = 'Penerimaan pembelian - ' . $data->code;
        $payable_id = DB::table('payables')
        ->insertGetId($params);
        //detail payable
        foreach($data_detail as $x){
            $item=DB::table('items')->where('id',$x->item_id)->first();
            $dataPODetail= DB::table('purchase_order_details')->where('header_id',$dataPO->id)->where('item_id',$x->item_id)->first();
            // dd($dataPODetail);
            $params = [];
            $detail = [];
            $detail['header_id'] = $payable_id;
            $detail['type_transaction_id'] = 32;
            $detail['journal_id'] = $j->id;
            $detail['created_at'] = Carbon::now()->format('Y-m-d');
            $detail['description'] = 'Penerimaan pembelian - ' . $data->code . ' - atas barang ' . $item->name;
            $detail['relation_id'] = $dataPODetail->id;
            $detail['code'] = $data->code;
            $detail['debet'] = 0;
            $detail['credit'] = $x->qty * $dataPODetail->price;
            $params[] = $detail;
            DB::table('payable_details')
            ->insert($params);
        }
    }
    /*
      Date : 24-11-2021
      Description : Set Payable Accrual Penagihan Vendor
      Developer : Atika
      Status : Create
    */
    public static function payable_accrual($id){
        $data= DB::table('warehouse_receipts')->where('warehouse_receipts.id',$id)->first();
        $dataPO= DB::table('purchase_orders')->where('id',$data->purchase_order_id)->first();
        $data_detail= DB::table('warehouse_receipt_details')->where('warehouse_receipt_details.header_id',$id)
        ->leftJoin('warehouse_receipts','warehouse_receipts.id','warehouse_receipt_details.header_id')
        ->leftJoin('purchase_orders','purchase_orders.id','warehouse_receipts.purchase_order_id')
        ->leftJoin('items','items.id','warehouse_receipt_details.item_id')
        ->select('purchase_orders.code as po_code',
        'purchase_orders.id as po_id',
        'items.name as item_name',
        'items.id as item_id',
        'warehouse_receipt_details.qty as qty')
        ->get();
        $grandtotal = DB::table('purchase_order_details')
        ->whereHeaderId($dataPO->id)
        ->sum('total');
        $paramsPayable['company_id'] = $dataPO->company_id;
        $paramsPayable['contact_id'] = $dataPO->supplier_id;
        $paramsPayable['type_transaction_id'] = 32;
        $paramsPayable['relation_id'] = $dataPO->id;
        $paramsPayable['created_by'] = auth()->user()->id;
        $paramsPayable['code'] = $data->code;
        $paramsPayable['date_transaction'] = Carbon::now()->format('Y-m-d');
        $paramsPayable['created_at'] = Carbon::now()->format('Y-m-d');
        $supplier = DB::table('contacts')
            ->whereId($dataPO->supplier_id)
            ->select('term_of_payment')
            ->first();
        $tempo = $supplier->term_of_payment ?? 1;
        // $paramsPayable['date_tempo'] = Carbon::now()->addDays($dataPO->due_date)->format('Y-m-d');
        $paramsPayable['credit'] = $grandtotal;
        $paramsPayable['debet'] = $grandtotal;
        $paramsPayable['description'] = 'Penerimaan pembelian - ' . $data->code;
        $paramsPayable['is_temporary'] =1;
        $paramsPayable['period'] = $dataPO->due_date;
            $paramsPayable['detail'] = $data_detail->map(function($d) {
                $dataPODetail= DB::table('purchase_order_details')->where('header_id',$d->po_id)->where('item_id',$d->item_id)->first();
                return [
                    'type_transaction_id' => 32,
                    'created_at' => Carbon::now()->format('Y-m-d'),
                    'description' => 'Penerimaan pembelian - ' . $d->po_code . ' - atas barang ' . $d->item_name,
                    'code' => $d->po_code,
                    'debet' => 0,
                    'credit' => $d->qty * $dataPODetail->price,
                    'relation_id' => $dataPODetail->id,
                ];
            });
        Payable::store_data($paramsPayable);
    }
    /*
      Date : 24-11-2021
      Description : Set Cash Transaction
      Developer : Atika
      Status : Create
    */
    public static function set_cash_transaction($id){
        $data= DB::table('warehouse_receipts')->where('warehouse_receipts.id',$id)->first();
        $dataPO= DB::table('purchase_orders')->where('id',$data->purchase_order_id)->first();
        $data_detail= DB::table('warehouse_receipt_details')->where('header_id',$id)->get();
        $cashAccount = DB::table('accounts')
                        ->where('no_cash_bank', '>', 0)
                        ->select('id')
                        ->first();
        $companies= DB::table('companies')->where('id',$dataPO->company_id)->first();
        if($companies->cash_account_id != null){
        $cashAccountIdData = $companies->cash_account_id;
        }
        else{
            $cashAccountIdData=$cashAccount->id;
        }
        if($cashAccountIdData == null) {
            throw new Exception('Akun kas belum di-set pada setting default akun');
        }
        DB::beginTransaction();
        $tyname="CashOut";
        $tp = TypeTransaction::where('slug', $tyname)->first();
        $code = new TransactionCode($dataPO->company_id, $tyname);
        $code->setCode();
        $trx_code = $code->getCode();
        //create cash transaction
        $i=CashTransaction::create([
            'company_id' => $dataPO->company_id,
            'type_transaction_id' => $tp->id,
            'code' => $trx_code,
            'jenis' => 2,
            'type' => 1,
            'description' => 'Penerimaan Barang - ' . $data->code,
            'total' => 0,
            'account_id' => $cashAccountIdData,
            'date_transaction' => Carbon::now()->format('Y-m-d'),
            'status_cost' => 1,
            'created_by' => auth()->id()
        ]);
        //create detail cash transaction
        $account_default = DB::table('account_defaults')
            ->first();
        foreach($data_detail as $x){
            $item=DB::table('items')->where('id',$x->item_id)->first();
            $dataPODetail= DB::table('purchase_order_details')->where('header_id',$dataPO->id)->where('item_id',$x->item_id)->first();
            //akun persediaan
            if($item->is_stock == 1){ //jika item stock
                if($item->account_id != null){
                    $account_persediaan= $item->account_id;
                }
                else{
                    $account_persediaan=$account_default->persediaan;
                }
                if($account_persediaan == null) {
                    throw new Exception('Akun persediaan belum di-set pada setting default akun');
                }
                $desc= "Persediaan barang ";
            }
            else{
                if($item->account_usage != null){
                    $account_persediaan= $item->account_usage;
                }
                else{
                    $account_persediaan=$account_default->item_usage;
                }
                if($account_persediaan == null) {
                    throw new Exception('Akun biaya penggunaan barang belum di-set pada setting default akun');
                }
                $desc= "Penggunaan barang ";
            }
            $CTDetailData=CashTransactionDetail::where('header_id',$i->id)->where('account_id',$account_persediaan)->first();
            if($CTDetailData == null){
                CashTransactionDetail::create([
                    'header_id' => $i->id,
                    'account_id' => $account_persediaan,
                    'amount' => $x->qty*$dataPODetail->price,
                    'job_order_cost_id' => $x->job_order_cost_id ?? null,
                    'manifest_cost_id' => $x->manifest_cost_id ?? null,
                    'description' => $desc.'item '.$item->name,
                    'jenis' => 1
                ]);
            }
            else{
                $updateCTDetail= CashTransactionDetail::find($CTDetailData->id);
                $updateCTDetail->amount = $updateCTDetail->amount+($x->qty*$dataPODetail->price);
                $updateCTDetail->description = $updateCTDetail->description.' & '.$item->name;
                $updateCTDetail->save();
            }
        }
        $total= CashTransactionDetail::where('header_id',$i->id)->sum('amount');
        $updateTotalCT= CashTransaction::find($i->id);
        $updateTotalCT->total= $total;
        $updateTotalCT->save();
        DB::commit();
        return Response::json(null);
    }
}
