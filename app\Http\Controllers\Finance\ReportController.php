<?php
namespace App\Http\Controllers\Finance;
use Excel;
use Exception;
use Carbon\Carbon;
use App\Model\Account;
use App\Model\Company;
use App\Model\Contact;
use App\Model\Journal;
use PHPExcel_Style_Fill;
use App\Model\JournalDetail;
use Illuminate\Http\Request;
use App\Model\AccountDefault;
use App\Model\TypeTransaction;
use App\Exports\Finance\ArusKas;
use App\Exports\Finance\ArusKasBaru;
use App\Exports\Finance\ArusKasPerbandinganBaru;
use App\Exports\Finance\Ekuitas;
use App\Exports\Finance\LabaRugi;
use App\Exports\Finance\BukuBesar;
use Barryvdh\DomPDF\Facade as PDF;
use App\Exports\Finance\DaftarAkun;
use App\Exports\Finance\JournalUmum;
use App\Exports\Finance\NeracaLajur;
use App\Exports\Finance\NeracaSaldo;
use App\Exports\Finance\RegisterPengeluaranJo;
use App\Http\Controllers\Controller;
use Barryvdh\DomPDF\Facade as DomPDF;
use Barryvdh\Snappy\Facades\SnappyPdf;
use App\Exports\Finance\PosisiKeuangan;
use App\Exports\Finance\BukuBesarHutang;
use App\Exports\Finance\BukuBesarPiutang;
use App\Exports\Finance\OutstandingDebit;
use App\Exports\Finance\OutstandingCredit;
use App\Exports\Finance\PosisiKeuanganPerbandingan;
use App\Model\JobOrder;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;
use stdClass;

class ReportController extends Controller
{
  /*
    Date : 05-10-2022 By : Muhammad Firyanul Rizky
    Description : 13. Report - Finance Accounting
    Keterangan : fungsi get data untuk laporan Data Akun
  */
  public function account_data()
  {
    return collect($data['data']=Account::orderBy('code')->get());
    // dd($data);
    // Excel::create('Daftar Akun', function($excel) use ($data) {
    //   $excel->sheet('Daftar Akun', function($sheet) use ($data){
    //     $sheet->loadView('export.account', $data);
    //     // $sheet->setStyle([
    //     //   'font' => [
    //     //     'name' => 'Calibri',
    //     //     'size' => 11,
    //     //   ],
    //     // ]);
    //   });
    // })->export('xls');
  }
  public function account_get()
  {
    $data['data']=Account::orderBy('code')->get();
    return $data;
  }
  public function account(Request $request) {
    $data = $this->account_get($request);
    return PDF::loadView('export.account', $data)->stream('Daftar Akun.pdf');
  }
  public function account_excel(Request $request) {
    $now = date("d_m_Y_H_i_s");
    return Excel::download(New DaftarAkun($request), " daftar_akun_$now.xlsx");
  }
  // Close Description : 13. Report - Finance Accounting
  public function journal()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['type_transaction']=TypeTransaction::all();
    $data['account']=Account::with('parent','type')->where('is_base',0)->orderBy('code')->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ledger()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['account']=Account::with('parent')->where('is_base',0)->orderBy('code')->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ledger_receivable()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['customer']=Contact::where('is_pelanggan', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ledger_payable()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['supplier']=Contact::whereRaw("is_vendor = 1 or is_supplier=1")->where('vendor_status_approve', 2)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ledger_um_supplier()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['supplier']=Contact::whereRaw("is_vendor = 1 or is_supplier=1")->where('vendor_status_approve', 2)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ledger_um_customer()
  {
    $data['company']=companyAdmin(auth()->id());
    $data['customer']=Contact::where('is_pelanggan', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function neraca_saldo()
  {
    $data['company']=companyAdmin(auth()->id());
    // $data['customer']=Contact::where('is_pelanggan', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function arus_kas()
  {
    $data['company']=companyAdmin(auth()->id());
    // $data['customer']=Contact::where('is_pelanggan', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function laba_rugi()
  {
    $data['company']=companyAdmin(auth()->id());
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function ekuitas()
  {
    $data['company']=companyAdmin(auth()->id());
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function posisi_keuangan()
  {
    $data['company']=companyAdmin(auth()->id());
    $setting = DB::table('settings')->where('slug', 'general')->first();
    $data['initial_date_accounting'] = collect(json_decode($setting->content)->settings)->where('slug', 'initial_date_transaction_accounting')->first()->value;
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function export_journal(Request $request)
  {
    $journal = Journal::query();

    // Filter tanggal
    if (!empty($request->start_date) && !empty($request->end_date)) {
        $start_date = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date = Carbon::parse($request->end_date)->format('Y-m-d');
        $journal->whereBetween('date_transaction', [$start_date, $end_date]);
    }

    // Filter company_id
    if (!empty($request->company_id)) {
        $journal->where('company_id', $request->company_id);
    }

    // Filter status
    if (!empty($request->status)) {
        $journal->where('status', $request->status);
    }

    // Filter type_transaction_id
    if (!empty($request->type_transaction_id)) {
        $journal->where('type_transaction_id', $request->type_transaction_id);
    }

    // Filter berdasarkan account_id pada relasi details
    if (!empty($request->account_id)) {
        $journal->whereHas('details', function($query) use ($request) {
            $query->where('account_id', $request->account_id);
        });

        // Eager load details yang sudah difilter juga
        $journal->with([
          'details' => function ($query) use ($request) {
              $query->where('account_id', $request->account_id)
                    ->select('id', 'header_id', 'account_id', 'description', 'debet', 'credit'); // include journal_id untuk relasi
          },
          'details.account:id,code,name',
          'type_transaction:id,name',
        ]);
    } else {
        // Eager load semua details jika tidak difilter
        $journal->with([
            'details' => function ($query) {
                $query->select('id', 'header_id', 'account_id', 'description', 'debet', 'credit'); // include journal_id juga
            },
            'details.account:id,code,name',
            'type_transaction:id,name',
        ]);
    }

    // Filter jika ingin menyembunyikan nilai 0
    $prevent_zero = $request->prevent_zero === 'true';
    if ($prevent_zero) {
        $journal->where(function($query) {
            $query->where('debet', '!=', 0)
                  ->orWhere('credit', '!=', 0);
        });
    }

    // Ambil data terurut
    $journal = $journal->orderBy('date_transaction')
                    ->select(
                      'id',
                      'code',
                      'date_transaction',
                      'type_transaction_id'
                    )
                    ->get();
    $data['data']= $journal;
    $data['company']=Company::find($request->company_id);
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    return PDF::loadView('export.journal', $data)->stream('Jurnal Umum.pdf');
  }
  public function journal_data(Request $request)
  {
    $wr="1=1";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND company_id = $request->company_id";
    }
    if (isset($request->status)) {
      $wr.=" AND status = $request->status";
    }
    if (isset($request->type_transaction_id)) {
      $wr.=" AND type_transaction_id = $request->type_transaction_id";
    }
    $journal = Journal::whereRaw($wr);
    // Apakah menampilkan yang nilainya 0 atau tidak
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
        $journal = $journal->where([
          ['debet', '!=', 0],
          ['credit', '!=', 0]
        ]);
    }
    $journal = $journal->orderBy('date_transaction')->get();
    return collect($data['data']= $journal);
  }
  public function export_journal_excel(Request $request){
    $journal = Journal::query();

    // Filter tanggal
    if (!empty($request->start_date) && !empty($request->end_date)) {
        $start_date = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date = Carbon::parse($request->end_date)->format('Y-m-d');
        $journal->whereBetween('date_transaction', [$start_date, $end_date]);
    }

    // Filter company_id
    if (!empty($request->company_id)) {
        $journal->where('company_id', $request->company_id);
    }

    // Filter status
    if (!empty($request->status)) {
        $journal->where('status', $request->status);
    }

    // Filter type_transaction_id
    if (!empty($request->type_transaction_id)) {
        $journal->where('type_transaction_id', $request->type_transaction_id);
    }

    // Filter berdasarkan account_id pada relasi details
    if (!empty($request->account_id)) {
        $journal->whereHas('details', function($query) use ($request) {
            $query->where('account_id', $request->account_id);
        });

        // Eager load details yang sudah difilter juga
        $journal->with([
          'details' => function ($query) use ($request) {
              $query->where('account_id', $request->account_id)
                    ->select('id', 'header_id', 'account_id', 'description', 'debet', 'credit'); // include journal_id untuk relasi
          },
          'details.account:id,code,name',
          'type_transaction:id,name',
        ]);
    } else {
        // Eager load semua details jika tidak difilter
        $journal->with([
            'details' => function ($query) {
                $query->select('id', 'header_id', 'account_id', 'description', 'debet', 'credit'); // include journal_id juga
            },
            'details.account:id,code,name',
            'type_transaction:id,name',
        ]);
    }

    // Filter jika ingin menyembunyikan nilai 0
    $prevent_zero = $request->prevent_zero === 'true';
    if ($prevent_zero) {
        $journal->where(function($query) {
            $query->where('debet', '!=', 0)
                  ->orWhere('credit', '!=', 0);
        });
    }

    // Ambil data terurut
    $journal = $journal->orderBy('date_transaction')
                    ->select(
                      'id',
                      'code',
                      'date_transaction',
                      'type_transaction_id'
                    )
                    ->get();
     $company=Company::find($request->company_id);
     if($company){
      $name=$company->name;
     }else{
      $name="semua cabang";
     }
     $data['data']= $journal;
     $data['company']=$name;
     $data['start']=$request->start_date;
     $data['end']=$request->end_date;
    //  return PDF::loadView('export.journal', $data)->stream('Jurnal Umum.pdf');
    $now = date("d-m-Y H.i");
    return Excel::download(New JournalUmum($data), "Jurnal Umum $now.xlsx");
  }
  /*
      Date : 18-04-2020
      Description : Export buku besar
      Developer : Didin
      Status : Edit
  */
  public function export_ledger(Request $request)
  {
      try {
          $wr = '1=1';

          if ($request->account_id) {
            $wr .= " AND journal_details.account_id = $request->account_id";
          }

          $dt_end = isset($request->end_date) ? date('Y-m-d', strtotime($request->end_date)) : Carbon::now()->format('Y-m-d');
          $dt_start = isset($request->start_date) ? date('Y-m-d', strtotime($request->start_date)) : Carbon::parse('first day of this month')->format('Y-m-d');

          $conditions = [
              ['journals.status', 3],
              ['journals.is_audit', $request->is_audit ?? 0]
          ];

          if (isset($request->company_id)) {
              $conditions[] = ['journals.company_id', intval($request->company_id)];
          }

          if (isset($request->work_order_id)) {
              $conditions[] = ['journals.work_order_id', intval($request->work_order_id)];
          }

          $accounts = DB::table('accounts')
              ->where('is_base', 0)
              ->select('id', 'code', 'name', 'jenis', 'description');

          if ($request->account_id) {
              $accounts->where('id', $request->account_id);
          }

          $accounts = $accounts->orderBy('code')->get()->keyBy('id');

          $saldoAwal = DB::table('journal_details')
              ->leftJoin('journals', 'journals.id', 'journal_details.header_id')
              ->leftJoin('accounts', 'accounts.id', 'journal_details.account_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->where('journals.date_transaction', '<', $dt_start)
              ->groupBy('journal_details.account_id')
              ->selectRaw("journal_details.account_id, SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet)) as saldo")
              ->pluck('saldo', 'account_id');

          $transactions = DB::table('journal_details')
              ->leftJoin('journals', 'journal_details.header_id', 'journals.id')
              ->leftJoin('companies', 'companies.id', 'journals.company_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->whereBetween('journals.date_transaction', [$dt_start, $dt_end])
              ->select('journal_details.account_id', 'journals.date_transaction', 'journals.code', 'journals.wo_code', 'journals.description as j_description', 'journal_details.description', 'journal_details.debet', 'journal_details.credit', 'companies.name as company')
              ->orderBy('journals.date_transaction')
              ->get()
              ->groupBy('account_id');

          $saldoAkhir = DB::table('journal_details')
              ->leftJoin('journals', 'journals.id', 'journal_details.header_id')
              ->leftJoin('accounts', 'accounts.id', 'journal_details.account_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->where('journals.date_transaction', '<=', $dt_end)
              ->groupBy('journal_details.account_id')
              ->selectRaw("journal_details.account_id, SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet)) as saldo")
              ->pluck('saldo', 'account_id');

          $prevent_zero = isset($request->prevent_zero) && $request->prevent_zero == 'true';
          $data = [];

          foreach ($accounts as $account) {
              $pos = $saldoAwal[$account->id] ?? 0;
              $get = $transactions[$account->id] ?? [];
              $saldo_akhir = $saldoAkhir[$account->id] ?? 0;

              if (!$prevent_zero || ($saldo_akhir != 0 || count($get) > 0)) {
                  $data[] = [
                      'account' => [
                          'name' => $account->code . ' - ' . $account->name,
                          'jenis' => $account->jenis,
                          'description' => $account->description,
                      ],
                      'saldo' => $pos,
                      'detail' => $get
                  ];
              }
          }

          $company = Company::find($request->company_id);
          $companyName = $company ? $company->name : "Semua Cabang";

          $responseData = [
              'data' => $data,
              'company' => $companyName,
              'start' => $request->start_date,
              'end' => $request->end_date
          ];

          return PDF::loadView('export.ledger', $responseData)->stream();
      } catch (Exception $e) {
          return Response::json(['message' => $e->getMessage()]);
      }
  }

  public function export_ledger_excel(Request $request)
  {
      try {
          $wr = '1=1';

          if ($request->account_id) {
            $wr .= " AND journal_details.account_id = $request->account_id";
          }

          $dt_end = isset($request->end_date) ? date('Y-m-d', strtotime($request->end_date)) : Carbon::now()->format('Y-m-d');
          $dt_start = isset($request->start_date) ? date('Y-m-d', strtotime($request->start_date)) : Carbon::parse('first day of this month')->format('Y-m-d');

          $conditions = [
              ['journals.status', 3],
              ['journals.is_audit', $request->is_audit ?? 0]
          ];

          if (isset($request->company_id)) {
              $conditions[] = ['journals.company_id', intval($request->company_id)];
          }

          if (isset($request->work_order_id)) {
              $conditions[] = ['journals.work_order_id', intval($request->work_order_id)];
          }

          $accounts = DB::table('accounts')
              ->where('is_base', 0)
              ->select('id', 'code', 'name', 'jenis', 'description');

          if ($request->account_id) {
              $accounts->where('id', $request->account_id);
          }

          $accounts = $accounts->orderBy('code')->get()->keyBy('id');

          $saldoAwal = DB::table('journal_details')
              ->leftJoin('journals', 'journals.id', 'journal_details.header_id')
              ->leftJoin('accounts', 'accounts.id', 'journal_details.account_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->where('journals.date_transaction', '<', $dt_start)
              ->groupBy('journal_details.account_id')
              ->selectRaw("journal_details.account_id, SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet)) as saldo")
              ->pluck('saldo', 'account_id');

          $transactions = DB::table('journal_details')
              ->leftJoin('journals', 'journal_details.header_id', 'journals.id')
              ->leftJoin('companies', 'companies.id', 'journals.company_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->whereBetween('journals.date_transaction', [$dt_start, $dt_end])
              ->select('journal_details.account_id', 'journals.date_transaction', 'journals.code', 'journals.wo_code', 'journals.description as j_description', 'journal_details.description', 'journal_details.debet', 'journal_details.credit', 'companies.name as company')
              ->orderBy('journals.date_transaction')
              ->get()
              ->groupBy('account_id');

          $saldoAkhir = DB::table('journal_details')
              ->leftJoin('journals', 'journals.id', 'journal_details.header_id')
              ->leftJoin('accounts', 'accounts.id', 'journal_details.account_id')
              ->where($conditions)
              ->whereRaw($wr)
              ->where('journals.date_transaction', '<=', $dt_end)
              ->groupBy('journal_details.account_id')
              ->selectRaw("journal_details.account_id, SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet)) as saldo")
              ->pluck('saldo', 'account_id');

          $prevent_zero = isset($request->prevent_zero) && $request->prevent_zero == 'true';
          $data = [];

          $company = Company::find($request->company_id);
          $companyName = $company ? $company->name : "Semua Cabang";

          foreach ($accounts as $account) {
              $pos = $saldoAwal[$account->id] ?? 0;
              $get = $transactions[$account->id] ?? [];
              $saldo_akhir = $saldoAkhir[$account->id] ?? 0;

              if (!$prevent_zero || ($saldo_akhir != 0 || count($get) > 0)) {
                  $data[] = [
                      'account' => [
                          'name' => $account->code . ' - ' . $account->name,
                          'jenis' => $account->jenis,
                          'description' => $account->description,
                      ],
                      'saldo' => $pos,
                      'detail' => $get,
                      'count' => count($get),
                      'company' => $companyName,
                      'start' => $request->start_date,
                      'end' => $request->end_date
                  ];
              }
          }

          $now = date("d_m_Y_H_i_s");
          $fileName = " ";
          $fileName .= 'buku_besar';
          return Excel::download(New BukuBesar($data), "$fileName$now.xlsx");
      } catch (Exception $e) {
          return response()->json(['message' => $e->getMessage()]);
      }
  }

  public function export_ledger_backup(Request $request)
  {
    $response = null;
    try {
      $dt_end = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
      $wr="1=1";
      $wr_sa="1=1";
      $wr2="";
      if (isset($request->start_date) && isset($request->end_date)) {
        $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
        $wr_sa.=" AND journals.date_transaction < '".date('Y-m-d', strtotime($request->start_date))."'";
        $dt_start=date('Y-m-d', strtotime($request->start_date));
      } else {
        $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
        $wr.=" AND journals.date_transaction >= '$dt_start'";
        $wr_sa.=" AND journals.date_transaction >= '$dt_start'";
      }
      $is_audit = $request->is_audit ?? 0;
      $wr.=" AND journals.is_audit = $is_audit";
      $wr_sa.=" AND journals.is_audit = $is_audit";
      $wr2.=" AND journals.is_audit = $is_audit";
      if (isset($request->company_id)) {
        $wr.=" AND journals.company_id = $request->company_id";
        $wr_sa.=" AND journals.company_id = $request->company_id";
        $wr2.=" AND journals.company_id = $request->company_id";
      }
      $wr3="1=1";
      if (isset($request->account_id)) {
        $wr3.=" AND id = ".$request->account_id;
      }
      if ($request->work_order_id) {
        $wr.=" AND journals.work_order_id = $request->work_order_id";
        $wr_sa.=" AND journals.work_order_id = $request->work_order_id";
        $wr2.=" AND journals.work_order_id = $request->work_order_id";
      }
      $data=[];
      // $account=Account::where('is_base', 0)->whereRaw($wr3)->orderBy('code')->get();
      $account=DB::table('accounts')->whereRaw($wr3)->where('is_base', 0)->select('code','name','id','jenis','description')->orderBy('code')->get();
      // dd($account);
      foreach ($account as $value) {
        // $saldo=JournalDetail::leftJoin('journals','journals.id','=','journal_details.header_id')
        //                       ->where('journals.date_transaction', '<', $dt_start)
        $sqlsaldo1="select ifnull(sum(
          if(accounts.jenis=1,
            journal_details.debet-journal_details.credit,
            journal_details.credit
          )),0) as saldo from journal_details
              left join journals on journals.id = journal_details.header_id
              left join type_transactions on type_transactions.id = journals.type_transaction_id
              left join accounts on accounts.id = journal_details.account_id
              where journal_details.account_id = $value->id and journals.status = 3 and $wr_sa";
        $saldo=DB::select($sqlsaldo1)[0];
        if ($saldo->saldo==0) {
          $sqlsaldo2="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
                left join journals on journals.id = journal_details.header_id
                left join type_transactions on type_transactions.id = journals.type_transaction_id
                left join accounts on accounts.id = journal_details.account_id
                where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction < '$dt_start'";
          $saldo=DB::select($sqlsaldo2)[0];
        }
        $pos=$saldo->saldo;
        // $get=JournalDetail::leftJoin('journals','journals.id','=','journal_details.header_id')->whereRaw($wr." AND journals.status = 3")->where('journals.status', 3)->orderBy('journals.date_transaction')->select('journals.date_transaction','journals.code','journal_details.description','journal_details.debet','journal_details.credit')->get();
        $get=DB::select("select journals.date_transaction,journals.code,journals.description as j_description,journal_details.description,journal_details.debet, journal_details.credit, companies.name as company, journals.wo_code from journal_details 
                        left join journals on journal_details.header_id = journals.id 
                        left join type_transactions on type_transactions.id = journals.type_transaction_id
                        left join companies on companies.id = journals.company_id 
                        where $wr and journals.status = 3 and journal_details.account_id = $value->id 
                        order by journals.date_transaction");
        // dd($get);
        $sqlSaldoAkhir="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
                left join journals on journals.id = journal_details.header_id
                left join type_transactions on type_transactions.id = journals.type_transaction_id
                left join accounts on accounts.id = journal_details.account_id
                where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction <= '$dt_end'";
        $saldo_akhir=DB::select($sqlSaldoAkhir)[0]->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && ($saldo_akhir != 0 || count($get) > 0)) || !$prevent_zero) {
            $data[]=[
                'account' => [
                    'name' => $value->code.' - '.$value->name,
                    'jenis' => $value->jenis,
                    'description' => $value->description,
                ],
                'saldo' => $pos,
                'detail' => $get
            ];
        }
      }
      $company=Company::find($request->company_id);
      if($company){
        $name=$company->name;
      }else{
        $name="semua cabang";
      }
      $data['data']=$data;
      $data['company']=$name;
      $data['start']=$request->start_date;
      $data['end']=$request->end_date;
      $response = PDF::loadView('export.ledger', $data)->stream();
    } catch (Exception $e) {
      $response = Response::json(['message' => $e->getMessage()]);
    }
    return $response;
  }
  /*
      Date : 14-12-2022
      Description : Export excel buku besar
      Developer : alwi
      Status : Add
  */
  public function export_ledger_excel_backup(Request $request)
  {
    $response = null;
    try {
      $dt_end = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
      $wr="1=1";
      $wr_sa="1=1";
      $wr2="";
      if (isset($request->start_date) && isset($request->end_date)) {
        $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
        $wr_sa.=" AND journals.date_transaction < '".date('Y-m-d', strtotime($request->start_date))."'";
        $dt_start=date('Y-m-d', strtotime($request->start_date));
      } else {
        $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
        $wr.=" AND journals.date_transaction >= '$dt_start'";
        $wr_sa.=" AND journals.date_transaction >= '$dt_start'";
      }
      $is_audit = $request->is_audit ?? 0;
      $wr.=" AND journals.is_audit = $is_audit";
      $wr_sa.=" AND journals.is_audit = $is_audit";
      $wr2.=" AND journals.is_audit = $is_audit";
      if (isset($request->company_id)) {
        $wr.=" AND journals.company_id = $request->company_id";
        $wr_sa.=" AND journals.company_id = $request->company_id";
        $wr2.=" AND journals.company_id = $request->company_id";
      }
      $wr3="1=1";
      if (isset($request->account_id)) {
        $wr3.=" AND id = ".$request->account_id;
      }
      if ($request->work_order_id) {
        $wr.=" AND journals.work_order_id = $request->work_order_id";
        $wr_sa.=" AND journals.work_order_id = $request->work_order_id";
        $wr2.=" AND journals.work_order_id = $request->work_order_id";
      }
      $data=[];
      // $account=Account::where('is_base', 0)->whereRaw($wr3)->orderBy('code')->get();
      $account=DB::table('accounts')->whereRaw($wr3)->where('is_base', 0)->select('code','name','id','jenis','description')->orderBy('code')->get();
      // dd($account);
      foreach ($account as $value) {
        // $saldo=JournalDetail::leftJoin('journals','journals.id','=','journal_details.header_id')
        //                       ->where('journals.date_transaction', '<', $dt_start)
        $sqlsaldo1="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit)),0) as saldo from journal_details
              left join journals on journals.id = journal_details.header_id
              left join type_transactions on type_transactions.id = journals.type_transaction_id
              left join accounts on accounts.id = journal_details.account_id
              where journal_details.account_id = $value->id and journals.status = 3 and $wr_sa";
        $saldo=DB::select($sqlsaldo1)[0];
        // dd($saldo);
        if ($saldo->saldo==0) {
          $sqlsaldo2="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
                left join journals on journals.id = journal_details.header_id
                left join type_transactions on type_transactions.id = journals.type_transaction_id
                left join accounts on accounts.id = journal_details.account_id
                where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction < '$dt_start'";
          $saldo=DB::select($sqlsaldo2)[0];
        }
        $pos=$saldo->saldo;
        // $get=JournalDetail::leftJoin('journals','journals.id','=','journal_details.header_id')->whereRaw($wr." AND journals.status = 3")->where('journals.status', 3)->orderBy('journals.date_transaction')->select('journals.date_transaction','journals.code','journal_details.description','journal_details.debet','journal_details.credit')->get();
        $get=DB::select("select journals.date_transaction,journals.code,journals.description as j_description,journal_details.description,journal_details.debet, journal_details.credit, companies.name as company, journals.wo_code from journal_details 
                        left join journals on journal_details.header_id = journals.id 
                        left join type_transactions on type_transactions.id = journals.type_transaction_id
                        left join companies on companies.id = journals.company_id 
                        where $wr and journals.status = 3 and journal_details.account_id = $value->id 
                        order by journals.date_transaction");
        // dd($get);
        $sqlSaldoAkhir="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
                left join journals on journals.id = journal_details.header_id
                left join type_transactions on type_transactions.id = journals.type_transaction_id
                left join accounts on accounts.id = journal_details.account_id
                where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction <= '$dt_end'";
        $saldo_akhir=DB::select($sqlSaldoAkhir)[0]->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        $company=Company::find($request->company_id);
        if($company){
          $name=$company->name;
        }else{
          $name="semua cabang";
        }
        if(($prevent_zero && ($saldo_akhir != 0 || count($get) > 0)) || !$prevent_zero) {
            $data[]=[
                'account' => [
                    'name' => $value->code.' - '.$value->name,
                    'jenis' => $value->jenis,
                    'description' => $value->description,
                ],
                'saldo' => $pos,
                'detail' => $get,
                'count' => count($get),
                'company'=> $name,
                'start'=>$request->start_date,
                'end'=>$request->end_date,
            ];
        }
      }
      // $data['data']=$data;
      // $data['count']=
      //  dd($data);
      // dd(count($data));
      // return view('export.ledger', $data);
      // $response = SnappyPdf::loadView('export.ledger', $data)->stream();
      $now = date("d_m_Y_H_i_s");
      $fileName = " ";
      $fileName .= 'buku_besar';
      return Excel::download(New BukuBesar($data), "$fileName$now.xlsx");
      // $response = PDF::loadView('export.ledger', $data)->stream();
    } catch (Exception $e) {
      $response = Response::json(['message' => $e->getMessage()]);
    }
    return $response;
  }
  public function export_ledger_receivable(Request $request)
  {
    $start_date = (isset($request->start_date)) ?
        date('Y-m-d', strtotime($request->start_date)) :
        Carbon::parse('first day of this month')->format('Y-m-d');
    $end_date = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
    $whereCustomer = (isset($request->customer_id)) ? " AND id = {$request->customer_id} " : "";
    $whereSaldoAwal = "AND rd.date_transaction < '{$start_date}' ";
    $whereSaldoAkhir = "AND rd.date_transaction <= '{$end_date}' ";
    $data=['data'=>[]];
    $customer = DB::table('contacts')
        ->whereRaw("is_pelanggan = 1 {$whereCustomer}")
        ->select('id','name')->get();
    $details_query = "FROM receivable_details rd "
        . "LEFT JOIN receivables r ON rd.header_id = r.id "
        . "LEFT JOIN type_transactions t ON rd.type_transaction_id = t.id "
        . "WHERE 1 = 1 ";
    if(isset($request->company_id))
        $details_query .= "AND r.company_id = {$request->company_id} ";
    $saldo_query = "SELECT IFNULL(SUM(rd.debet-rd.credit),0) as saldo $details_query ";
    $saldo_debet_query = "SELECT IFNULL(SUM(rd.debet),0) as saldo  ";
    $saldo_credit_query = "SELECT IFNULL(SUM(rd.credit),0) as saldo  ";
    foreach ($customer as $value) {
        $saldo_sql = $saldo_query . "AND r.contact_id = {$value->id} ";
        $saldo_debet_sql = $saldo_debet_query . $details_query . "AND r.contact_id = {$value->id} ";
        $saldo_credit_sql = $saldo_credit_query . $details_query . "AND r.contact_id = {$value->id} ";
        $saldo_awal_sql = DB::select($saldo_sql . $whereSaldoAwal)[0];
        $saldo_akhir_sql = DB::select($saldo_sql . $whereSaldoAkhir)[0];
        $saldo_debet_sql = DB::select($saldo_debet_sql . $whereSaldoAkhir)[0];
        $saldo_credit_sql = DB::select($saldo_credit_sql . $whereSaldoAkhir)[0];
        $details = DB::select("SELECT rd.date_transaction, rd.code, rd.description, rd.debet, rd.credit "
            . $details_query
            . "AND rd.date_transaction BETWEEN '{$start_date}' AND '{$end_date}' "
            . "AND r.contact_id = {$value->id}");
        $saldo_awal = $saldo_awal_sql->saldo;
        $saldo_akhir = $saldo_akhir_sql->saldo;
        $saldo_debet = $saldo_debet_sql->saldo;
        $saldo_credit = $saldo_credit_sql->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && ($saldo_akhir != 0 || count($details) > 0)) || !$prevent_zero) {
            $data['data'] []= [
                'contact' => [
                    'name' => $value->name,
                ],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => $details
            ];
        }
    }
    $data['company']=Company::find($request->company_id);
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    // return SnappyPdf::loadView('export.ledger_receivable', $data)->stream('Buku Besar Piutang.pdf');
    return PDF::loadView('export.ledger_receivable', $data)->stream('Buku Besar Piutang.pdf');
  }
  public function export_ledger_receivable_excel(Request $request)
  {
    $start_date = (isset($request->start_date)) ?
        date('Y-m-d', strtotime($request->start_date)) :
        Carbon::parse('first day of this month')->format('Y-m-d');
    $end_date = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
    $whereCustomer = (isset($request->customer_id)) ? " AND id = {$request->customer_id} " : "";
    $whereSaldoAwal = "AND rd.date_transaction < '{$start_date}' ";
    $whereSaldoAkhir = "AND rd.date_transaction <= '{$end_date}' ";
    $data=['data'=>[]];
    $customer = DB::table('contacts')
        ->whereRaw("is_pelanggan = 1 {$whereCustomer}")
        ->select('id','name')->get();
    $details_query = "FROM receivable_details rd "
        . "LEFT JOIN receivables r ON rd.header_id = r.id "
        . "LEFT JOIN type_transactions t ON rd.type_transaction_id = t.id "
        . "WHERE 1 = 1 ";
    if(isset($request->company_id))
        $details_query .= "AND r.company_id = {$request->company_id} ";
    $saldo_query = "SELECT IFNULL(SUM(rd.debet-rd.credit),0) as saldo $details_query ";
    $saldo_debet_query = "SELECT IFNULL(SUM(rd.debet),0) as saldo  ";
    $saldo_credit_query = "SELECT IFNULL(SUM(rd.credit),0) as saldo  ";
    foreach ($customer as $value) {
        $saldo_sql = $saldo_query . "AND r.contact_id = {$value->id} ";
        $saldo_debet_sql = $saldo_debet_query . $details_query . "AND r.contact_id = {$value->id} ";
        $saldo_credit_sql = $saldo_credit_query . $details_query . "AND r.contact_id = {$value->id} ";
        $saldo_awal_sql = DB::select($saldo_sql . $whereSaldoAwal)[0];
        $saldo_akhir_sql = DB::select($saldo_sql . $whereSaldoAkhir)[0];
        $saldo_debet_sql = DB::select($saldo_debet_sql . $whereSaldoAkhir)[0];
        $saldo_credit_sql = DB::select($saldo_credit_sql . $whereSaldoAkhir)[0];
        $details = DB::select("SELECT rd.date_transaction, rd.code, rd.description, rd.debet, rd.credit "
            . $details_query
            . "AND rd.date_transaction BETWEEN '{$start_date}' AND '{$end_date}' "
            . "AND r.contact_id = {$value->id}");
        $saldo_awal = $saldo_awal_sql->saldo;
        $saldo_akhir = $saldo_akhir_sql->saldo;
        $saldo_debet = $saldo_debet_sql->saldo;
        $saldo_credit = $saldo_credit_sql->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && ($saldo_akhir != 0 || count($details) > 0)) || !$prevent_zero) {
            $data['data'] []= [
                'contact' => [
                    'name' => $value->name,
                ],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => $details
            ];
        }
    }
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name='semua cabang';
    }
    $data['company']=$name;
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'buku_besar_piutang';
// dd($datas);
// return $datas;
    return Excel::download(New BukuBesarPiutang($data), "$fileName$now.xlsx");
  }
  public function export_ledger_payable(Request $request)
  {
    // 1. Tanggal awal dan akhir
    $start_date = $request->filled('start_date') ? date('Y-m-d', strtotime($request->start_date)) : now()->startOfMonth()->format('Y-m-d');
    $end_date = $request->filled('end_date') ? date('Y-m-d', strtotime($request->end_date)) : now()->format('Y-m-d');

    // 2. Ambil supplier/vendor
    $contacts = DB::table('contacts')
        ->where(function ($q) {
            $q->where('is_supplier', 1)->orWhere('is_vendor', 1);
        })
        ->where('vendor_status_approve', 2)
        ->when($request->filled('customer_id'), fn($q) => $q->where('id', $request->customer_id))
        ->when($request->filled('supplier_id'), fn($q) => $q->where('id', $request->supplier_id))
        ->select('id', 'name')
        ->get();

    $data = ['data' => []];

    foreach ($contacts as $contact) {
        $transactions = DB::table('payable_details as pd')
            ->join('payables as p', 'pd.header_id', '=', 'p.id')
            ->join('journals as j', 'p.journal_id', '=', 'j.id')
            ->leftJoin('type_transactions as t', 'pd.type_transaction_id', '=', 't.id')
            ->leftJoin('debt_details as dd', 'dd.payable_id', '=', 'p.id')
            ->where('p.contact_id', $contact->id)
            ->where('j.status', 3)
            ->when($request->filled('company_id'), fn($q) => $q->where('p.company_id', $request->company_id))
            ->selectRaw('
              pd.date_transaction, 
              pd.debet, 
              pd.credit, 
              pd.code, 
              IF(pd.description is null, dd.description, pd.description) as description
            ')
            ->groupBy('pd.id')
            ->get();

        // Filter saldo awal, akhir, debet, kredit
        $saldo_awal = $transactions
            ->where('date_transaction', '<', $start_date)
            ->sum(fn($t) => $t->credit - $t->debet);

        $filtered = $transactions->filter(function ($t) use ($start_date, $end_date) {
                      return $t->date_transaction >= $start_date && $t->date_transaction <= $end_date;
                    });

        $saldo_akhir = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum(fn($t) => $t->credit - $t->debet);

        $saldo_debet = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum('debet');

        $saldo_credit = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum('credit');

        $details = $filtered->map(function ($item) {
            return [
                'date_transaction' => $item->date_transaction,
                'code' => $item->code,
                'description' => $item->description,
                'debet' => $item->debet,
                'credit' => $item->credit
            ];
        })->values();

        $preventZero = filter_var($request->prevent_zero, FILTER_VALIDATE_BOOLEAN);

        if (!$preventZero || ($saldo_akhir != 0 || $details->count() > 0)) {
            $data['data'][] = [
                'contact' => ['name' => $contact->name],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => collect($details)->map(fn($d) => (object) $d),
            ];
        }
    }

    // 4. Data tambahan
    $company = Company::find($request->company_id);
    $data['company'] = $company->name ?? 'semua cabang';
    $data['start'] = $start_date;
    $data['end'] = $end_date;

    // 5. Ekspor atau gagal
    if (empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    return PDF::loadView('export.ledger_payable', $data)->stream('Buku Besar Hutang.pdf');
  }

  public function export_ledger_payable_excel(Request $request)
  {
    // 1. Tanggal awal dan akhir
    $start_date = $request->filled('start_date') ? date('Y-m-d', strtotime($request->start_date)) : now()->startOfMonth()->format('Y-m-d');
    $end_date = $request->filled('end_date') ? date('Y-m-d', strtotime($request->end_date)) : now()->format('Y-m-d');

    // 2. Ambil supplier/vendor
    $contacts = DB::table('contacts')
        ->where(function ($q) {
            $q->where('is_supplier', 1)->orWhere('is_vendor', 1);
        })
        ->where('vendor_status_approve', 2)
        ->when($request->filled('customer_id'), fn($q) => $q->where('id', $request->customer_id))
        ->when($request->filled('supplier_id'), fn($q) => $q->where('id', $request->supplier_id))
        ->select('id', 'name')
        ->get();

    $data = ['data' => []];

    foreach ($contacts as $contact) {
        $transactions = DB::table('payable_details as pd')
            ->join('payables as p', 'pd.header_id', '=', 'p.id')
            ->join('journals as j', 'p.journal_id', '=', 'j.id')
            ->leftJoin('type_transactions as t', 'pd.type_transaction_id', '=', 't.id')
            ->leftJoin('debt_details as dd', 'dd.payable_id', '=', 'p.id')
            ->where('p.contact_id', $contact->id)
            ->where('j.status', 3)
            ->when($request->filled('company_id'), fn($q) => $q->where('p.company_id', $request->company_id))
            ->selectRaw('
              pd.date_transaction, 
              pd.debet, 
              pd.credit, 
              pd.code, 
              IF(pd.description is null, dd.description, pd.description) as description
            ')
            ->groupBy('pd.id')
            ->get();

        // Filter saldo awal, akhir, debet, kredit
        $saldo_awal = $transactions
            ->where('date_transaction', '<', $start_date)
            ->sum(fn($t) => $t->credit - $t->debet);

        $filtered = $transactions->filter(function ($t) use ($start_date, $end_date) {
                      return $t->date_transaction >= $start_date && $t->date_transaction <= $end_date;
                    });

        $saldo_akhir = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum(fn($t) => $t->credit - $t->debet);

        $saldo_debet = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum('debet');

        $saldo_credit = $transactions
            ->where('date_transaction', '<=', $end_date)
            ->sum('credit');

        $details = $filtered->map(function ($item) {
            return [
                'date_transaction' => $item->date_transaction,
                'code' => $item->code,
                'description' => $item->description,
                'debet' => $item->debet,
                'credit' => $item->credit
            ];
        })->values();

        $preventZero = filter_var($request->prevent_zero, FILTER_VALIDATE_BOOLEAN);

        if (!$preventZero || ($saldo_akhir != 0 || $details->count() > 0)) {
            $data['data'][] = [
                'contact' => ['name' => $contact->name],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => collect($details)->map(fn($d) => (object) $d),
            ];
        }
    }

    // 4. Data tambahan
    $company = Company::find($request->company_id);
    $data['company'] = $company->name ?? 'semua cabang';
    $data['start'] = $start_date;
    $data['end'] = $end_date;

    // 5. Ekspor atau gagal
    if (empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }

    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'buku_besar_hutang';
    return Excel::download(New BukuBesarHutang($data), "$fileName$now.xlsx");
  }

  public function export_ledger_payable_old(Request $request)
  {
    $start_date = (isset($request->start_date)) ?
        date('Y-m-d', strtotime($request->start_date)) :
        Carbon::parse('first day of this month')->format('Y-m-d');
    $end_date = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
    $whereCustomer = (isset($request->customer_id)) ? " AND id = {$request->customer_id} " : "";
    $whereSaldoAwal = "AND pd.date_transaction < '{$start_date}' ";
    $whereSaldoAkhir = "AND pd.date_transaction <= '{$end_date}' ";
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" and pd.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND pd.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND p.company_id = $request->company_id";
      $wr2.=" AND p.company_id = $request->company_id";
    }
    $wr3="1=1";
    if (isset($request->supplier_id)) {
      $wr3.=" AND id = ".$request->supplier_id;
    }
    $data=["data" => []];
    $customer=DB::table('contacts')
        ->whereRaw("(is_supplier=1 or is_vendor=1) {$whereCustomer}")
        ->where('vendor_status_approve', 2)
        ->whereRaw($wr3)
        ->select('id','name')->get();
    $details_query = "FROM payable_details pd "
        . "LEFT JOIN payables p ON pd.header_id = p.id "
        . "LEFT JOIN type_transactions t ON pd.type_transaction_id = t.id "
        . "JOIN journals j ON j.id = p.journal_id and j.status=3 "
        . "WHERE 1 = 1 ";
    if(isset($request->company_id))
        $details_query .= "AND p.company_id = {$request->company_id} ";
    $saldo_query = "SELECT IFNULL(SUM(pd.credit-pd.debet),0) as saldo  ";
    $saldo_debet_query = "SELECT IFNULL(SUM(pd.debet),0) as saldo  ";
    $saldo_credit_query = "SELECT IFNULL(SUM(pd.credit),0) as saldo  ";
    foreach ($customer as $value) {
        $saldo_sql = $saldo_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_debet_sql = $saldo_debet_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_credit_sql = $saldo_credit_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_awal_sql = DB::select($saldo_sql . $whereSaldoAwal)[0];
        $saldo_akhir_sql = DB::select($saldo_sql . $whereSaldoAkhir)[0];
        $saldo_debet_sql = DB::select($saldo_debet_sql . $whereSaldoAkhir)[0];
        $saldo_credit_sql = DB::select($saldo_credit_sql . $whereSaldoAkhir)[0];
        $details = DB::select("SELECT pd.date_transaction, pd.code, pd.description, pd.debet, pd.credit "
            . $details_query
            . "AND pd.date_transaction BETWEEN '{$start_date}' AND '{$end_date}' "
            . "AND p.contact_id = {$value->id}");
        $saldo_awal = $saldo_awal_sql->saldo;
        $saldo_akhir = $saldo_akhir_sql->saldo;
        $saldo_debet = $saldo_debet_sql->saldo;
        $saldo_credit = $saldo_credit_sql->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && ($saldo_akhir != 0 || count($details) > 0)) || !$prevent_zero) {
            $data['data'] []= [
                'contact' => [
                    'name' => $value->name,
                ],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => $details
            ];
        }
    }
    $data['company']=Company::find($request->company_id);
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    // dd($data);
    return PDF::loadView('export.ledger_payable', $data)->stream('Buku Besar Hutang.pdf');
  }
  
  public function export_ledger_payable_excel_old(Request $request)
  {
    $start_date = (isset($request->start_date)) ?
        date('Y-m-d', strtotime($request->start_date)) :
        Carbon::parse('first day of this month')->format('Y-m-d');
    $end_date = (isset($request->end_date)) ?
        date('Y-m-d', strtotime($request->end_date)) :
        Carbon::now()->format('Y-m-d');
    $whereCustomer = (isset($request->customer_id)) ? " AND id = {$request->customer_id} " : "";
    $whereSaldoAwal = "AND pd.date_transaction < '{$start_date}' ";
    $whereSaldoAkhir = "AND pd.date_transaction <= '{$end_date}' ";
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" and pd.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND pd.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND p.company_id = $request->company_id";
      $wr2.=" AND p.company_id = $request->company_id";
    }
    $wr3="1=1";
    if (isset($request->supplier_id)) {
      $wr3.=" AND id = ".$request->supplier_id;
    }
    $data=["data" => []];
    $customer=DB::table('contacts')
        ->whereRaw("(is_supplier=1 or is_vendor=1) {$whereCustomer}")
        ->where('vendor_status_approve', 2)
        ->whereRaw($wr3)
        ->select('id','name')->get();
    $details_query = "FROM payable_details pd "
        . "LEFT JOIN payables p ON pd.header_id = p.id "
        . "LEFT JOIN type_transactions t ON pd.type_transaction_id = t.id "
        . "JOIN journals j ON j.id = p.journal_id and j.status=3 "
        . "WHERE 1 = 1 ";
    if(isset($request->company_id))
        $details_query .= "AND p.company_id = {$request->company_id} ";
    $saldo_query = "SELECT IFNULL(SUM(pd.credit-pd.debet),0) as saldo  ";
    $saldo_debet_query = "SELECT IFNULL(SUM(pd.debet),0) as saldo  ";
    $saldo_credit_query = "SELECT IFNULL(SUM(pd.credit),0) as saldo  ";
    foreach ($customer as $value) {
        $saldo_sql = $saldo_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_debet_sql = $saldo_debet_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_credit_sql = $saldo_credit_query . $details_query . "AND p.contact_id = {$value->id} ";
        $saldo_awal_sql = DB::select($saldo_sql . $whereSaldoAwal)[0];
        $saldo_akhir_sql = DB::select($saldo_sql . $whereSaldoAkhir)[0];
        $saldo_debet_sql = DB::select($saldo_debet_sql . $whereSaldoAkhir)[0];
        $saldo_credit_sql = DB::select($saldo_credit_sql . $whereSaldoAkhir)[0];
        $details = DB::select("SELECT pd.date_transaction, pd.code, pd.description, pd.debet, pd.credit "
            . $details_query
            . "AND pd.date_transaction BETWEEN '{$start_date}' AND '{$end_date}' "
            . "AND p.contact_id = {$value->id}");
        $saldo_awal = $saldo_awal_sql->saldo;
        $saldo_akhir = $saldo_akhir_sql->saldo;
        $saldo_debet = $saldo_debet_sql->saldo;
        $saldo_credit = $saldo_credit_sql->saldo;
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && ($saldo_akhir != 0 || count($details) > 0)) || !$prevent_zero) {
            $data['data'] []= [
                'contact' => [
                    'name' => $value->name,
                ],
                'saldo_awal' => $saldo_awal,
                'saldo_akhir' => $saldo_akhir,
                'saldo_debet' => $saldo_debet,
                'saldo_credit' => $saldo_credit,
                'detail' => $details
            ];
        }
    }
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name='semua cabang';
    }
    $data['company']=$name;
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'buku_besar_hutang';
// dd($datas);
// return $datas;
    return Excel::download(New BukuBesarHutang($data), "$fileName$now.xlsx");
  }
  public function export_ledger_um_supplier(Request $request)
  {
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND um_supplier_details.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND um_supplier_details.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND um_suppliers.company_id = $request->company_id";
      $wr2.=" AND um_suppliers.company_id = $request->company_id";
    }
    $wr3="1=1";
    if (isset($request->supplier_id)) {
      $wr3.=" AND id = ".$request->supplier_id;
    }
    $data=[];
    $customer=Contact::whereRaw("(is_supplier=1 or is_vendor=1)")->where('vendor_status_approve', 2)->whereRaw($wr3)->select('id','name')->get();
    foreach ($customer as $value) {
      $sqlsaldo1="select ifnull(sum(um_supplier_details.debet-um_supplier_details.credit),0) as saldo
      from um_supplier_details
      left join um_suppliers on um_suppliers.id = um_supplier_details.header_id
      left join type_transactions on type_transactions.id = um_supplier_details.type_transaction_id
      where type_transactions.slug like '%saldo%' and um_suppliers.contact_id = $value->id and $wr";
      $saldo=DB::select($sqlsaldo1)[0]->saldo;
      if ($saldo<=0) {
        $sqlsaldo1="select ifnull(sum(um_supplier_details.debet-um_supplier_details.credit),0) as saldo
        from um_supplier_details
        left join um_suppliers on um_suppliers.id = um_supplier_details.header_id
        left join type_transactions on type_transactions.id = um_supplier_details.type_transaction_id
        where um_suppliers.contact_id = $value->id and um_supplier_details.date_transaction < '$dt_start' $wr2";
        $saldo=DB::select($sqlsaldo1)[0]->saldo;
      }
      $sql="SELECT um_supplier_details.date_transaction, um_supplier_details.code, um_supplier_details.description, um_supplier_details.debet, um_supplier_details.credit FROM um_supplier_details LEFT JOIN um_suppliers ON um_suppliers.id = um_supplier_details.header_id left join type_transactions on type_transactions.id = um_supplier_details.type_transaction_id WHERE $wr and type_transactions.slug not like '%saldo%' AND um_suppliers.contact_id = $value->id";
      $dt=DB::select($sql);
      $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
      if(($prevent_zero && $saldo != 0) || !$prevent_zero) {
        $data[]=[
            'contact' => [
            'name' => $value->name,
            ],
            'saldo' => $saldo,
            'detail' => $dt
        ];
      }
    }
    // dd($data);
    $data['data']=$data;
    $data['company']=Company::find($request->company_id);
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    // return SnappyPdf::loadView('export.ledger_um_supplier', $data)->stream();
    return PDF::loadView('export.ledger_um_supplier', $data)->stream();
  }
  public function export_ledger_um_customer(Request $request)
  {
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND um_customer_details.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND um_customer_details.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND um_customers.company_id = $request->company_id";
      $wr2.=" AND um_customers.company_id = $request->company_id";
    }
    $dt_end = isset($request->end_date) ?
        date('Y-m-d', strtotime($request->end_date)) :
        date('Y-m-d');
    $wr3="1=1";
    if (isset($request->customer_id)) {
      $wr3.=" AND id = ".$request->customer_id;
    }
    $data=[];
    $customer=Contact::whereRaw("is_pelanggan=1")->whereRaw($wr3)->select('id','name')->get();
    foreach ($customer as $value) {
      $sqlsaldo1="select ifnull(sum(um_customer_details.credit-um_customer_details.debet),0) as saldo
      from um_customer_details
      left join um_customers on um_customers.id = um_customer_details.header_id
      left join type_transactions on type_transactions.id = um_customer_details.type_transaction_id
      where type_transactions.slug like '%saldo%' and um_customers.contact_id = $value->id and $wr";
      $saldo=DB::select($sqlsaldo1)[0]->saldo;
      if ($saldo<=0) {
        $sqlsaldo1="select ifnull(sum(um_customer_details.credit-um_customer_details.debet),0) as saldo
        from um_customer_details
        left join um_customers on um_customers.id = um_customer_details.header_id
        left join type_transactions on type_transactions.id = um_customer_details.type_transaction_id
        where um_customers.contact_id = $value->id and um_customer_details.date_transaction < '$dt_start' $wr2";
        $saldo=DB::select($sqlsaldo1)[0]->saldo;
      }
      $sqlsaldo_akhir="select ifnull(sum(um_customer_details.credit-um_customer_details.debet),0) as saldo
        from um_customer_details
        left join um_customers on um_customers.id = um_customer_details.header_id
        left join type_transactions on type_transactions.id = um_customer_details.type_transaction_id
        where um_customers.contact_id = $value->id and um_customer_details.date_transaction < '$dt_end' $wr2";
      $saldo_akhir = DB::select($sqlsaldo_akhir)[0]->saldo;
      $sql="SELECT um_customer_details.date_transaction, um_customer_details.code, um_customer_details.description, um_customer_details.debet, um_customer_details.credit FROM um_customer_details LEFT JOIN um_customers ON um_customers.id = um_customer_details.header_id left join type_transactions on type_transactions.id = um_customer_details.type_transaction_id WHERE $wr and type_transactions.slug not like '%saldo%' AND um_customers.contact_id = $value->id";
      $dt=DB::select($sql);
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && $saldo_akhir != 0) || !$prevent_zero) {
            $data[]=[
                'contact' => [
                    'name' => $value->name,
                ],
                'saldo' => $saldo,
                'detail' => $dt
            ];
        }
    }
    $data['data']=$data;
    $data['company']=Company::find($request->company_id);
    $data['start']=$request->start_date;
    $data['end']=$request->end_date;
    if(empty($data['data'])) {
        echo("Data Tidak ditemukan!!");
        return;
    }
    // return SnappyPdf::loadView('export.ledger_um_customer', $data)->stream();
    return PDF::loadView('export.ledger_um_customer', $data)->stream();
  }
  /*
      Date : 18-04-2020
      Description : Export neraca saldo
      Developer : Didin
      Status : Edit
  */
  public function export_neraca_saldo(Request $request)
  {
    
    $acc_default = DB::table('account_defaults')->first();
    $accounts = DB::table('accounts')
        ->select('id', 'code', 'name', 'deep', 'is_base', 'group_report')
        ->orderBy('code')
        ->get();

    $start = $request->start_date ? Carbon::parse($request->start_date) : null;
    $end = $request->end_date ? Carbon::parse($request->end_date) : null;

    // Ambil total transaksi jurnal dalam periode
    $query1 = DB::table('journal_details as jd')
        ->leftJoin('journals as j', 'jd.header_id', '=', 'j.id')
        ->where('j.status', 3);

    if ($start && $end) {
        $query1->whereBetween('j.date_transaction', [$start, $end]);
    }

    if ($request->company_id) {
        $query1->where('j.company_id', $request->company_id);
    }

    $query1->where('j.is_audit', $request->is_audit ?? 0);

    $journalDetails = $query1->select(
        'jd.account_id',
        DB::raw('SUM(jd.debet) as total_debet'),
        DB::raw('SUM(jd.credit) as total_credit')
    )
    ->groupBy('jd.account_id')
    ->get()
    ->keyBy('account_id');

    // Ambil saldo awal (sebelum start date)
    $query2 = DB::table('journal_details as jd')
        ->leftJoin('journals as j', 'jd.header_id', '=', 'j.id')
        ->where('j.status', 3);

    if ($start) {
        $query2->where('j.date_transaction', '<', $start);
    }

    $saldoAwal = $query2->select(
        'jd.account_id',
        DB::raw('SUM(jd.debet) as total_debet'),
        DB::raw('SUM(jd.credit) as total_credit')
    )
    ->groupBy('jd.account_id')
    ->get()
    ->keyBy('account_id');

    $data = [];
    $laba = 0;
    $rugi = 0;

    foreach ($accounts as $acc) {
        // if ($acc->id == $acc_default->laba_bulan_berjalan) {
        //     $data[] = [
        //         'id' => $acc->id,
        //         'code' => $acc->code,
        //         'name' => $acc->name,
        //         'deep' => $acc->deep,
        //         'is_base' => $acc->is_base,
        //         'debet' => 0,
        //         'credit' => 0,
        //     ];
        //     continue;
        // }

        $debet = 0;
        $credit = 0;

        if (isset($journalDetails[$acc->id])) {
            $debet += $journalDetails[$acc->id]->total_debet;
            $credit += $journalDetails[$acc->id]->total_credit;
        }

        if (isset($saldoAwal[$acc->id])) {
            $debet += $saldoAwal[$acc->id]->total_debet;
            $credit += $saldoAwal[$acc->id]->total_credit;
        }

        // if ($acc->group_report == 2) {
        //     $laba += $credit;
        //     $rugi += $debet;
        //     $debet_j = 0;
        //     $credit_j = 0;
        // } else {
            if ($debet > $credit) {
                $debet_j = $debet - $credit;
                $credit_j = 0;
            } else {
                $debet_j = 0;
                $credit_j = $credit - $debet;
            }
        // }

        if ($request->prevent_zero == 'true' && $debet_j == $credit_j && $acc->is_base != 1) {
            continue;
        }

        $data[] = [
            'id' => $acc->id,
            'code' => $acc->code,
            'name' => $acc->name,
            'deep' => $acc->deep,
            'is_base' => $acc->is_base,
            'debet' => $debet_j,
            'credit' => $credit_j,
        ];
    }

    // Akun laba bulan berjalan
    if ($laba > $rugi) {
        $db = 0;
        $cr = $laba - $rugi;
    } else {
        $db = $rugi - $laba;
        $cr = 0;
    }

    // foreach ($data as &$val) {
    //     if ($val['id'] == $acc_default->laba_bulan_berjalan) {
    //         $val['debet'] = $db;
    //         $val['credit'] = $cr;
    //     }
    // }

    $data = array_filter($data, function ($val) use ($request) {
        if ($request->prevent_zero == 'true') {
            return !($val['debet'] == $val['credit'] && $val['is_base'] != 1);
        }
        return true;
    });

    $company = Company::find($request->company_id);
    $datas = [
        'data' => array_values($data),
        'start' => $request->start_date,
        'end' => $request->end_date,
        'company' => $company ? $company->name : 'semua cabang',
        'count' => count($data),
    ];

    return PDF::loadView('export.neraca_saldo_2', $datas)->stream();
  }
  /*
      Date : 14-12-2022
      Description : Export excel neraca saldo
      Developer : alwi
      Status : Add
  */

  public function export_neraca_saldo_excel(Request $request)
  {
    $acc_default = DB::table('account_defaults')->first();
    $accounts = DB::table('accounts')
        ->select('id', 'code', 'name', 'deep', 'is_base', 'group_report')
        ->orderBy('code')
        ->get();

    $start = $request->start_date ? Carbon::parse($request->start_date) : null;
    $end = $request->end_date ? Carbon::parse($request->end_date) : null;

    // Ambil total transaksi jurnal dalam periode
    $query1 = DB::table('journal_details as jd')
        ->leftJoin('journals as j', 'jd.header_id', '=', 'j.id')
        ->where('j.status', 3);

    if ($start && $end) {
        $query1->whereBetween('j.date_transaction', [$start, $end]);
    }

    if ($request->company_id) {
        $query1->where('j.company_id', $request->company_id);
    }

    $query1->where('j.is_audit', $request->is_audit ?? 0);

    $journalDetails = $query1->select(
        'jd.account_id',
        DB::raw('SUM(jd.debet) as total_debet'),
        DB::raw('SUM(jd.credit) as total_credit')
    )
    ->groupBy('jd.account_id')
    ->get()
    ->keyBy('account_id');

    // Ambil saldo awal (sebelum start date)
    $query2 = DB::table('journal_details as jd')
        ->leftJoin('journals as j', 'jd.header_id', '=', 'j.id')
        ->where('j.status', 3);

    if ($start) {
        $query2->where('j.date_transaction', '<', $start);
    }

    $saldoAwal = $query2->select(
        'jd.account_id',
        DB::raw('SUM(jd.debet) as total_debet'),
        DB::raw('SUM(jd.credit) as total_credit')
    )
    ->groupBy('jd.account_id')
    ->get()
    ->keyBy('account_id');

    $data = [];
    $laba = 0;
    $rugi = 0;

    foreach ($accounts as $acc) {
        // if ($acc->id == $acc_default->laba_bulan_berjalan) {
        //     $data[] = [
        //         'id' => $acc->id,
        //         'code' => $acc->code,
        //         'name' => $acc->name,
        //         'deep' => $acc->deep,
        //         'is_base' => $acc->is_base,
        //         'debet' => 0,
        //         'credit' => 0,
        //     ];
        //     continue;
        // }

        $debet = 0;
        $credit = 0;

        if (isset($journalDetails[$acc->id])) {
            $debet += $journalDetails[$acc->id]->total_debet;
            $credit += $journalDetails[$acc->id]->total_credit;
        }

        if (isset($saldoAwal[$acc->id])) {
            $debet += $saldoAwal[$acc->id]->total_debet;
            $credit += $saldoAwal[$acc->id]->total_credit;
        }

        // if ($acc->group_report == 2) {
        //     $laba += $credit;
        //     $rugi += $debet;
        //     $debet_j = 0;
        //     $credit_j = 0;
        // } else {
            if ($debet > $credit) {
                $debet_j = $debet - $credit;
                $credit_j = 0;
            } else {
                $debet_j = 0;
                $credit_j = $credit - $debet;
            }
        // }

        if ($request->prevent_zero == 'true' && $debet_j == $credit_j && $acc->is_base != 1) {
            continue;
        }

        $data[] = [
            'id' => $acc->id,
            'code' => $acc->code,
            'name' => $acc->name,
            'deep' => $acc->deep,
            'is_base' => $acc->is_base,
            'debet' => $debet_j,
            'credit' => $credit_j,
        ];
    }

    // Akun laba bulan berjalan
    if ($laba > $rugi) {
        $db = 0;
        $cr = $laba - $rugi;
    } else {
        $db = $rugi - $laba;
        $cr = 0;
    }

    // foreach ($data as &$val) {
    //     if ($val['id'] == $acc_default->laba_bulan_berjalan) {
    //         $val['debet'] = $db;
    //         $val['credit'] = $cr;
    //     }
    // }

    $data = array_filter($data, function ($val) use ($request) {
        if ($request->prevent_zero == 'true') {
            return !($val['debet'] == $val['credit'] && $val['is_base'] != 1);
        }
        return true;
    });

    $company = Company::find($request->company_id);
    $datas = [
        'data' => array_values($data),
        'start' => $request->start_date,
        'end' => $request->end_date,
        'company' => $company ? $company->name : 'semua cabang',
        'count' => count($data),
    ];

    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'neraca_saldo';

    return Excel::download(New NeracaSaldo($datas), "$fileName$now.xlsx");
  }
  public function export_neraca_saldo_old(Request $request)
  {
    $acc_default=DB::table('account_defaults')->first();
    $accounts=DB::table('accounts')->selectRaw('id,code,name,deep,is_base,group_report')->orderBy('code','asc')->get();
    $data = array();
    $laba=0;
    $rugi=0;
    foreach ($accounts as $key => $value) {
      $debet=0;
      $credit=0;
      if ($value->id==$acc_default->laba_bulan_berjalan) {
        array_push($data,[
          'id' => $value->id,
          'code' => $value->code,
          'name' => $value->name,
          'deep' => $value->deep,
          'is_base' => $value->is_base,
          'debet' => 0,
          'credit' => 0,
        ]);
        continue;
      }
      $query=DB::table('journal_details')
      ->leftJoin('journals','journals.id','journal_details.header_id')
      ->where('journal_details.account_id', $value->id)
      ->where('journals.status', 3);
      if ($request->start_date&&$request->end_date) {
        $start=Carbon::parse($request->start_date);
        $end=Carbon::parse($request->end_date);
        $query=$query->whereBetween('journals.date_transaction',[$start,$end]);
      }
      $query->whereIsAudit( $request->is_audit ?? 0 );
      if ($request->company_id) {
        $query=$query->where('journals.company_id', $request->company_id);
      }
      $query=$query->selectRaw('journal_details.debet, journal_details.credit')->orderBy('journal_details.id')->chunk(50, function($chunk) use (&$debet,&$credit) {
        foreach ($chunk as $chk) {
          $debet+=$chk->debet;
          $credit+=$chk->credit;
        }
      });
      // Selisih
      if ($value->group_report==2) {
        $laba+=$credit;
        $rugi+=$debet;
        $debet_j=0;
        $credit_j=0;
      } else {
        if ($debet>$credit) {
          $debet_j=$debet-$credit;
          $credit_j=0;
        } else {
          $debet_j=0;
          $credit_j=$credit-$debet;
        }
      }
      /* TAMPILKAN TIDAK 0
      if ($request->prevent_zero) {
        if ($debet_j==$credit_j) {
          continue;
        }
      }
      */
      array_push($data,[
        'id' => $value->id,
        'code' => $value->code,
        'name' => $value->name,
        'deep' => $value->deep,
        'is_base' => $value->is_base,
        'debet' => $debet_j,
        'credit' => $credit_j,
      ]);
    }
    if ($laba>$rugi) {
      $db=0;
      $cr=$laba-$rugi;
    } else {
      $db=$rugi-$laba;
      $cr=0;
    }
    $data=collect($data)->map(function($val,$key) use ($acc_default,$db,$cr,$request){
      if ($acc_default->laba_bulan_berjalan==$val['id']) {
        $val['debet']=$db;
        $val['credit']=$cr;
      }
      if ($request->prevent_zero=='true') {
        if ($val['debet']==$val['credit']&&$val['is_base']!=1) {
          return null;
        }
      }
      return $val;
    });
    $datas = [
      'data' => $data,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => Company::find($request->company_id)
    ];
    // return SnappyPdf::loadView('export.neraca_saldo_2', $datas)->stream();
    return PDF::loadView('export.neraca_saldo_2', $datas)->stream();
  }
  /*
      Date : 14-12-2022
      Description : Export excel neraca saldo
      Developer : alwi
      Status : Add
  */
  public function export_neraca_saldo_excel_old(Request $request)
  {
    $acc_default=DB::table('account_defaults')->first();
    $accounts=DB::table('accounts')->selectRaw('id,code,name,deep,is_base,group_report')->orderBy('code','asc')->get();
    $data = array();
    $laba=0;
    $rugi=0;
    foreach ($accounts as $key => $value) {
      $debet=0;
      $credit=0;
      if ($value->id==$acc_default->laba_bulan_berjalan) {
        array_push($data,[
          'id' => $value->id,
          'code' => $value->code,
          'name' => $value->name,
          'deep' => $value->deep,
          'is_base' => $value->is_base,
          'debet' => 0,
          'credit' => 0,
        ]);
        continue;
      }
      $query=DB::table('journal_details')
      ->leftJoin('journals','journals.id','journal_details.header_id')
      ->where('journal_details.account_id', $value->id)
      ->where('journals.status', 3);
      if ($request->start_date&&$request->end_date) {
        $start=Carbon::parse($request->start_date);
        $end=Carbon::parse($request->end_date);
        $query=$query->whereBetween('journals.date_transaction',[$start,$end]);
      }
      $query->whereIsAudit( $request->is_audit ?? 0 );
      if ($request->company_id) {
        $query=$query->where('journals.company_id', $request->company_id);
      }
      $query=$query->selectRaw('journal_details.debet, journal_details.credit')->orderBy('journal_details.id')->chunk(50, function($chunk) use (&$debet,&$credit) {
        foreach ($chunk as $chk) {
          $debet+=$chk->debet;
          $credit+=$chk->credit;
        }
      });
      // Selisih
      if ($value->group_report==2) {
        $laba+=$credit;
        $rugi+=$debet;
        $debet_j=0;
        $credit_j=0;
      } else {
        if ($debet>$credit) {
          $debet_j=$debet-$credit;
          $credit_j=0;
        } else {
          $debet_j=0;
          $credit_j=$credit-$debet;
        }
      }
      /* TAMPILKAN TIDAK 0
      if ($request->prevent_zero) {
        if ($debet_j==$credit_j) {
          continue;
        }
      }
      */
      array_push($data,[
        'id' => $value->id,
        'code' => $value->code,
        'name' => $value->name,
        'deep' => $value->deep,
        'is_base' => $value->is_base,
        'debet' => $debet_j,
        'credit' => $credit_j,
      ]);
    }
    if ($laba>$rugi) {
      $db=0;
      $cr=$laba-$rugi;
    } else {
      $db=$rugi-$laba;
      $cr=0;
    }
    $data=collect($data)->map(function($val,$key) use ($acc_default,$db,$cr,$request){
      if ($acc_default->laba_bulan_berjalan==$val['id']) {
        $val['debet']=$db;
        $val['credit']=$cr;
      }
      if ($request->prevent_zero=='true') {
        if ($val['debet']==$val['credit']&&$val['is_base']!=1) {
          return null;
        }
      }
      return $val;
    });
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name="semua cabang";
    }
    // dd($company->name);
    $datas = [
      'data' => $data,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => $name,
      'count' =>count($data)
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'neraca_saldo';
// dd($datas);
// return $datas;
    return Excel::download(New NeracaSaldo($datas), "$fileName$now.xlsx");
    // return SnappyPdf::loadView('export.neraca_saldo_2', $datas)->stream();
    // return PDF::loadView('export.neraca_saldo_2', $datas)->stream();
  }
  /*
      Date : 18-04-2020
      Description : Export laba rugi
      Developer : Didin
      Status : Edit
  */
  public function export_laba_rugi(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="";
    if (isset($request->start_month) && isset($request->end_month)) {
      
      $splitStart = explode("-", $request->start_month);
      $splitEnd = explode("-", $request->end_month);
  
      $yearStartSplitted = $splitStart[1];
      $yearEndSplitted = $splitEnd[1];
  
      $monthStartSplitted = $splitStart[0];
      $monthEndSplitted = $splitEnd[0];
  
      $formatReqStart = $yearStartSplitted."-".$monthStartSplitted;
      $formatReqEnd = $yearEndSplitted."-".$monthEndSplitted;

      if ($formatReqStart != $formatReqEnd) {
        $wr .= " AND DATE_FORMAT(journals.date_transaction, '%Y-%m') BETWEEN '".$formatReqStart."' AND '".$formatReqEnd."'";
      } else {
        $wr .=" AND DATE_FORMAT(journals.date_transaction, '%Y-%m') = '$formatReqStart'";
      }
      // $wr .= " AND journals.date_transaction <= '" . date('Y-m-t', strtotime($formatReqEnd."-01")) . "'";
    } else {
        $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
        $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $is_audit = $request->is_audit ?? 0;
      $wr.=" AND journals.is_audit = $is_audit";
      $wr2.=" AND journals.is_audit = $is_audit";
    $wr1 = "";
    if (isset($request->is_not_zero)) {
      $wr1 .= " WHERE
        ( det.jml_debet != 0
        OR det.jml_credit != 0 ) ";
    }
    $parent=Account::where('group_report', 2)->orderBy('code')->get();
    $datas=[];
    // dd($parent);
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM( journal_details.credit - journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3 AND journals.type_transaction_id != 53
      ";
      $tarik=DB::select($sql)[0];
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && $tarik->amount != 0) || !$prevent_zero) {
            $datas []= [
                'id' => $value->id,
                'deep' => $value->deep,
                'code' => $value->code,
                'name' => $value->name,
                'jenis' => $value->jenis,
                'is_base' => $value->is_base,
                'parent' => $value->parent_id,
                'amount' => $tarik->amount
            ];
        }
    }
    
    $laba_rugi_tambahan = [
      (object) [
        'id' => 'LR',
        'deep' => 0,
        'code' => '-',
        'name' => 'LABA RUGI',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR',
        'deep' => 1,
        'code' => '-',
        'name' => 'LABA RUGI KOTOR PENJUALAN',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR1',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA  PENGURUSAN TRANSP',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transportasi Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-01',
        'account_code_credit' => '4000-01',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-02',
        'account_code_credit' => '4000-02',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-03',
        'account_code_credit' => '4000-03',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-04',
        'account_code_credit' => '4000-04',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR2',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA ATAS ONGKOS ANGKUT',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-01',
        'account_code_credit' => '4001-01',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-02',
        'account_code_credit' => '4001-02',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-03',
        'account_code_credit' => '4001-03',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-04',
        'account_code_credit' => '4001-04',
        'amount' => 0
      ],
    ];

    $datas = collect($datas);
    $new_data = [];

    foreach ($datas as $key => $value) {
      $new_data[] = $value;
      if ($value['code'] == '5001-04') {
        foreach ($laba_rugi_tambahan as $key => $val) {
          $amount = 0;
          if ($val->account_code_credit != '000') {
            $debet = $datas->where('code', $val->account_code_debet)->first()['amount'];
            $credit = $datas->where('code', $val->account_code_credit)->first()['amount'];
            $amount = $credit - $debet;
          }
          $new_data[] = [
            'id' => $val->id,
            'deep' => $val->deep,
            'code' => $val->code,
            'name' => $val->name,
            'jenis' => $val->jenis,
            'is_base' => $val->is_base,
            'parent' => $val->parent_id,
            'amount' => $amount
          ];
        }
      }
    }

    $datas = $new_data;

    $data = [
      'data' => $datas,
      'start' => $formatReqStart,
      'end' => $formatReqEnd,
      'company' => Company::find($request->company_id)
    ];
    // dd($data);
    // return SnappyPdf::loadView('export.laba_rugi2', $data)->stream();
    return PDF::loadView('export.laba_rugi2', $data)->stream();
  }
   /*
      Date : 15-12-2022
      Description : Export excel laba rugi
      Developer : alwi
      Status : Add
  */
  public function export_laba_rugi_excel(Request $request)
  {
    $wr="1=1";
    $wr2="";
    if (isset($request->start_month) && isset($request->end_month)) {
      
      $splitStart = explode("-", $request->start_month);
      $splitEnd = explode("-", $request->end_month);
  
      $yearStartSplitted = $splitStart[1];
      $yearEndSplitted = $splitEnd[1];
  
      $monthStartSplitted = $splitStart[0];
      $monthEndSplitted = $splitEnd[0];
  
      $formatReqStart = $yearStartSplitted."-".$monthStartSplitted;
      $formatReqEnd = $yearEndSplitted."-".$monthEndSplitted;

      if ($formatReqStart != $formatReqEnd) {
        $wr .= " AND DATE_FORMAT(journals.date_transaction, '%Y-%m') BETWEEN '".$formatReqStart."' AND '".$formatReqEnd."'";
      } else {
        $wr .=" AND DATE_FORMAT(journals.date_transaction, '%Y-%m') = '$formatReqStart'";
      }
      // $wr .= " AND journals.date_transaction <= '" . date('Y-m-t', strtotime($formatReqEnd."-01")) . "'";
    } else {
        $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
        $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $is_audit = $request->is_audit ?? 0;
      $wr.=" AND journals.is_audit = $is_audit";
      $wr2.=" AND journals.is_audit = $is_audit";
    $wr1 = "";
    if (isset($request->is_not_zero)) {
      $wr1 .= " WHERE
        ( det.jml_debet != 0
        OR det.jml_credit != 0 ) ";
    }
    $parent=Account::where('group_report', 2)->orderBy('code')->get();
    $datas=[];
    // dd($parent);
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet) ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3 AND journals.type_transaction_id != 53
      ";
      $tarik=DB::select($sql)[0];
        $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
        if(($prevent_zero && $tarik->amount != 0) || !$prevent_zero) {
            $datas []= [
                'id' => $value->id,
                'deep' => $value->deep,
                'code' => $value->code,
                'name' => $value->name,
                'jenis' => $value->jenis,
                'is_base' => $value->is_base,
                'parent' => $value->parent_id,
                'amount' => $tarik->amount
            ];
        }
    }
    
    $laba_rugi_tambahan = [
      (object) [
        'id' => 'LR',
        'deep' => 0,
        'code' => '-',
        'name' => 'LABA RUGI',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR',
        'deep' => 1,
        'code' => '-',
        'name' => 'LABA RUGI KOTOR PENJUALAN',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR1',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA  PENGURUSAN TRANSP',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transportasi Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-01',
        'account_code_credit' => '4000-01',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-02',
        'account_code_credit' => '4000-02',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-03',
        'account_code_credit' => '4000-03',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-04',
        'account_code_credit' => '4000-04',
        'amount' => 0
      ],
      (object) [
        'id' => 'LR2',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA ATAS ONGKOS ANGKUT',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-01',
        'account_code_credit' => '4001-01',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-02',
        'account_code_credit' => '4001-02',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-03',
        'account_code_credit' => '4001-03',
        'amount' => 0
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-04',
        'account_code_credit' => '4001-04',
        'amount' => 0
      ],
    ];

    $datas = collect($datas);
    $new_data = [];

    foreach ($datas as $key => $value) {
      $new_data[] = $value;
      if ($value['code'] == '5001-04') {
        foreach ($laba_rugi_tambahan as $key => $val) {
          $amount = 0;
          if ($val->account_code_credit != '000') {
            $debet = $datas->where('code', $val->account_code_debet)->first()['amount'];
            $credit = $datas->where('code', $val->account_code_credit)->first()['amount'];
            $amount = $credit - $debet;
          }
          $new_data[] = [
            'id' => $val->id,
            'deep' => $val->deep,
            'code' => $val->code,
            'name' => $val->name,
            'jenis' => $val->jenis,
            'is_base' => $val->is_base,
            'parent' => $val->parent_id,
            'amount' => $amount
          ];
        }
      }
    }

    $datas = $new_data;
    
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name="semua cabang";
    }
    $end_data=end($datas);
    // dd($end_data['id']);
    $data = [
      'data' => $datas,
      'end_data'=>$end_data,
      'start' => $formatReqStart,
      'end' => $formatReqEnd,
      'company' => $name,
      'tipe_dokumen' => "2",
    ];
    // dd($data);
    // return SnappyPdf::loadView('export.laba_rugi2', $data)->stream();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi';
    return Excel::download(New LabaRugi($data), "$fileName$now.xlsx");
    // return PDF::loadView('export.laba_rugi2', $data)->stream();
  }

  function calculateSubtotals(&$datas, &$grandTotalPeriod, $parentId = 0) {
    foreach ($datas as $key => &$value) {
        if ($value['parent'] == $parentId) {
            // Calculate the subtotal for the current item
            foreach ($value['amount'] as $amountKeyParent => $amountValueParent) {
                $value['amount'][$amountKeyParent] += $amountValueParent;
            }

            // Recursively calculate subtotals for children
            $this->calculateSubtotals($datas, $grandTotalPeriod, $value['id']);
        }
    }
  }

  public function export_laba_rugi_excel_horizontal(Request $request)
  {
    $wr="1=1";
    $wr2="";

    $arrQueryMonth = [];

    $splitStart = explode("-", $request->start_month);
    $splitEnd = explode("-", $request->end_month);

    $yearStartSplitted = $splitStart[1];
    $yearEndSplitted = $splitEnd[1];

    $monthStartSplitted = $splitStart[0];
    $monthEndSplitted = $splitEnd[0];

    $formatReqStart = $yearStartSplitted."-".$monthStartSplitted;
    $formatReqEnd = $yearEndSplitted."-".$monthEndSplitted;

    // UNTUK MENGHITUNG PERIODE PERBULAN
    if ($yearStartSplitted == $yearEndSplitted) {
        if ($monthStartSplitted == $monthEndSplitted) {
            if($monthStartSplitted - 1 == 0){
              array_push($arrQueryMonth, sprintf("%04d-%02d", $yearStartSplitted-1, 12));
            } else {
              array_push($arrQueryMonth, sprintf("%04d-%02d", $yearStartSplitted, $monthStartSplitted - 1));
            }
            array_push($arrQueryMonth, sprintf("%04d-%02d", $yearStartSplitted, $monthStartSplitted));
        } else {
            for ($i = $monthStartSplitted; $i <= $monthEndSplitted; $i++) {
                array_push($arrQueryMonth, sprintf("%04d-%02d", $yearStartSplitted, $i));
            }
        }
    } else {
        for ($i = $yearStartSplitted; $i <= $yearEndSplitted; $i++) {
            if ($i == $yearStartSplitted) {
                for ($j = $monthStartSplitted; $j <= 12; $j++) {
                    array_push($arrQueryMonth, sprintf("%04d-%02d", $i, $j));
                }
            } elseif ($i == $yearEndSplitted) {
                for ($j = 1; $j <= $monthEndSplitted; $j++) {
                    array_push($arrQueryMonth, sprintf("%04d-%02d", $i, $j));
                }
            } else {
              for ($j = 1; $j <= 12; $j++) {
                array_push($arrQueryMonth, sprintf("%04d-%02d", $i, $j));
              }
            }
        }
    }

        // GET FIRST TRANSACTION FROM JANUARI - TO REQ END
        array_push($arrQueryMonth, [$yearEndSplitted.'-01', $formatReqEnd]);

    // GET FIRST TRANSACTION ON THE APP
    // $firstJournal = Journal::orderBy('date_transaction', 'ASC')->first();
    // array_push($arrQueryMonth, [Carbon::parse($firstJournal->date_transaction)->format('Y-m'), $formatReqEnd]);  

    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    if (isset($request->kurs_valas_id)) {
      $wr.=" AND journals.kurs_valas_id = $request->kurs_valas_id";
      $wr2.=" AND journals.kurs_valas_id = $request->kurs_valas_id";
    }
    $is_audit = $request->is_audit ?? 0;
      $wr.=" AND journals.is_audit = $is_audit";
      $wr2.=" AND journals.is_audit = $is_audit";
    $wr1 = "";
    if (isset($request->is_not_zero)) {
      $wr1 .= " WHERE
        ( det.jml_debet != 0
        OR det.jml_credit != 0 ) ";
    }
    $parent=Account::with('parent', 'parent.parent')->where('group_report', 2);
    $parent=$parent->orderBy('code')->get();
    $datas=[];
    // deep terkecil
    $maxDeep = 0;
    foreach ($parent as $value) {
      $dataDateTransaction = [];
      $prevent_zero = (isset($request->prevent_zero) && $request->prevent_zero == 'true');
      foreach ($arrQueryMonth as $key => $date) {
        if(is_array($date)){
          $wrDate = " DATE_FORMAT(journals.date_transaction, '%Y-%m') BETWEEN '".$date[0]."' AND '".$date[1]."'";
        } else {
          $wrDate =" DATE_FORMAT(journals.date_transaction, '%Y-%m') = '$date'";
        }
        $sql2 = "
          SELECT
            IFNULL(SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet) ),0) AS amount
          FROM
            journal_details
            LEFT JOIN accounts ON accounts.id = journal_details.account_id
            LEFT JOIN journals ON journals.id = journal_details.header_id
          WHERE
            journal_details.account_id = $value->id AND $wr AND $wrDate AND journals.status = 3 AND journals.type_transaction_id != 53
        ";
        $tarikDate=DB::select($sql2)[0];
        if(($prevent_zero && $tarikDate->amount != 0) || !$prevent_zero) {
          $dataDateTransaction[]= $tarikDate->amount;
        }       
      }
      if($maxDeep < $value->deep){
        $maxDeep = $value->deep;
      }
      $datas []= [
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'jenis' => $value->jenis,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $dataDateTransaction
      ];
    }

    $laba_rugi_tambahan = [
      (object) [
        'id' => 'LR',
        'deep' => 0,
        'code' => '-',
        'name' => 'LABA RUGI',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => []
      ],
      (object) [
        'id' => 'LR',
        'deep' => 1,
        'code' => '-',
        'name' => 'LABA RUGI KOTOR PENJUALAN',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => '-',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => []
      ],
      (object) [
        'id' => 'LR1',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA  PENGURUSAN TRANSP',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transportasi Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-01',
        'account_code_credit' => '4000-01',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-02',
        'account_code_credit' => '4000-02',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-03',
        'account_code_credit' => '4000-03',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR1',
        'account_code_debet' => '5000-04',
        'account_code_credit' => '4000-04',
        'amount' => []
      ],
      (object) [
        'id' => 'LR2',
        'deep' => 2,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor PENJUALAN JASA ATAS ONGKOS ANGKUT',
        'jenis' => 0,
        'is_base' => 1,
        'parent_id' => 'LR',
        'account_code_debet' => '000',
        'account_code_credit' => '000',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Umum',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-01',
        'account_code_credit' => '4001-01',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Pengurusan Transp Udara',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-02',
        'account_code_credit' => '4001-02',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Container',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-03',
        'account_code_credit' => '4001-03',
        'amount' => []
      ],
      (object) [
        'id' => 0,
        'deep' => 3,
        'code' => '-',
        'name' => 'Laba (Rugi) Kotor Jasa Atas Ongkos Angkut Curah',
        'jenis' => 0,
        'is_base' => 0,
        'parent_id' => 'LR2',
        'account_code_debet' => '5001-04',
        'account_code_credit' => '4001-04',
        'amount' => []
      ],
    ];

    $datas = collect($datas);
    $new_data = [];

    foreach ($datas as $key => $value) {
      $new_data[] = $value;
      if ($value['code'] == '5001-04') {
        foreach ($laba_rugi_tambahan as $key => $val) {
          $count_amount = count($value['amount']);
          $amount = [];
          for ($i = 0; $i < $count_amount; $i++) { 
            if ($val->account_code_credit != '000') {
              $debet = $datas->where('code', $val->account_code_debet)->first()['amount'][$i];
              $credit = $datas->where('code', $val->account_code_credit)->first()['amount'][$i];
              $amount[] = $credit - $debet;
            } else {
              $amount[] = 0;
            }
          }
          $new_data[] = [
            'id' => $val->id,
            'deep' => $val->deep,
            'code' => $val->code,
            'name' => $val->name,
            'jenis' => $val->jenis,
            'is_base' => $val->is_base,
            'parent' => $val->parent_id,
            'amount' => $amount
          ];
        }
      }
    }

    $datas = $new_data;

    // SET ARRAY GRAND TOTAL TO SAME LENGHT AS ARR QUERY
    $grandTotalPeriod = [];
    foreach ($arrQueryMonth as $keyMonth => $valMonth) {
      if(!isset($grandTotalPeriod[$keyMonth])){
        $grandTotalPeriod[$keyMonth] = 0;
      }
    }

    for ($i=$maxDeep-1; $i >= 0; $i--) { 
      // will sum all deep 2 to deep 1
      foreach ($datas as $key => $value) {
        if ($value['deep'] == $i) {
          $parentId = $value['id'];
          foreach ($datas as $innerKey => $innerValue) {
            if ($innerValue['parent'] == $parentId) {
              foreach ($innerValue['amount'] as $amountKeyParent => $amountValueParent) {
                $datas[$key]['amount'][$amountKeyParent] += $amountValueParent;
              }
            }
          }
        }
      }

      if($i == 0){
        foreach ($datas as $key => $value) {
          // SUM ALL DEEP 1 TO GRAND TOTAL
          if($value['deep'] == 1) {
            $id = $value['id'];
            foreach ($datas as $innerKey => $innerValue) {
              if ($innerValue['id'] == $id) {
                foreach ($innerValue['amount'] as $amountKeyParent => $amountValueParent) {
                  if ($value['jenis'] == 1) {
                    $grandTotalPeriod[$amountKeyParent] -= $amountValueParent;
                  } else {
                    $grandTotalPeriod[$amountKeyParent] += $amountValueParent;
                  }
                }
              }
            }
          }
        }
      }
    }

    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name="semua cabang";
    }
    $end_data=end($datas);
    // dd($arrQueryMonth);
    $data = [
      'data' => $datas,
      'end_data'=>$end_data,
      'start' => $formatReqStart,
      'end' => $formatReqEnd,
      'company' => $name,
      'tipe_dokumen' => "3",
      'date_params' => $arrQueryMonth,
      'grand_total' => $grandTotalPeriod
    ];
    // return SnappyPdf::loadView('export.laba_rugi2', $data)->stream();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi_horizontal';
    return Excel::download(New LabaRugi($data), "$fileName$now.xlsx");
    // return PDF::loadView('export.laba_rugi2', $data)->stream();
  }
  public function export_ekuitas(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $wr1 = "";
    if (isset($request->is_not_zero)) {
      $wr1 .= " WHERE
        ( det.jml_debet != 0
        OR det.jml_credit != 0 ) ";
    }
    $parent=Account::with('type')->whereHas('type', function($query){
      $query->whereIn('id',[12,13,14]);
    })->orderBy('code')->get();
    $datas=[];
    // dd($parent);
    $default=AccountDefault::first();
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start' and journals.status = 3 AND accounts.group_report = 2
    ".(isset($request->company_id)?' and journals.company_id = '.$request->company_id:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr and journals.status = 3 AND accounts.group_report = 2";
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM( journal_details.credit ) - SUM( journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3
      ";
      $tarik=DB::select($sql)[0];
      // $datas[]=[
      //   'id' => $value->id,
      //   'deep' => $value->deep,
      //   'code' => $value->code,
      //   'name' => $value->name,
      //   'is_base' => $value->is_base,
      //   'parent' => $value->parent_id,
      //   'amount' => $tarik->amount
      // ];
      if ($value->id==$default->laba_tahun_berjalan) {
        // dd($lr_tahun_berjalan);
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $lr_tahun_berjalan
        ];
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        // dd($lr_bulan_berjalan);
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $lr_bulan_berjalan
        ];
      } else {
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $tarik->amount
        ];
      }
    }
    // dd($datas);
    // dd($data);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $datas;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit['amount'] == 0 ) {
            unset( $datas[$key] );
          }
      }
    }
    $data = [
      'data' => $datas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => Company::find($request->company_id)
    ];
    // dd($data);
    // return SnappyPdf::loadView('export.ekuitas', $data)->stream();
    return PDF::loadView('export.ekuitas', $data)->stream();
  }
   /*
      Date : 16-12-2022
      Description : Export excel ekuitas
      Developer : alwi
      Status : Add
  */
  public function export_ekuitas_excel(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $wr1 = "";
    if (isset($request->is_not_zero)) {
      $wr1 .= " WHERE
        ( det.jml_debet != 0
        OR det.jml_credit != 0 ) ";
    }
    $parent=Account::with('type')->whereHas('type', function($query){
      $query->whereIn('id',[12,13,14]);
    })->orderBy('code')->get();
    $datas=[];
    // dd($parent);
    $default=AccountDefault::first();
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start' and journals.status = 3 AND accounts.group_report = 2
    ".(isset($request->company_id)?' and journals.company_id = '.$request->company_id:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr and journals.status = 3 AND accounts.group_report = 2";
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM( journal_details.credit ) - SUM( journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3
      ";
      $tarik=DB::select($sql)[0];
      // $datas[]=[
      //   'id' => $value->id,
      //   'deep' => $value->deep,
      //   'code' => $value->code,
      //   'name' => $value->name,
      //   'is_base' => $value->is_base,
      //   'parent' => $value->parent_id,
      //   'amount' => $tarik->amount
      // ];
      if ($value->id==$default->laba_tahun_berjalan) {
        // dd($lr_tahun_berjalan);
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $lr_tahun_berjalan
        ];
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        // dd($lr_bulan_berjalan);
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $lr_bulan_berjalan
        ];
      } else {
        $datas[]=[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'amount' => $tarik->amount
        ];
      }
    }
    // dd($datas);
    // dd($data);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $datas;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit['amount'] == 0 ) {
            unset( $datas[$key] );
          }
      }
    }
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name="semua cabang";
    }
    $data = [
      'data' => $datas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' =>$name,
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi';
    return Excel::download(New Ekuitas($data), "$fileName$now.xlsx");
  }
  public function export_neraca_saldo_banding(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="1=1";
    if (isset($request->start_date1) && isset($request->end_date1)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date1))."' AND '".date('Y-m-d', strtotime($request->end_date1))."'";
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->start_date2) && isset($request->end_date2)) {
      $wr2.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date2))."' AND '".date('Y-m-d', strtotime($request->end_date2))."'";
    } else {
      $dt_start2=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr2.=" AND journals.date_transaction >= '$dt_start2'";
    }
    if (isset($request->company_id1)) {
      $wr.=" AND journals.company_id = $request->company_id1";
    }
    if (isset($request->company_id2)) {
      $wr2.=" AND journals.company_id = $request->company_id2";
    }
    $sql = "
    SELECT
      accounts.deep,
      accounts.is_base,
      accounts.code,
      accounts.name,
      accounts.jenis,
      det.jml_debet AS tot_db,
      det.jml_credit AS tot_cr,
      det2.jml_debet AS tot_db2,
      det2.jml_credit AS tot_cr2
    FROM
      accounts
      LEFT JOIN (
    SELECT
      journal_details.account_id AS acc_id,
    IF
      ( ( accounts.jenis = 1 ), SUM(journal_details.debet) - SUM(journal_details.credit), null ) AS jml_debet,
    IF
      ( ( accounts.jenis = 2 ), SUM(journal_details.credit) - SUM(journal_details.debet), null ) AS jml_credit
    FROM
      journal_details
      INNER JOIN accounts ON journal_details.account_id = accounts.id
      INNER JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr AND journals.status = 3
    GROUP BY
      journal_details.account_id
      ) det ON accounts.id = det.acc_id
      LEFT JOIN (
    SELECT
      journal_details.account_id AS acc_id,
    IF
      ( ( accounts.jenis = 1 ), SUM(journal_details.debet) - SUM(journal_details.credit), null ) AS jml_debet,
    IF
      ( ( accounts.jenis = 2 ), SUM(journal_details.credit) - SUM(journal_details.debet), null ) AS jml_credit
    FROM
      journal_details
      INNER JOIN accounts ON journal_details.account_id = accounts.id
      INNER JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr2 AND journals.status = 3
    GROUP BY
      journal_details.account_id
      ) det2 ON accounts.id = det2.acc_id
    ORDER BY
      accounts.code ASC;
    ";
    // dd($sql);
    $datas = DB::select($sql);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $datas;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit->tot_db == 0 &&  $unit->tot_db2 == 0 &&  $unit->tot_cr == 0 &&  $unit->tot_cr2 == 0) {
            unset( $datas[$key] );
          }
      }
    }
    $data = [
      'data' => $datas,
      'start1' => $request->start_date1,
      'end1' => $request->end_date1,
      'start2' => $request->start_date2,
      'end2' => $request->end_date2,
      'company1' => Company::find($request->company_id1),
      'company2' => Company::find($request->company_id2),
    ];
    // dd($data);
    // return SnappyPdf::loadView('export.neraca_saldo_banding', $data)->stream();
    return PDF::loadView('export.neraca_saldo_banding', $data)->stream();
  }
  public function export_ekuitas_banding(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="1=1";
    if (isset($request->start_date1) && isset($request->end_date1)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date1))."' AND '".date('Y-m-d', strtotime($request->end_date1))."'";
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id1)) {
      $wr.=" AND journals.company_id = $request->company_id1";
    }
    if (isset($request->start_date2) && isset($request->end_date2)) {
      $wr2.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date2))."' AND '".date('Y-m-d', strtotime($request->end_date2))."'";
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr2.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id2)) {
      $wr2.=" AND journals.company_id = $request->company_id2";
    }
    $parent=Account::with('type')->whereHas('type', function($query){
      $query->whereIn('id',[12,13,14]);
    })->orderBy('code')->get();
    $datas=[];
    // dd($parent);
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM( journal_details.credit ) - SUM( journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3
      ";
      $tarik=DB::select($sql)[0];
      $sql2 = "
      SELECT
        IFNULL(SUM( journal_details.credit ) - SUM( journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr2 AND journals.status = 3
      ";
      $tarik2=DB::select($sql)[0];
      $datas[]=[
        'id' => $value->id,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'is_base' => $value->is_base,
        'parent' => $value->parent_id,
        'amount' => $tarik->amount,
        'amount2' => $tarik2->amount,
      ];
    }
    // dd($datas);
    // dd($data);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $datas;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit['amount'] == 0 && $unit['amount2'] == 0 ) {
            unset( $datas[$key] );
          }
      }
    }
    $data = [
      'data' => $datas,
      'start' => $request->start_date1,
      'end' => $request->end_date1,
      'start2' => $request->start_date2,
      'end2' => $request->end_date2,
      'company' => Company::find($request->company_id),
      'company2' => Company::find($request->company_id2)
    ];
    // dd($data);
    // return SnappyPdf::loadView('export.ekuitas_banding', $data)->stream();
    return PDF::loadView('export.ekuitas_banding', $data)->stream();
  }
  public function export_posisi_keuangan(Request $request)
  {
    $wr = "journals.status = 3";
    $wr2 = "";
    
    if ($request->start_date && $request->end_date) {
        $dt_start = Carbon::parse($request->start_date)->format('Y-m-d');
        $wr .= " AND journals.date_transaction BETWEEN '$dt_start' AND '" . Carbon::parse($request->end_date)->format('Y-m-d') . "'";
    } else {
        $dt_start = Carbon::parse('first day of this month')->format('Y-m-d');
        $wr .= " AND journals.date_transaction >= '$dt_start'";
    }
    
    if ($request->company_id) {
        $wr .= " AND journals.company_id = $request->company_id";
        $wr2 .= " AND journals.company_id = $request->company_id";
    }

    $default = AccountDefault::first();
    
    $lr_tahun_berjalan = DB::table('journal_details')
        ->join('accounts', 'accounts.id', '=', 'journal_details.account_id')
        ->join('journals', 'journals.id', '=', 'journal_details.header_id')
        ->where('accounts.group_report', 2)
        ->where('journals.date_transaction', '<', $dt_start)
        ->where('journals.status', 3)
        ->when($request->company_id, fn($q) => $q->where('journals.company_id', $request->company_id))
        ->selectRaw('IFNULL(SUM(journal_details.credit) - SUM(journal_details.debet), 0) AS amount')
        ->value('amount');
    
    $lr_bulan_berjalan = DB::table('journal_details')
        ->join('accounts', 'accounts.id', '=', 'journal_details.account_id')
        ->join('journals', 'journals.id', '=', 'journal_details.header_id')
        ->whereRaw($wr)
        ->where('accounts.group_report', 2)
        ->selectRaw('IFNULL(SUM(journal_details.credit) - SUM(journal_details.debet), 0) AS amount')
        ->value('amount');
    
    $datas = ['aktiva' => [], 'pasiva' => []];
    
    $accounts = Account::where('group_report', 1)->orderBy('code')->get();
    
    $saldo_awal = DB::table('journal_details')
        ->leftJoin('journals', 'journals.id', '=', 'journal_details.header_id')
        ->leftJoin('type_transactions', 'type_transactions.id', '=', 'journals.type_transaction_id')
        ->leftJoin('accounts', 'accounts.id', '=', 'journal_details.account_id')
        ->whereRaw("journals.status = 3 AND type_transactions.slug LIKE '%saldo%'")
        ->whereRaw($wr)
        ->select(
          'accounts.id', 
          DB::raw('IFNULL(SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit)), 0) AS saldo')
        )
        ->groupBy('accounts.id')
        ->get();

    $saldo_awal2 = DB::table('journal_details')
        ->leftJoin('journals', 'journals.id', '=', 'journal_details.header_id')
        ->leftJoin('type_transactions', 'type_transactions.id', '=', 'journals.type_transaction_id')
        ->leftJoin('accounts', 'accounts.id', '=', 'journal_details.account_id')
        ->whereRaw("journals.status = 3")
        ->whereRaw("journals.date_transaction < '$dt_start'")
        ->select(
          'accounts.id',
          DB::raw('IFNULL(SUM(IF(accounts.jenis = 1, journal_details.debet - journal_details.credit, journal_details.credit - journal_details.debet)), 0) AS saldo')
        )
        ->groupBy('accounts.id')
        ->get();
    
    $transactions = DB::table('journal_details')
        ->leftJoin('journals', 'journals.id', '=', 'journal_details.header_id')
        ->leftJoin('accounts', 'accounts.id', '=', 'journal_details.account_id')
        ->whereRaw($wr)
        ->where('journals.status', 3)
        ->select(
            'accounts.id',
            DB::raw('IFNULL(SUM(journal_details.debet), 0) AS debet'),
            DB::raw('IFNULL(SUM(journal_details.credit), 0) AS credit')
        )
        ->groupBy('accounts.id')
        ->get()
        ->keyBy('id');
    
    foreach ($accounts as $account) {
        $saldo = $saldo_awal->where('id', $account->id)->first()->saldo ?? 0;
        $saldo2 = $saldo_awal2->where('id', $account->id)->first()->saldo ?? 0;
        $main = $transactions[$account->id] ?? (object) ['debet' => 0, 'credit' => 0];
        
        // $amount = ($account->jenis == 1)
        //     ? ($saldo ? $main->debet - $main->credit : $saldo2 + $main->debet - $main->credit)
        //     : ($saldo ? $main->credit - $main->debet : $saldo2 + $main->credit - $main->debet);
        $amount = ($account->jenis == 1)
            ? $saldo2 + $main->debet - $main->credit
            : $saldo2 + $main->credit - $main->debet;

        $category = $account->jenis == 1 ? 'aktiva' : 'pasiva';
        $amount = ($account->id == $default->laba_tahun_berjalan) ? $lr_tahun_berjalan : $amount;
        $amount = ($account->id == $default->laba_bulan_berjalan) ? $lr_bulan_berjalan : $amount;
        
        $datas[$category][] = [
            'is_base' => $account->is_base,
            'deep' => $account->deep,
            'code' => $account->code,
            'name' => $account->name,
            'amount' => $amount,
            'saldo_awal' => $saldo,
            'status' => $category,
        ];
    }
    
    if ($request->prevent_zero == 'true') {
        foreach ($datas as $key => $items) {
            $datas[$key] = array_values(array_filter($items, fn($item) => $item['amount'] != 0));
        }
    }
    
    $data = [
        'data' => $datas,
        'start' => $request->start_date,
        'end' => $request->end_date,
        'company' => Company::find($request->company_id),
        'sumA' => array_sum(array_column($datas['aktiva'], 'amount')),
        'sumB' => array_sum(array_column($datas['pasiva'], 'amount')),
        'prevent_zero' => $request->prevent_zero,
    ];
    
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'posisi_keuangan_';
    return FacadesExcel::download(new PosisiKeuangan($data), "$fileName$now.xlsx");
  }
  public function export_posisi_keuangan_backup(Request $request)
  {
    // dd($request->all());
    // die();
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=Carbon::parse($request->start_date)->format('Y-m-d');
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $default=AccountDefault::first();
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start' and journals.status = 3 AND accounts.group_report = 2
    ".(isset($request->company_id)?' and journals.company_id = '.$request->company_id:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr and journals.status = 3 AND accounts.group_report = 2";
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    $datas=[];
    $acc_aktiva=Account::where('group_report', 1)->where('jenis', 1)->orderBy('code')->get();
    $sumA=0;
    foreach ($acc_aktiva as $key => $value) {
      $sqlsaldo1="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit)),0) as saldo from journal_details
            left join journals on journals.id = journal_details.header_id
            left join type_transactions on type_transactions.id = journals.type_transaction_id
            left join accounts on accounts.id = journal_details.account_id
            where journal_details.account_id = $value->id and journals.status = 3 and type_transactions.slug like '%saldo%' and $wr";
      $saldo=DB::select($sqlsaldo1)[0];
      $saldo1 = true;
      if ($saldo->saldo==0) {
        $sqlsaldo2="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
              left join journals on journals.id = journal_details.header_id
              left join type_transactions on type_transactions.id = journals.type_transaction_id
              left join accounts on accounts.id = journal_details.account_id
              where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction < '$dt_start'";
        $saldo=DB::select($sqlsaldo2)[0];
        $saldo1 = false;
      }
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE $wr AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      $amount = ($value->jenis == 1
      ? ($saldo1 ? $main->debet - $main->credit : $saldo->saldo + $main->debet - $main->credit)
      : ($saldo1 ? $main->credit - $main->debet : $saldo->saldo + $main->credit - $main->debet));
      $datas['aktiva'][]=[
        'is_base' => $value->is_base,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'amount' => $amount,
        'saldo_awal' => $saldo->saldo,
        'status'=> "aktiva",
      ];
      $sumA += $amount;
    }
    $acc_pasiva=Account::where('group_report', 1)->where('jenis', 2)->orderBy('code')->get();
    $sumB=0;
    foreach ($acc_pasiva as $key => $value) {
      $sqlsaldo1="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit)),0) as saldo from journal_details
            left join journals on journals.id = journal_details.header_id
            left join type_transactions on type_transactions.id = journals.type_transaction_id
            left join accounts on accounts.id = journal_details.account_id
            where journal_details.account_id = $value->id and journals.status = 3 and type_transactions.slug like '%saldo%' and $wr";
      $saldo=DB::select($sqlsaldo1)[0];
      $saldo1 = true;
      if ($saldo->saldo==0) {
        $sqlsaldo2="select ifnull(sum(if(accounts.jenis=1,journal_details.debet-journal_details.credit,journal_details.credit-journal_details.debet)),0) as saldo from journal_details
              left join journals on journals.id = journal_details.header_id
              left join type_transactions on type_transactions.id = journals.type_transaction_id
              left join accounts on accounts.id = journal_details.account_id
              where journal_details.account_id = $value->id and journals.status = 3 $wr2 and journals.date_transaction < '$dt_start'";
        $saldo=DB::select($sqlsaldo2)[0];
        $saldo1=false;
      }
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE $wr AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      // dd($value->id);
      if ($value->id==$default->laba_tahun_berjalan) {
        // dd($lr_tahun_berjalan);
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_tahun_berjalan,
          'saldo_awal' => $saldo->saldo,
          'saldo1' => $saldo1,
          'status'=> "pasiva",
        ];
        $sumB+= $lr_tahun_berjalan;
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        // dd($lr_bulan_berjalan);
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_bulan_berjalan,
          'saldo_awal' => $saldo->saldo,
          'status'=> "pasiva",
        ];
        $sumB+= $lr_bulan_berjalan;
      } else {
        $amount = ($value->jenis == 1
        ? ($saldo1 ? $main->debet - $main->credit : $saldo->saldo + $main->debet - $main->credit)
        : ($saldo1 ? $main->credit - $main->debet : $saldo->saldo + $main->credit - $main->debet));
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $amount,
          'saldo_awal' => $saldo->saldo,
          'status'=> "pasiva",
        ];
        $sumB+= $amount;
      }
    }
    // Apakah menampikan data yang bernilai 0 atau tidak
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      $units = $datas;
      foreach ($units as $door => $accounts) {
        foreach ($accounts as $key => $unit) {
          if( $unit['amount'] == 0 ) {
            // dd($door,$key);
            // die();
            unset( $datas[$door][$key] );
          }
        }
      }
    }
    // =======================================================================
    $datas['aktiva'] = array_values($datas['aktiva']);
    $datas['pasiva'] = array_values($datas['pasiva']);
    // $data['aktiva'] = [
    //   'data' => $datas['aktiva'],
    //   'start' => $request->start_date,
    //   'end' => $request->end_date,
    //   'company' => Company::find($request->company_id),
    //   'sumA' => $sumA,
    //   'prevent_zero' => $prevent_zero,
    // ];
    // $data['pasiva'] = [
    //   'data' => $datas['pasiva'],
    //   'start' => $request->start_date,
    //   'end' => $request->end_date,
    //   'company' => Company::find($request->company_id),
    //   'sumB' => $sumB,
    //   'prevent_zero' => $prevent_zero,
    // ];
    $data = [
      'data' => $datas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => Company::find($request->company_id),
      'sumA' => $sumA,
      'sumB' => $sumB,
      'prevent_zero' => $prevent_zero,
    ];
    // dd($data);
    // return view('export.posisi_keuangan',$data);
    // return PDF::loadView('export.posisi_keuangan', $data)->stream();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'posisi_keuangan_';
    // return PDF::loadView('report.finance.posisi_keuangan', $data)->stream();
    // return view('report.finance.posisi_keuangan',compact('data'));
    return FacadesExcel::download(new PosisiKeuangan($data), "$fileName$now.xlsx");
  }
  public function export_posisi_keuangan_perbandingan(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="1=1";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=Carbon::parse($request->start_date)->format('Y-m-d');
      $dtstring1=$request->start_date." s/d ".$request->end_date;
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
      $dtstring1=$dt_start." s/d ".date('Y-m-d');
    }
    if (isset($request->start_date2) && isset($request->end_date2)) {
      $wr2.=" and journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date2))."' AND '".date('Y-m-d', strtotime($request->end_date2))."'";
      $dt_start2=Carbon::parse($request->start_date2)->format('Y-m-d');
      $dtstring2=$request->start_date2." s/d ".$request->end_date2;
    } else {
      $dt_start2=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr2.=" AND journals.date_transaction >= '$dt_start2'";
      $dtstring2=$dt_start2." s/d ".date('Y-m-d');
    }
    $wrc1="";
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $cy=DB::table('companies')->where('id', $request->company_id)->select('name')->first();
      $companyname1=$cy->name;
      $wrc1=" and journals.company_id = $request->company_id";
    } else {
      $companyname1="Semua Cabang";
    }
    $wrc2="";
    if (isset($request->company_id2)) {
      $wr2.=" AND journals.company_id = $request->company_id2";
      $cy=DB::table('companies')->where('id', $request->company_id2)->select('name')->first();
      $companyname2=$cy->name;
      $wrc2=" and journals.company_id = $request->company_id";
    } else {
      $companyname2="Semua Cabang";
    }
    $default=AccountDefault::first();
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start' and journals.status = 3 AND accounts.group_report = 2
    ".(isset($request->company_id)?' and journals.company_id = '.$request->company_id:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr and journals.status = 3 AND accounts.group_report = 2";
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    $data1=[];
    $sum1A=0;
    $sum1B=0;
    $acc_aktiva=Account::where('group_report', 1)->whereBetween('type_id', [1,8])->orderBy('code')->get();
    foreach ($acc_aktiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE IF($value->group_report=2,$wr,journals.date_transaction < '$dt_start' $wrc1) AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      $data1['aktiva'][]=[
        'is_base' => $value->is_base,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
      ];
      $sum1A += ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet);
    }
    $acc_pasiva=Account::where('group_report', 1)->whereBetween('type_id', [9,17])->orderBy('code')->get();
    foreach ($acc_pasiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE IF($value->group_report=2,$wr,journals.date_transaction < '$dt_start' $wrc1) AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      // dd($value->id);
      if ($value->id==$default->laba_tahun_berjalan) {
        // dd($lr_tahun_berjalan);
        $data1['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_tahun_berjalan
        ];
        $sum1B += $lr_tahun_berjalan;
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        // dd($lr_bulan_berjalan);
        $data1['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_bulan_berjalan
        ];
        $sum1B += $lr_bulan_berjalan;
      } else {
        $data1['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
        ];
        $sum1B += ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet);
      }
    }
    //banding--------------
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start2' and journals.status = 3 AND accounts.group_report = 2
    ".(isset($request->company_id2)?' and journals.company_id = '.$request->company_id2:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr2 and journals.status = 3 AND accounts.group_report = 2";
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    $data2=[];
    $sum2A=0;
    $sum2B=0;
    $acc_aktiva=Account::where('group_report', 1)->whereBetween('type_id', [1,8])->orderBy('code')->get();
    foreach ($acc_aktiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE IF($value->group_report=2,$wr2,journals.date_transaction < '$dt_start2' $wrc2) AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      $data2['aktiva'][]=[
        'is_base' => $value->is_base,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
      ];
      $sum2A += ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet);
    }
    $acc_pasiva=Account::where('group_report', 1)->whereBetween('type_id', [9,17])->orderBy('code')->get();
    foreach ($acc_pasiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE IF($value->group_report=2,$wr2,journals.date_transaction < '$dt_start2' $wrc2) AND accounts.id = $value->id and journals.status = 3
      ";
      $main=DB::select($sql)[0];
      // dd($value->id);
      if ($value->id==$default->laba_tahun_berjalan) {
        // dd($lr_tahun_berjalan);
        $data2['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_tahun_berjalan
        ];
        $sum2B += $lr_tahun_berjalan;
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        // dd($lr_bulan_berjalan);
        $data2['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_bulan_berjalan
        ];
        $sum2B += $lr_bulan_berjalan;
      } else {
        $data2['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
        ];
        $sum2B += ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet);
      }
    }
    //end ------------------
    // dd($datas);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      $units = $data1['aktiva'];
      $units2 = $data2['aktiva'];
      foreach ($units as $key => $unit) {
          $unit2 = $units[$key];
          if( $unit['amount'] == 0 && $unit2['amount'] == 0 ) {
            unset( $data1['aktiva'][$key] );
            unset( $data2['aktiva'][$key] );
          }
      }
      $units = $data1['pasiva'];
      $units2 = $data2['pasiva'];
      foreach ($units as $key => $unit) {
          $unit2 = $units[$key];
          if( $unit['amount'] == 0 && $unit2['amount'] == 0) {
            unset( $data1['pasiva'][$key] );
            unset( $data2['pasiva'][$key] );
          }
      }
    }
    $data1 = array_values($data1);
    $data2 = array_values($data2);
    $data = [
      'data1' => $data1,
      'data2' => $data2,
      'datestring1' => $dtstring1,
      'datestring2' => $dtstring2,
      'company1' => $companyname1,
      'company2' => $companyname2,
      'sum1A' => $sum1A,
      'sum1B' => $sum1B,
      'sum2A' => $sum2A,
      'sum2B' => $sum2B,
    ];
    // dd($data);
    // return view('export.posisi_keuangan',$data);
    // return PDF::loadView('export.posisi_keuangan', $data)->stream();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'posisi_keuangan_perbandingan_';
    return Excel::download(new PosisiKeuanganPerbandingan($data), "$fileName$now.xlsx");
  }
  public function export_posisi_keuangan_bkp(Request $request)
  {
    // dd($request);
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=Carbon::parse($request->start_date)->format('Y-m-d');
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $default=AccountDefault::first();
    $SQL_laba_tahun_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE
      accounts.group_report = 2
      AND journals.date_transaction < '$dt_start'
    ".(isset($request->company_id)?' and journals.company_id = '.$request->company_id:'');
    $SQL_laba_bulan_berjalan="
    SELECT
      IFNULL( SUM( journal_details.credit ) - SUM( journal_details.debet ), 0 ) AS amount
    FROM
      journal_details
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      LEFT JOIN journals ON journals.id = journal_details.header_id
    WHERE $wr ";
    // dd($sql);
    $lr_tahun_berjalan = DB::select($SQL_laba_tahun_berjalan)[0]->amount;
    $lr_bulan_berjalan = DB::select($SQL_laba_bulan_berjalan)[0]->amount;
    // dd($lr_bulan_berjalan);
    $datas=[];
    $acc_aktiva=Account::where('group_report', 1)->whereBetween('type_id', [1,8])->orderBy('code')->get();
    foreach ($acc_aktiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE $wr AND accounts.id = $value->id
      ";
      $main=DB::select($sql)[0];
      $datas['aktiva'][]=[
        'is_base' => $value->is_base,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
      ];
    }
    $acc_pasiva=Account::where('group_report', 1)->whereBetween('type_id', [9,17])->orderBy('code')->get();
    foreach ($acc_pasiva as $key => $value) {
      $sql="
      SELECT
        IFNULL(SUM(journal_details.debet),0) as debet,
        IFNULL(SUM(journal_details.credit),0) as credit
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE $wr AND accounts.id = $value->id
      ";
      $main=DB::select($sql)[0];
      if ($value->id==$default->laba_tahun_berjalan) {
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_tahun_berjalan
        ];
      } elseif ($value->id==$default->laba_bulan_berjalan) {
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => $lr_bulan_berjalan
        ];
      } else {
        $datas['pasiva'][]=[
          'is_base' => $value->is_base,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'amount' => ($value->jenis==1?$main->debet-$main->credit:$main->credit-$main->debet)
        ];
      }
    }
    // dd($datas);
    $data = [
      'data' => $datas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => Company::find($request->company_id)
    ];
    // dd($data);
    // return view('export.posisi_keuangan',$data);
    // return SnappyPdf::loadView('export.posisi_keuangan', $data)->stream();
    return PDF::loadView('export.posisi_keuangan', $data)->stream();
  }
  public function outstanding_debt()
  {
    $data['company'] = companyAdmin(auth()->id());
    $data['customer'] = Contact::where('is_pelanggan', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function export_outstanding_debt(Request $request)
  {
    $wr="1=1";
    if (!empty($request->company_id)) {
      $data['company'] = Company::find($request->company_id);
      $wr.=" and receivables.company_id = $request->company_id";
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $data['company'] = Company::all();
      } else {
        $data['company'] = Company::where('id', $user->company_id)->get();
        $wr.=" and receivables.company_id = $user->company_id";
      }
    }
    if ($request->customer_id) {
      $wr.=" and receivables.contact_id = $request->customer_id";
    }
    if ($request->start_date && $request->end_date) {
      $start=Carbon::parse($request->start_date)->format('Y-m-d');
      $end=Carbon::parse($request->end_date)->format('Y-m-d');
      $wr.=" and receivables.date_tempo between '$start' and '$end'";
    } else {
      $now= date("Y-m-d",strtotime(now()));
      $wr.=" and receivables.date_tempo <= '$now'";
    }
    $source=DB::table('receivables')
    ->leftJoin('contacts','contacts.id','=','receivables.contact_id')
    ->leftJoin('companies','companies.id','=','receivables.company_id')
    ->leftJoin('type_transactions','type_transactions.id','=','receivables.type_transaction_id')
    ->leftJoin('users','receivables.created_by','=','users.id')
    ->whereRaw($wr)
    ->whereRaw('(receivables.debet-receivables.credit > 0)')
    ->where('receivables.debet','!=','receivables.credit')
    ->orderBy('receivables.date_transaction')
    ->select([
      'companies.name as company',
      'contacts.name as contact',
      'type_transactions.name as type_transaction',
      'receivables.code',
      'receivables.date_transaction',
      'receivables.date_tempo',
      'receivables.debet',
      'receivables.credit',
      'receivables.updated_at',
      'receivables.description',
      'users.name as username',
      DB::raw('(receivables.debet-receivables.credit) as sisa'),
      DB::raw("CASE
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 0 THEN 'Sementara'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 1 THEN 'Lunas'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 2 THEN 'Outstanding'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 3 THEN 'Proses'
          ELSE NULL
      END AS status_piutang_name"),
      DB::raw('DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) as due_days'),
      // DB::raw('DATEDIFF(receivables.date_tempo,receivables.date_transaction) as due_days'),
      DB::raw('(receivables.debet-receivables.credit)/receivables.debet*100 as percent'),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) < 60, 'V', '') as day_30"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) BETWEEN 60 AND 89, 'V', '') as day_60"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) = 90, 'V', '') as day_90"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) > 90, 'V', '') as day_more_90"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) <= 30,'V','') as day_30"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) between 31 and 60,'V','') as day_60"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) between 61 and 90,'V','') as day_90"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) > 90,'V','') as day_more_90"),
    ])->get();
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $source;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit->debet == 0 && $unit->credit == 0 ) {
            unset( $source[$key] );
          }
      }
    }
    // dd($source);
    $data['source']=$source;
    $data['type_transaction'] = TypeTransaction::where('slug', 'giro')->first();
    $data['request'] = (object)$request->input();
    return PDF::loadView('export.outstanding_piutang_new', $data)->setPaper('A4', 'landscape')->stream();
  }
   /*
      Date : 17-12-2022
      Description : Export excel oustanding excel
      Developer : alwi
      Status : Add
  */
  public function export_outstanding_debt_excel(Request $request)
  {
    $wr="1=1";
    if (!empty($request->company_id)) {
      $data['company'] = Company::find($request->company_id);
      $wr.=" and receivables.company_id = $request->company_id";
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $data['company'] = Company::all();
      } else {
        $data['company'] = Company::where('id', $user->company_id)->get();
        $wr.=" and receivables.company_id = $user->company_id";
      }
    }
    if ($request->customer_id) {
      $wr.=" and receivables.contact_id = $request->customer_id";
    }
    if ($request->start_date && $request->end_date) {
      $start=Carbon::parse($request->start_date)->format('Y-m-d');
      $end=Carbon::parse($request->end_date)->format('Y-m-d');
      $wr.=" and receivables.date_tempo between '$start' and '$end'";
    } else {
      $now= date("Y-m-d",strtotime(now()));
      $wr.=" and receivables.date_tempo <= '$now'";
    }
    $source=DB::table('receivables')
    ->leftJoin('contacts','contacts.id','=','receivables.contact_id')
    ->leftJoin('companies','companies.id','=','receivables.company_id')
    ->leftJoin('type_transactions','type_transactions.id','=','receivables.type_transaction_id')
    ->leftJoin('users','receivables.created_by','=','users.id')
    ->whereRaw($wr)
    ->whereRaw('(receivables.debet-receivables.credit > 0)')
    ->where('receivables.debet','!=','receivables.credit')
    ->orderBy('receivables.date_transaction')
    ->select([
      'companies.name as company',
      'contacts.name as contact',
      'type_transactions.name as type_transaction',
      'receivables.code',
      'receivables.date_transaction',
      'receivables.date_tempo',
      'receivables.debet',
      'receivables.credit',
      'receivables.updated_at',
      'receivables.description',
      'users.name as username',
      DB::raw('(receivables.debet-receivables.credit) as sisa'),
      DB::raw("CASE
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 0 THEN 'Sementara'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 1 THEN 'Lunas'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 2 THEN 'Outstanding'
          WHEN IF(receivables.is_temporary = 1, 0, IF(receivables.debet - receivables.credit = 0, 1, IF(DATEDIFF(NOW(), receivables.date_tempo) > 0, 2, 3))) = 3 THEN 'Proses'
          ELSE NULL
      END AS status_piutang_name"),
      DB::raw('DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) as due_days'),
      // DB::raw('DATEDIFF(receivables.date_tempo,receivables.date_transaction) as due_days'),
      DB::raw('(receivables.debet-receivables.credit)/receivables.debet*100 as percent'),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) < 60, 'V', '') as day_30"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) BETWEEN 60 AND 89, 'V', '') as day_60"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) = 90, 'V', '') as day_90"),
      DB::raw("IF(DATEDIFF(CURDATE(), DATE_ADD(receivables.date_transaction, INTERVAL 4 DAY)) > 90, 'V', '') as day_more_90"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) <= 30,'V','') as day_30"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) between 31 and 60,'V','') as day_60"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) between 61 and 90,'V','') as day_90"),
      // DB::raw("IF(DATEDIFF(CURDATE(), receivables.date_tempo) > 90,'V','') as day_more_90"),
    ])->get();
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $source;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit->debet == 0 && $unit->credit == 0 ) {
            unset( $source[$key] );
          }
      }
    }
    // dd($source);
    $data['source']=$source;
    $data['type_transaction'] = TypeTransaction::where('slug', 'giro')->first();
    $data['request'] = (object)$request->input();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'outstanding_piutang';
    return Excel::download(New OutstandingDebit($data), "$fileName$now.xlsx");
  }
  public function outstanding_credit()
  {
    $data['company'] = companyAdmin(auth()->id());
    $data['supplier'] = Contact::where('is_supplier', 1)->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function export_outstanding_credit(Request $request)
  {
    $wr="1=1";
    if (!empty($request->company_id)) {
      $data['company'] = Company::find($request->company_id);
      $wr.=" and payables.company_id = $request->company_id";
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $data['company'] = Company::all();
      } else {
        $data['company'] = Company::where('id', $user->company_id)->get();
        $wr.=" and payables.company_id = $user->company_id";
      }
    }
    if ($request->customer_id) {
      $wr.=" and payables.contact_id = $request->customer_id";
    }
    if ($request->start_date && $request->end_date) {
      $start=Carbon::parse($request->start_date)->format('Y-m-d');
      $end=Carbon::parse($request->end_date)->format('Y-m-d');
      $wr.=" and payables.date_tempo between '$start' and '$end'";
    } else {
      $now= date("Y-m-d",strtotime(now()));
      $wr.=" and payables.date_tempo <= '$now'";
    }
    $source=DB::table('payables')
    ->leftJoin('contacts','contacts.id','=','payables.contact_id')
    ->leftJoin('companies','companies.id','=','payables.company_id')
    ->leftJoin('type_transactions','type_transactions.id','=','payables.type_transaction_id')
    ->leftJoin('users','payables.created_by','=','users.id')
    ->leftJoin("journals", 'journals.id', 'payables.journal_id')
    ->whereRaw($wr)
    ->whereRaw('(payables.credit-payables.debet > 0)')
    ->select([
      'companies.name as company',
      'contacts.name as contact',
      'type_transactions.name as type_transaction',
      'payables.code',
      'payables.date_transaction',
      'payables.date_tempo',
      'payables.debet',
      'payables.credit',
      'payables.updated_at',
      'payables.description',
      'users.name as username',
      DB::raw('(payables.credit-payables.debet) as sisa'),
      DB::raw('DATEDIFF(payables.date_tempo,payables.date_transaction) as due_days'),
      DB::raw('(payables.debet-payables.credit)/payables.debet*100 as percent'),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) <= 30,'V','') as day_30"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) between 31 and 60,'V','') as day_60"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) between 61 and 90,'V','') as day_90"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) > 90,'V','') as day_more_90"),
      DB::raw("CASE
          WHEN (payables.credit - payables.debet) = 0 THEN 'Lunas'
          WHEN payables.is_cost = 1 THEN 'Outstanding'
          WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 'Outstanding'
          ELSE 'Proses'
      END AS status_hutang_name")
    ])->get();
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $source;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit->debet == 0 && $unit->credit == 0 ) {
            unset( $source[$key] );
          }
      }
    }
    $data['source']=$source;
    $data['type_transaction'] = TypeTransaction::where('slug', 'giro')->first();
    $data['request'] = (object)$request->input();
    return PDF::loadView('export.outstanding_hutang_new', $data)->setPaper('A4', 'landscape')->stream();
  }
   /*
      Date : 17-12-2022
      Description : Export excel outstanding credit
      Developer : alwi
      Status : Add
  */
  public function export_outstanding_credit_excel(Request $request)
  {
    $wr="1=1";
    if (!empty($request->company_id)) {
      $data['company'] = Company::find($request->company_id);
      $wr.=" and payables.company_id = $request->company_id";
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $data['company'] = Company::all();
      } else {
        $data['company'] = Company::where('id', $user->company_id)->get();
        $wr.=" and payables.company_id = $user->company_id";
      }
    }
    if ($request->customer_id) {
      $wr.=" and payables.contact_id = $request->customer_id";
    }
    if ($request->start_date && $request->end_date) {
      $start=Carbon::parse($request->start_date)->format('Y-m-d');
      $end=Carbon::parse($request->end_date)->format('Y-m-d');
      $wr.=" and payables.date_tempo between '$start' and '$end'";
    } else {
      $now= date("Y-m-d",strtotime(now()));
      $wr.=" and payables.date_tempo <= '$now'";
    }
    $source=DB::table('payables')
    ->leftJoin('contacts','contacts.id','=','payables.contact_id')
    ->leftJoin('companies','companies.id','=','payables.company_id')
    ->leftJoin('type_transactions','type_transactions.id','=','payables.type_transaction_id')
    ->leftJoin('users','payables.created_by','=','users.id')
    ->leftJoin("journals", 'journals.id', 'payables.journal_id')
    ->whereRaw($wr)
    ->whereRaw('(payables.credit-payables.debet > 0)')
    ->select([
      'companies.name as company',
      'contacts.name as contact',
      'type_transactions.name as type_transaction',
      'payables.code',
      'payables.date_transaction',
      'payables.date_tempo',
      'payables.debet',
      'payables.credit',
      'payables.updated_at',
      'payables.description',
      'users.name as username',
      DB::raw('(payables.credit-payables.debet) as sisa'),
      DB::raw('DATEDIFF(payables.date_tempo,payables.date_transaction) as due_days'),
      DB::raw('(payables.debet-payables.credit)/payables.debet*100 as percent'),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) <= 30,'V','') as day_30"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) between 31 and 60,'V','') as day_60"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) between 61 and 90,'V','') as day_90"),
      DB::raw("IF(DATEDIFF(CURDATE(), payables.date_tempo) > 90,'V','') as day_more_90"),
      DB::raw("CASE
          WHEN (payables.credit - payables.debet) = 0 THEN 'Lunas'
          WHEN payables.is_cost = 1 THEN 'Outstanding'
          WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 'Outstanding'
          ELSE 'Proses'
      END AS status_hutang_name")
    ])->get();
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $source;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit->debet == 0 && $unit->credit == 0 ) {
            unset( $source[$key] );
          }
      }
    }
    $data['source']=$source;
    $data['type_transaction'] = TypeTransaction::where('slug', 'giro')->first();
    $data['request'] = (object)$request->input();
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'outstanding_hutang';
    return Excel::download(New OutstandingCredit($data), "$fileName$now.xlsx");
  }
  public function laba_rugi_perbandingan()
  {
    $data['company']=companyAdmin(auth()->id());
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function export_laba_rugi_perbandingan(Request $request)
  {
    $company_id = '';
    $start = '';
    $end = '';
    if($request->company_id){
      $company_id = $request->company_id;
    }
    if(isset($request->start_date) && isset($request->end_date)) {
      $start = $request->start_date;
      $end = $request->end_date;
    }
    $datas = self::data_laba_rugi($company_id,$start,$end);
    $company_id = '';
    $start = '';
    $end = '';
    if($request->company_id_perbandingan){
      $company_id = $request->company_id_perbandingan;
    }
    if(isset($request->start_date_perbandingan) && isset($request->start_date_perbandingan)) {
      $start = $request->start_date_perbandingan;
      $end = $request->start_date_perbandingan;
    }
    $perbandingan = self::data_laba_rugi($company_id,$start,$end);
    if (!empty($request->company_id)) {
      $company = Company::find($request->company_id);
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $company = Company::all();
      } else {
        $company = Company::where('id', $user->company_id)->get();
      }
    }
    if (!empty($request->company_id_perbandingan)) {
      $company_perbandingan = Company::find($request->company_id_perbandingan);
    } else {
      $user = DB::table('users')->where('id', auth()->id())->first();
      if ($user->is_admin) {
        $company_perbandingan = Company::all();
      } else {
        $company_perbandingan = Company::where('id', $user->company_id)->get();
      }
    }
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    $units = $datas;
    if($prevent_zero == 'true') {
      foreach ($units as $key => $unit) {
          if( $unit['is_base'] == 0 && $unit['amount'] == 0 ) {
            unset( $datas[$key] );
          }
      }
    }
    $data = [
      'data' => $datas,
      'perbandingan'=>$perbandingan,
      'request'=>(object)$request->input(),
      'company'=>$company,
      'company_perbandingan'=>$company_perbandingan,
    ];
    // return view('export.laba_rugi_banding', $data);
    return PDF::loadView('export.laba_rugi_banding', $data)->setPaper('A4')->stream();
  }
  public static function data_laba_rugi($company_id,$start,$end){
    $wr = "1=1";
    $wr2 = "";
    if ((isset($start) && $start!= '') && isset($end) && $end!='') {
      $wr .= " AND journals.date_transaction BETWEEN '" . date('Y-m-d', strtotime($start)) . "' AND '" . date('Y-m-d', strtotime($end)) . "'";
      $dt_start = date('Y-m-d', strtotime($start));
    } else {
      $dt_start = Carbon::parse('first day of this month')->format('Y-m-d');
      $wr .= " AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($company_id) && $company_id!='') {
      $wr .= " AND journals.company_id = $company_id";
      $wr2 .= " AND journals.company_id = $company_id";
    }
    $wr1 = "";
    // if (isset($request->is_not_zero)) {
    //   $wr1 .= " WHERE
    //     ( det.jml_debet != 0
    //     OR det.jml_credit != 0 ) ";
    // }
    $parent = Account::where('group_report', 2)->orderBy('code')->get();
    $datas = [];
    foreach ($parent as $value) {
      $sql = "
      SELECT
        IFNULL(SUM( journal_details.credit ) - SUM( journal_details.debet ),0) AS amount
      FROM
        journal_details
        LEFT JOIN accounts ON accounts.id = journal_details.account_id
        LEFT JOIN journals ON journals.id = journal_details.header_id
      WHERE
        journal_details.account_id = $value->id AND $wr AND journals.status = 3 AND journals.type_transaction_id != 53
      ";
      $tarik = DB::select($sql)[0];
      $datas[] = [
        'id' => $value->id,
        'deep' => $value->deep,
        'code' => $value->code,
        'name' => $value->name,
        'jenis' => $value->jenis,
        'is_base' => $value->is_base,
        'parent' => $value->parent_id,
        'amount' => $tarik->amount
      ];
    }
    return $datas;
  }
  public function export_arus_kas(Request $request)
  {
    // dd($request);
    $aktivitas=[
      1=> 'Aktivitas Operasional',
      2=> 'Aktivitas Investasi',
      3=> 'Aktivitas Pendanaan',
    ];
    $wr="1=1";
    $wr1="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    $wr1.=" AND journals.date_transaction < '$dt_start'";
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr1.=" AND journals.company_id = $request->company_id";
    }
    $sql = "
    SELECT
      id,
      code,
      name,
      kategori,
      jenis,
      is_base,
      ifnull(IF(jenis=1,(Y.db-Y.cr),(Y.cr-Y.db)),0) as total
    FROM
      cash_categories
    LEFT JOIN (select cash_category_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 and journal_details.cash_category_id is not null and $wr group by journal_details.cash_category_id) Y on Y.cash_category_id = cash_categories.id
    ORDER BY
    CODE ASC
    ";
    // dd($sql);
    $datas = DB::select($sql);
    // dd($datas);
    $sql_kas="
    SELECT
      code,
      name,
      ifnull(if(jenis=1,(db-cr),(cr-db)),0) as total
    FROM
      accounts
    LEFT JOIN (select journal_details.account_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 $wr1 group by journal_details.account_id) Y on Y.account_id = accounts.id
    WHERE
      no_cash_bank IN ( 1, 2 ) and is_base = 0
    ";
    $data_kas=DB::select($sql_kas);
    // Apakah menampikan data yang bernilai 0 atau tidak
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      $units = $datas;
      foreach ($units as $key => $unit) {
        if( $unit->total == 0 ) {
          unset( $datas[$key] );
        }
      }
      $units = $data_kas;
      foreach ($units as $key => $unit) {
        if( $unit->total == 0 ) {
          unset( $data_kas[$key] );
        }
      }
    }
    // =======================================================================
    $data = [
      'data' => $datas,
      'data_kas' => $data_kas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'aktivitas' => $aktivitas,
      'company' => Company::find($request->company_id)
    ];
    // return SnappyPdf::loadView('export.arus_kas', $data)->stream();
    return PDF::loadView('export.arus_kas', $data)->stream();
  }
   /*
      Date : 15-12-2022
      Description : Export excel arus kas
      Developer : alwi
      Status : Add
  */
  public function export_arus_kas_excel(Request $request)
  {
    // dd($request);
    $aktivitas=[
      1=> 'Aktivitas Operasional',
      2=> 'Aktivitas Investasi',
      3=> 'Aktivitas Pendanaan',
    ];
    $wr="1=1";
    $wr1="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    $wr1.=" AND journals.date_transaction < '$dt_start'";
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr1.=" AND journals.company_id = $request->company_id";
    }
    $sql = "
    SELECT
      id,
      code,
      name,
      kategori,
      jenis,
      is_base,
      ifnull(IF(jenis=1,(Y.db-Y.cr),(Y.cr-Y.db)),0) as total
    FROM
      cash_categories
    LEFT JOIN (select cash_category_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 and journal_details.cash_category_id is not null and $wr group by journal_details.cash_category_id) Y on Y.cash_category_id = cash_categories.id
    ORDER BY
    CODE ASC
    ";
    // dd($sql);
    $datas = DB::select($sql);
    // dd($datas);
    $sql_kas="
    SELECT
      code,
      name,
      ifnull(if(jenis=1,(db-cr),(cr-db)),0) as total
    FROM
      accounts
    LEFT JOIN (select journal_details.account_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 $wr1 group by journal_details.account_id) Y on Y.account_id = accounts.id
    WHERE
      no_cash_bank IN ( 1, 2 ) and is_base = 0
    ";
    $data_kas=DB::select($sql_kas);
    // Apakah menampikan data yang bernilai 0 atau tidak
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      $units = $datas;
      foreach ($units as $key => $unit) {
        if( $unit->total == 0 ) {
          unset( $datas[$key] );
        }
      }
      $units = $data_kas;
      foreach ($units as $key => $unit) {
        if( $unit->total == 0 ) {
          unset( $data_kas[$key] );
        }
      }
    }
    // =======================================================================
    $company=Company::find($request->company_id);
    if($company){
      $name=$company->name;
    }else{
      $name="semua cabang";
    }
    $data = [
      'data' => $datas,
      'data_kas' => $data_kas,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'aktivitas' => $aktivitas,
      'company' => $name
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'arus_kas';
    return Excel::download(New ArusKas($data), "$fileName$now.xlsx");
    // return SnappyPdf::loadView('export.arus_kas', $data)->stream();
    // return PDF::loadView('export.arus_kas', $data)->stream();
  }
  private function calculateArusKasBaruData(Request $request)
  {
    $wr = "1=1";
    $wr_prev = "1=1";

    if (isset($request->start_date) && isset($request->end_date)) {
      $start_date = date('Y-m-d', strtotime($request->start_date));
      $end_date = date('Y-m-d', strtotime($request->end_date));
      $wr .= " AND journals.date_transaction BETWEEN '$start_date' AND '$end_date'";

      $prev_end = date('Y-m-d', strtotime($start_date . ' -1 day'));
      $wr_prev .= " AND journals.date_transaction <= '$prev_end'";
    } else {
      $start_date = Carbon::parse('first day of this month')->format('Y-m-d');
      $end_date = Carbon::parse('last day of this month')->format('Y-m-d');
      $wr .= " AND journals.date_transaction BETWEEN '$start_date' AND '$end_date'";

      $prev_end = date('Y-m-d', strtotime($start_date . ' -1 day'));
      $wr_prev .= " AND journals.date_transaction <= '$prev_end'";
    }

    if (isset($request->company_id)) {
      $wr .= " AND journals.company_id = $request->company_id";
      $wr_prev .= " AND journals.company_id = $request->company_id";
    }

    $sql_current = "
      SELECT
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (107,112) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as laba_income,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (119,120,121,122,123,124) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as laba_expense,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (132,138,139,140,141,142,143,158,159,160,161,162,163,164,165) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as laba_other_expense,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 185 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as laba_other_income,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 190 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as laba_tax,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (12,15) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as piutang,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (21,26,29) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as aset_lancar,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 32 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as pajak_dimuka,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 174 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as penyusutan,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 56 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_usaha,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (60,68,71) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_lainnya,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 75 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_pajak,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 41 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as aset_tetap,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (87,89,91) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as liabilitas_panjang,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (95,98,102) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as ekuitas,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (3,6,10) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as kas_bank
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE journals.status = 3 AND $wr
    ";

    $current_result = DB::select($sql_current)[0];
    $current = [
      'laba_bersih' => $current_result->laba_income - $current_result->laba_expense - $current_result->laba_other_expense + $current_result->laba_other_income - $current_result->laba_tax,
      'piutang' => $current_result->piutang,
      'aset_lancar' => $current_result->aset_lancar,
      'pajak_dimuka' => $current_result->pajak_dimuka,
      'penyusutan' => $current_result->penyusutan,
      'hutang_usaha' => $current_result->hutang_usaha,
      'hutang_lainnya' => $current_result->hutang_lainnya,
      'hutang_pajak' => $current_result->hutang_pajak,
      'aset_tetap' => $current_result->aset_tetap,
      'liabilitas_panjang' => $current_result->liabilitas_panjang,
      'ekuitas' => $current_result->ekuitas,
      'kas_bank' => $current_result->kas_bank
    ];

    $sql_previous = "
      SELECT
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (12,15) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as piutang,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (21,26,29) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as aset_lancar,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 32 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as pajak_dimuka,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 56 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_usaha,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (60,68,71) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_lainnya,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 75 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as hutang_pajak,
        COALESCE(SUM(CASE WHEN journal_details.account_id = 41 THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as aset_tetap,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (87,89,91) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as liabilitas_panjang,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (95,98,102) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as ekuitas,
        COALESCE(SUM(CASE WHEN journal_details.account_id IN (3,6,10) THEN
          CASE WHEN accounts.jenis = 1 THEN journal_details.debet - journal_details.credit
          ELSE journal_details.credit - journal_details.debet END END), 0) as kas_bank
      FROM journal_details
      LEFT JOIN journals ON journals.id = journal_details.header_id
      LEFT JOIN accounts ON accounts.id = journal_details.account_id
      WHERE journals.status = 3 AND $wr_prev
    ";

    $previous_result = DB::select($sql_previous)[0];
    $previous = [
      'piutang' => $previous_result->piutang,
      'aset_lancar' => $previous_result->aset_lancar,
      'pajak_dimuka' => $previous_result->pajak_dimuka,
      'hutang_usaha' => $previous_result->hutang_usaha,
      'hutang_lainnya' => $previous_result->hutang_lainnya,
      'hutang_pajak' => $previous_result->hutang_pajak,
      'aset_tetap' => $previous_result->aset_tetap,
      'liabilitas_panjang' => $previous_result->liabilitas_panjang,
      'ekuitas' => $previous_result->ekuitas,
      'kas_bank' => $previous_result->kas_bank
    ];

    $reportData = [];

    // A. Aktivitas Operasional
    $reportData[] = (object)[
      'name' => 'A. Aktivitas Operasional',
      'amount' => 0,
      'is_bold' => true,
      'is_header' => true
    ];

    $reportData[] = (object)[
      'name' => '1. Laba Bersih',
      'amount' => $current['laba_bersih'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '2. Akun Piutang',
      'amount' => $current['piutang'] - $previous['piutang'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '3. Aset Lancar Lainnya',
      'amount' => $current['aset_lancar'] - $previous['aset_lancar'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '4. Pajak Dibayar Dimuka',
      'amount' => $current['pajak_dimuka'] - $previous['pajak_dimuka'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '5. Penyusutan & Amortisasi',
      'amount' => $current['penyusutan'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '6. Akun Hutang Usaha',
      'amount' => $current['hutang_usaha'] - $previous['hutang_usaha'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '7. Hutang & Hutang Lainnya',
      'amount' => $current['hutang_lainnya'] - $previous['hutang_lainnya'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '8. Hutang Pajak',
      'amount' => $current['hutang_pajak'] - $previous['hutang_pajak'],
      'is_bold' => false,
      'is_header' => false
    ];

    // Calculate total aktivitas operasional
    $total_operasional = $current['laba_bersih'] +
                        ($current['piutang'] - $previous['piutang']) +
                        ($current['aset_lancar'] - $previous['aset_lancar']) +
                        ($current['pajak_dimuka'] - $previous['pajak_dimuka']) +
                        $current['penyusutan'] +
                        ($current['hutang_usaha'] - $previous['hutang_usaha']) +
                        ($current['hutang_lainnya'] - $previous['hutang_lainnya']) +
                        ($current['hutang_pajak'] - $previous['hutang_pajak']);

    $reportData[] = (object)[
      'name' => 'Kas bersih yang diperoleh dari Aktivitas Operasional',
      'amount' => $total_operasional,
      'is_bold' => true,
      'is_header' => false
    ];

    // B. Aktivitas Investasi
    $reportData[] = (object)[
      'name' => 'B. Aktivitas Investasi',
      'amount' => 0,
      'is_bold' => true,
      'is_header' => true
    ];

    $reportData[] = (object)[
      'name' => '9. Aset Tetap',
      'amount' => $current['aset_tetap'] - $previous['aset_tetap'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '10. Aktivitas Investasi Lainnya',
      'amount' => 0,
      'is_bold' => false,
      'is_header' => false
    ];

    // Calculate total aktivitas investasi
    $total_investasi = ($current['aset_tetap'] - $previous['aset_tetap']) + 0;

    $reportData[] = (object)[
      'name' => 'Kas bersih yang diperoleh dari Aktivitas Investasi',
      'amount' => $total_investasi,
      'is_bold' => true,
      'is_header' => false
    ];

    // C. Aktivitas Pendanaan
    $reportData[] = (object)[
      'name' => 'C. Aktivitas Pendanaan',
      'amount' => 0,
      'is_bold' => true,
      'is_header' => true
    ];

    $reportData[] = (object)[
      'name' => '11. Liabilitas Jangka Panjang',
      'amount' => $current['liabilitas_panjang'] - $previous['liabilitas_panjang'],
      'is_bold' => false,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => '12. Ekuitas',
      'amount' => $current['ekuitas'] - $previous['ekuitas'],
      'is_bold' => false,
      'is_header' => false
    ];

    // Calculate total aktivitas pendanaan
    $total_pendanaan = ($current['liabilitas_panjang'] - $previous['liabilitas_panjang']) +
                       ($current['ekuitas'] - $previous['ekuitas']);

    $reportData[] = (object)[
      'name' => 'Kas bersih yang diperoleh dari Aktivitas Pendanaan',
      'amount' => $total_pendanaan,
      'is_bold' => true,
      'is_header' => false
    ];

    $kenaikan_kas = $total_operasional + $total_investasi + $total_pendanaan;
    $total_revaluasi = 0;
    $saldo_kas_awal = $previous['kas_bank'];
    $saldo_kas_akhir = $current['kas_bank'];

    $reportData[] = (object)[
      'name' => 'Kenaikan (Penurunan) Kas',
      'amount' => $kenaikan_kas,
      'is_bold' => true,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => 'Total Revaluasi Bank',
      'amount' => $total_revaluasi,
      'is_bold' => true,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => 'Saldo Kas Awal',
      'amount' => $saldo_kas_awal,
      'is_bold' => true,
      'is_header' => false
    ];

    $reportData[] = (object)[
      'name' => 'Saldo Kas Akhir',
      'amount' => $saldo_kas_akhir,
      'is_bold' => true,
      'is_header' => false
    ];

    $data = [
      'data' => $reportData,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'company' => Company::find($request->company_id),
      'current' => $current,
      'previous' => $previous,
      'total_operasional' => $total_operasional,
      'total_investasi' => $total_investasi,
      'total_pendanaan' => $total_pendanaan,
      'kenaikan_kas' => $kenaikan_kas,
      'total_revaluasi' => $total_revaluasi,
      'saldo_kas_awal' => $saldo_kas_awal,
      'saldo_kas_akhir' => $saldo_kas_akhir
    ];

    return $data;
  }

  private function calculateArusKasPerbandinganBaruData(Request $request)
  {
    // Get data for period 1
    $request1 = clone $request;
    $data1 = $this->calculateArusKasBaruData($request1);

    // Get data for period 2
    $request2 = clone $request;
    $request2->start_date = $request->start_date2;
    $request2->end_date = $request->end_date2;
    $data2 = $this->calculateArusKasBaruData($request2);

    // Combine data for comparison
    $combinedData = [];

    foreach ($data1['data'] as $index => $item1) {
      $item2 = isset($data2['data'][$index]) ? $data2['data'][$index] : (object)[
        'name' => $item1->name,
        'amount' => 0,
        'is_bold' => $item1->is_bold,
        'is_header' => $item1->is_header
      ];

      // Apply prevent_zero filter
      if ($request->prevent_zero == 'true') {
        if (!$item1->is_header && !$item1->is_bold && $item1->amount == 0 && $item2->amount == 0) {
          continue;
        }
      }

      $combinedData[] = (object)[
        'name' => $item1->name,
        'amount1' => $item1->amount,
        'amount2' => $item2->amount,
        'is_bold' => $item1->is_bold,
        'is_header' => $item1->is_header
      ];
    }

    $company = Company::find($request->company_id);
    $companyName = $company ? $company->name : 'Semua Cabang';

    return [
      'data' => $combinedData,
      'start1' => $request->start_date,
      'end1' => $request->end_date,
      'start2' => $request->start_date2,
      'end2' => $request->end_date2,
      'company' => $companyName,
      'company_obj' => $company
    ];
  }

  public function export_arus_kas_perbandingan_baru(Request $request)
  {
    try {
      $calculatedData = $this->calculateArusKasPerbandinganBaruData($request);

      if (empty($calculatedData['data'])) {
        return response()->json(['error' => 'Data laporan kosong. Pastikan ada transaksi pada periode yang dipilih.'], 400);
      }

      $data = [
        'data' => $calculatedData['data'],
        'company' => $calculatedData['company'],
        'start1' => $calculatedData['start1'],
        'end1' => $calculatedData['end1'],
        'start2' => $calculatedData['start2'],
        'end2' => $calculatedData['end2']
      ];

      $pdf = PDF::loadView('export.arus_kas_perbandingan_baru', $data);
      return $pdf->stream('Laporan_Arus_Kas_Perbandingan_Baru_' . date('Y-m-d_H-i-s') . '.pdf');
    } catch (Exception $e) {
      \Log::error('PDF Export Error: ' . $e->getMessage());
      return response()->json(['error' => 'Gagal generate laporan PDF: ' . $e->getMessage()], 500);
    }
  }

  public function export_arus_kas_perbandingan_baru_excel(Request $request)
  {
    try {
      $data = $this->calculateArusKasPerbandinganBaruData($request);

      if (empty($data['data'])) {
        return response()->json(['error' => 'Data laporan kosong. Pastikan ada transaksi pada periode yang dipilih.'], 400);
      }

      $now = date("d_m_Y_H_i_s");
      $fileName = " ";
      $fileName .= 'laporan_arus_kas_perbandingan_baru';
      return Excel::download(New ArusKasPerbandinganBaru($data), "$fileName$now.xlsx");
    } catch (Exception $e) {
      return response()->json(['error' => 'Gagal generate laporan Excel: ' . $e->getMessage()], 500);
    }
  }

  public function export_arus_kas_baru(Request $request)
  {
    try {
      $calculatedData = $this->calculateArusKasBaruData($request);

      if (empty($calculatedData['data'])) {
        return response()->json(['error' => 'Data laporan kosong. Pastikan ada transaksi pada periode yang dipilih.'], 400);
      }

      $data = [
        'data' => $calculatedData['data'],
        'company' => $calculatedData['company'],
        'start' => $calculatedData['start'],
        'end' => $calculatedData['end']
      ];

      $pdf = PDF::loadView('export.arus_kas_baru', $data);
      return $pdf->stream('Laporan_Arus_Kas_Baru_' . date('Y-m-d_H-i-s') . '.pdf');
    } catch (Exception $e) {
      \Log::error('PDF Export Error: ' . $e->getMessage());
      return response()->json(['error' => 'Gagal generate laporan PDF: ' . $e->getMessage()], 500);
    }
  }
  public function export_arus_kas_baru_excel(Request $request)
  {
    try {
      $data = $this->calculateArusKasBaruData($request);

      if (empty($data['data'])) {
        return response()->json(['error' => 'Data laporan kosong. Pastikan ada transaksi pada periode yang dipilih.'], 400);
      }

      $now = date("d_m_Y_H_i_s");
      $fileName = " ";
      $fileName .= 'laporan_arus_kas_baru';
      return Excel::download(New ArusKasBaru($data), "$fileName$now.xlsx");
    } catch (Exception $e) {
      return response()->json(['error' => 'Gagal generate laporan Excel: ' . $e->getMessage()], 500);
    }
  }
  public function export_arus_kas_perbandingan(Request $request)
  {
    // dd($request);
    $aktivitas=[
      1=> 'Aktivitas Operasional',
      2=> 'Aktivitas Investasi',
      3=> 'Aktivitas Pendanaan',
    ];
    $wr="1=1";
    $wr2="1=1";
    $wr1="";
    $wr3="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=date('Y-m-d', strtotime($request->start_date));
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->start_date2) && isset($request->end_date2)) {
      $wr2.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date2))."' AND '".date('Y-m-d', strtotime($request->end_date2))."'";
      $dt_start2=date('Y-m-d', strtotime($request->start_date2));
    } else {
      $dt_start2=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr2.=" AND journals.date_transaction >= '$dt_start2'";
    }
    $wr1.=" AND journals.date_transaction < '$dt_start'";
    $wr3.=" AND journals.date_transaction < '$dt_start2'";
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr1.=" AND journals.company_id = $request->company_id";
    }
    if (isset($request->company_id2)) {
      $wr2.=" AND journals.company_id = $request->company_id2";
      $wr3.=" AND journals.company_id = $request->company_id2";
    }
    $sql = "
    SELECT
      id,
      code,
      name,
      kategori,
      jenis,
      is_base,
      ifnull(IF(jenis=1,(Y.db-Y.cr),(Y.cr-Y.db)),0) as total
    FROM
      cash_categories
    LEFT JOIN (select cash_category_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 and journal_details.cash_category_id is not null and $wr group by journal_details.cash_category_id) Y on Y.cash_category_id = cash_categories.id
    ORDER BY
    CODE ASC
    ";
    $sql2 = "
    SELECT
      id,
      code,
      name,
      kategori,
      jenis,
      is_base,
      ifnull(IF(jenis=1,(Y.db-Y.cr),(Y.cr-Y.db)),0) as total
    FROM
      cash_categories
    LEFT JOIN (select cash_category_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 and journal_details.cash_category_id is not null and $wr2 group by journal_details.cash_category_id) Y on Y.cash_category_id = cash_categories.id
    ORDER BY
    CODE ASC
    ";
    // dd($sql);
    $datas = DB::select($sql);
    $datas2 = DB::select($sql2);
    // dd($datas);
    $sql_kas="
    SELECT
      code,
      name,
      ifnull(if(jenis=1,(db-cr),(cr-db)),0) as total
    FROM
      accounts
    LEFT JOIN (select journal_details.account_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 $wr1 group by journal_details.account_id) Y on Y.account_id = accounts.id
    WHERE
      no_cash_bank IN ( 1, 2 ) and is_base = 0
    ";
    $sql_kas2="
    SELECT
      code,
      name,
      ifnull(if(jenis=1,(db-cr),(cr-db)),0) as total
    FROM
      accounts
    LEFT JOIN (select journal_details.account_id, sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id where journals.status = 3 $wr3 group by journal_details.account_id) Y on Y.account_id = accounts.id
    WHERE
      no_cash_bank IN ( 1, 2 ) and is_base = 0
    ";
    $data_kas=DB::select($sql_kas);
    $data_kas2=DB::select($sql_kas2);
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      // Data
      $units = $datas;
      $units2 = $datas2;
      foreach ($units as $key => $unit) {
          $unit2 = $units2[$key];
          if( $unit->is_base == 0 && $unit->total == 0 && $unit2->total == 0 ) {
            unset( $datas[$key] );
            unset( $datas2[$key] );
          }
      }
      // Data
      $units = $data_kas;
      $units2 = $data_kas2;
      foreach ($units as $key => $unit) {
          $unit2 = $units2[$key];
          if( $unit->total == 0 && $unit2->total == 0 ) {
            unset( $data_kas[$key] );
            unset( $data_kas2[$key] );
          }
      }
    }
    $data = [
      'data' => $datas,
      'data2' => $datas2,
      'data_kas' => $data_kas,
      'data_kas2' => $data_kas2,
      'start' => $request->start_date,
      'end' => $request->end_date,
      'start2' => $request->start_date2,
      'end2' => $request->end_date2,
      'aktivitas' => $aktivitas,
      'company' => Company::find($request->company_id),
      'company2' => Company::find($request->company_id2)
    ];
    // return SnappyPdf::loadView('export.arus_kas_banding', $data)->stream();
    return PDF::loadView('export.arus_kas_banding', $data)->stream();
  }
  public function export_neraca_lajur(Request $request)
  {
    $wr="1=1";
    $wr2="";
    if (isset($request->start_date) && isset($request->end_date)) {
      $wr.=" AND journals.date_transaction BETWEEN '".date('Y-m-d', strtotime($request->start_date))."' AND '".date('Y-m-d', strtotime($request->end_date))."'";
      $dt_start=Carbon::parse($request->start_date)->format('Y-m-d');
    } else {
      $dt_start=Carbon::parse('first day of this month')->format('Y-m-d');
      $wr.=" AND journals.date_transaction >= '$dt_start'";
    }
    if (isset($request->company_id)) {
      $wr.=" AND journals.company_id = $request->company_id";
      $wr2.=" AND journals.company_id = $request->company_id";
    }
    $sql="
    select
    deep,
    is_base,
    concat(code,' - ',name) as account_name,
    @mutasiD := ifnull(Y.mutasiD,0) varMutD,
    @mutasiK := ifnull(Y.mutasiK,0) varMutK,
    @penyesuaianD := ifnull(Y.penyesuaianD,0) varPenyesuaianD,
    @penyesuaianK := ifnull(Y.penyesuaianK,0) varPenyesuaianK,
    @saldoMentah1 := if(jenis=1,(ifnull(saldo.db,0)-ifnull(saldo.cr,0)),(ifnull(saldo.cr,0)-ifnull(saldo.db,0))) varSaldoMentah1,
    @saldoMentah2 := if(jenis=1,(ifnull(saldo2.db,0)-ifnull(saldo2.cr,0)),(ifnull(saldo2.cr,0)-ifnull(saldo2.db,0))) varSaldoMentah2,
    @mutasiAsli := if(jenis=1,@mutasiD-@mutasiK,@mutasiK-@mutasiD) varMutasiAsli,
    @penyesuaianAsli := if(jenis=1,@penyesuaianD-@penyesuaianK,@penyesuaianK-@penyesuaianD) varPenyesuaianAsli,
    -- @saldoAsli := if(@saldoMentah1 != 0,@saldoMentah1,@saldoMentah2) as varSaldoAsli,
    @saldoAsli := @saldoMentah1+@saldoMentah2 as varSaldoAsli,
    -- @ambilSaldoD := if(group_report=1,if(jenis=1 and @saldoAsli>=0,@saldoAsli,if(jenis=2 and @saldoAsli<0,abs(@saldoAsli),0)),0) as ambilSaldoD,
    -- @ambilSaldoK := if(group_report=1,if(jenis=2 and @saldoAsli>=0,@saldoAsli,if(jenis=1 and @saldoAsli<0,abs(@saldoAsli),0)),0) as ambilSaldoK,
    @ambilSaldoD := if(jenis=1, @saldoAsli,0) as ambilSaldoD,
    @ambilSaldoK := if(jenis=2, @saldoAsli,0) as ambilSaldoK,
    @ambilMutasiD := if(jenis=1 and @mutasiAsli>=0,@mutasiAsli,if(jenis=2 and @mutasiAsli<0,abs(@mutasiAsli),0)) as ambilMutasiD,
    @ambilMutasiK := if(jenis=2 and @mutasiAsli>=0,@mutasiAsli,if(jenis=1 and @mutasiAsli<0,abs(@mutasiAsli),0)) as ambilMutasiK,
    -- @ambilNsD := if(@ambilSaldoD+@mutasiD-@mutasiK<0,0,@ambilSaldoD+@mutasiD-@mutasiK) as ambilNsD,
    -- @ambilNsK := if(@ambilSaldoK+@mutasiD-@mutasiK<0,abs(@ambilSaldoD+@mutasiD-@mutasiK),0) as ambilNsK,
    @ambilNsD := @ambilSaldoD+@mutasiD-@mutasiK as ambilNsD,
    @ambilNsK := @ambilSaldoD+@mutasiD-@mutasiK as ambilNsK,
    @ambilPenyesuaianD := if(jenis=1 and @penyesuaianAsli>=0,@penyesuaianAsli,if(jenis=2 and @penyesuaianAsli<0,abs(@penyesuaianAsli),0)) as ambilPenyesuaianD,
    @ambilPenyesuaianK := if(jenis=2 and @penyesuaianAsli>=0,@penyesuaianAsli,if(jenis=1 and @penyesuaianAsli<0,abs(@penyesuaianAsli),0)) as ambilPenyesuaianK,
    @ambilNsPD := @ambilNsD+@ambilPenyesuaianD as ambilNsPD,
    @ambilNsPK := @ambilNsK+@ambilPenyesuaianK as ambilNsPK,
    @ambilLRD := if(group_report=2,@ambilNsPD,0) as ambilLRD,
    @ambilLRK := if(group_report=2,@ambilNsPK,0) as ambilLRK,
    @ambilPSD := if(group_report=1,@ambilNsPD,0) as ambilPSD,
    @ambilPSK := if(group_report=1,@ambilNsPK,0) as ambilPSK
    from accounts
    left join (
      select journal_details.account_id,sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id left join type_transactions on type_transactions.id = journals.type_transaction_id where type_transactions.is_saldo = 1 and journals.status = 3 and $wr group by journal_details.account_id
    ) saldo on saldo.account_id = accounts.id
    left join (
      select journal_details.account_id,sum(journal_details.debet) as db, sum(journal_details.credit) as cr from journal_details left join journals on journals.id = journal_details.header_id left join type_transactions on type_transactions.id = journals.type_transaction_id where journals.status = 3 and journals.date_transaction < '$dt_start' $wr2 group by journal_details.account_id
    ) saldo2 on saldo2.account_id = accounts.id
    left join (
      select
      journal_details.account_id,
      sum(if(type_transactions.is_journal=1,journal_details.debet,0)) as mutasiD,
      sum(if(type_transactions.is_penyesuaian=1,journal_details.debet,0)) as penyesuaianD,
      sum(if(type_transactions.is_journal=1,journal_details.credit,0)) as mutasiK,
      sum(if(type_transactions.is_penyesuaian=1,journal_details.credit,0)) as penyesuaianK
      from journal_details
      left join journals on journals.id = journal_details.header_id
      left join type_transactions on type_transactions.id = journals.type_transaction_id
      where journals.status = 3 and $wr
      group by journal_details.account_id
    ) Y on Y.account_id = accounts.id
    order by code asc
    ";
    $data=DB::select($sql);
    // Menghitung subtotal
    foreach($data AS $unit) {
      $subtotal = $unit->ambilSaldoD + $unit->ambilSaldoK + $unit->varMutD + $unit->varMutK + $unit->ambilNsD + $unit->ambilNsK + $unit->ambilPenyesuaianD + $unit->ambilPenyesuaianK + $unit->ambilNsPD + $unit->ambilNsPK + $unit->ambilLRD + $unit->ambilLRK + $unit->ambilPSD + $unit->ambilPSK;
      $unit->subtotal = $subtotal;
    }
    // Apakah menampikan data yang bernilai 0 atau tidak
    $prevent_zero = $request->prevent_zero;
    $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';
    if($prevent_zero == 'true') {
      $units = $data;
      foreach ($units as $key => $unit) {
        if($unit->subtotal == 0) {
          unset( $data[$key] );
        }
      }
    }
    // =======================================================================
    // dd($data);
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'neraca_lajur_';
    return Excel::download(new NeracaLajur($data), "$fileName$now.xlsx");
  }

  public function nota_potong_penjualan_export(Request $request) {
    if ($request->company_id) {
      $companies = Company::where('id', $request->company_id)->get();
    } else {
      $companies = Company::get();
    }
    $remark = FacadesDB::table('print_remarks')
    ->where('company_id', auth()->user()->company_id)
    ->first();
    $datas = [];
            
    // Ambil Type Transaksi
    $debet=TypeTransaction::where('slug','piutangdebetNote')->first();
    $debet = $debet->id;
    $credit=TypeTransaction::where('slug','piutangcreditNote')->first();
    $credit = $credit->id;

    foreach ($companies as $key => $value) {
      $data= FacadesDB::table('nota_credits')
      ->leftJoin('companies as company','company.id','=','nota_credits.company_id')
      ->leftJoin('receivables as receivable','receivable.id','=','nota_credits.receivable_id')
      ->leftJoin('contacts as contact','contact.id','=','nota_credits.contact_id')
      ->leftJoin('journal_details as jd', function ($join) {
        $join->on('nota_credits.journal_id', '=', 'jd.header_id');
        $join->where(function ($query) {
            $query->where(function ($subquery) {
                $subquery->where('nota_credits.jenis', 1)
                         ->where('jd.debet', '>', 0);
            })
            ->orWhere(function ($subquery) {
                $subquery->where('nota_credits.jenis', 2)
                         ->where('jd.credit', '>', 0);
            });
        });
      })
      ->leftJoin('receivables as receive_nota', function ($join) use ($credit, $debet) {
        $join->on('receive_nota.relation_id', '=', 'nota_credits.id');
        // GET RECEIVABLE NOTA DEBET WHERE TYPE TRX KREDIT ATAU DEBET
        $join->where(function ($query) use ($credit, $debet) {
          $query->where('receive_nota.type_transaction_id', $credit);
          $query->orWhere('receive_nota.type_transaction_id', $debet);
        });
      })
      ->leftJoin('bill_details as bill_detail','bill_detail.receivable_id','=','receive_nota.id')
      ->leftJoin('bills as bill','bill.id','=','bill_detail.header_id')
      ->leftJoin('accounts as account','account.id','=','jd.account_id')
      ->where('nota_credits.company_id', $value->id)
      ->select(
        'nota_credits.*',
        'receivable.code as no_receivable',
        'jd.account_id as jd_account_id',
        FacadesDB::raw('CONCAT(account.code, " - ", account.name) as account_name'),
        FacadesDB::raw('
        CASE
            WHEN nota_credits.jenis = 1 THEN "DEBET"
            ELSE "KREDIT"
        END as jenis_name'),
        'bill.code as no_reff',
        'company.name as company_name',
        'contact.name as contact_name'
      )
      ->groupBy('nota_credits.id')
      ->get();
      if(count($data) <= 0) {
        $datas[$key][] = null;
        continue;
      }
      foreach ($data as $key2 => $value2) {
        $value2->date_transaction = dateView($value2->date_transaction);
        $value2->valas = 'IDR';
        $value2->amount = formatNumber($value2->amount);
      }
      $datas[$key][] = $data;
    }
    $item = [
      'trx' => 'piutang',
      'companies' => $companies,
      'remark' => $remark,
      'datas' => $datas
    ];
    $pdf = PDF::loadView('finance.nota_koreksi', $item)
    ->setPaper('a4', 'landscape');
    // ->setPaper(array(0,0,609.4488,935.433), 'landscape'); // KERTAS F4
    return $pdf->stream('Laporan Koreksi Piutang.pdf');
  }

  public function nota_potong_pembelian_export(Request $request) {
    if ($request->company_id) {
      $companies = Company::where('id', $request->company_id)->get();
    } else {
      $companies = Company::get();
    }
    $remark = FacadesDB::table('print_remarks')
    ->where('company_id', auth()->user()->company_id)
    ->first();
    $datas = [];
        
    // Ambil Type Transaksi
    $debet=TypeTransaction::where('slug','hutangdebetNote')->first();
    $debet = $debet->id;
    $credit=TypeTransaction::where('slug','hutangcreditNote')->first();
    $credit = $credit->id;

    foreach ($companies as $key => $value) {
      $data= FacadesDB::table('nota_debets')
      ->leftJoin('companies as company','company.id','=','nota_debets.company_id')
      ->leftJoin('payables as payable','payable.id','=','nota_debets.payable_id')
      ->leftJoin('contacts as contact','contact.id','=','nota_debets.contact_id')
      ->leftJoin('journal_details as jd', function ($join) {
        $join->on('nota_debets.journal_id', '=', 'jd.header_id');
        $join->where(function ($query) {
            $query->where(function ($subquery) {
                $subquery->where('nota_debets.jenis', 1)
                         ->where('jd.debet', '>', 0);
            })
            ->orWhere(function ($subquery) {
                $subquery->where('nota_debets.jenis', 2)
                         ->where('jd.credit', '>', 0);
            });
        });
      })
      ->leftJoin('payables as payable_nota', function ($join) use ($credit, $debet) {
        $join->on('payable_nota.relation_id', '=', 'nota_debets.id');
        // GET RECEIVABLE NOTA DEBET WHERE TYPE TRX KREDIT ATAU DEBET
        $join->where(function ($query) use ($credit, $debet) {
          $query->where('payable_nota.type_transaction_id', $credit);
          $query->orWhere('payable_nota.type_transaction_id', $debet);
        });
      })
      ->leftJoin('debt_details as debt_detail','debt_detail.payable_id','=','payable_nota.id')
      ->leftJoin('debts as debt','debt.id','=','debt_detail.header_id')
      ->leftJoin('accounts as account','account.id','=','jd.account_id')
      ->where('nota_debets.company_id', $value->id)
      ->select(
        'nota_debets.*',
        'payable.code as no_payable',
        'jd.account_id as jd_account_id',
        FacadesDB::raw('CONCAT(account.code, " - ", account.name) as account_name'),
        FacadesDB::raw('
        CASE
            WHEN nota_debets.jenis = 1 THEN "DEBET"
            ELSE "KREDIT"
        END as jenis_name'),
        'debt.code as no_reff',
        'company.name as company_name',
        'contact.name as contact_name'
      )
      ->groupBy('nota_debets.id')
      ->get();
      foreach ($data as $key2 => $value2) {
        $value2->date_transaction = dateView($value2->date_transaction);
        $value2->valas = 'IDR';
        $value2->amount = formatNumber($value2->amount);
      }
      if(count($data) <= 0) {
        $datas[$key][] = null;
      } else {
        $datas[$key][] = $data;
      }
    }
    $item = [
      'trx' => 'hutang',
      'companies' => $companies,
      'remark' => $remark,
      'datas' => $datas
    ];
    $pdf = PDF::loadView('finance.nota_koreksi', $item)
    ->setPaper('a4', 'landscape');
    // ->setPaper(array(0,0,609.4488,935.433), 'landscape'); // KERTAS F4
    return $pdf->stream('Laporan Koreksi Hutang.pdf');
   
  }

  public function get_register_pengeluaran_jo(Request $request){
    $wr = "1=1";
    $wr .= " and (ctd.job_order_cost_id is not null or ctd.manifest_cost_id is not null)";
    if ($request->start_date && $request->end_date) {
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->format('Y-m-d');
        $wr .= " and (ct.date_transaction between '$startDate' and '$endDate')";
    }
    if ($request->company_id) {
        $wr .= " and ct.company_id = $request->company_id";
    }
    if ($request->job_order_id) {
        $wr .= " and jo.id = $request->job_order_id";
    }

    $data = DB::table(DB::raw("cash_transaction_details as ctd"))
    ->leftJoin('cash_transactions as ct', 'ct.id', 'ctd.header_id')
    ->leftJoin('accounts', 'accounts.id', 'ctd.account_id')
    ->leftjoin('job_order_costs as joc', function($join){
      $join->on('joc.id', 'ctd.job_order_cost_id');
      $join->orOn('joc.manifest_cost_id', 'ctd.manifest_cost_id');
    })
    ->leftJoin('cost_types', 'cost_types.id', 'joc.cost_type_id')
    ->leftJoin('job_orders as jo', 'jo.id', 'joc.header_id')
    ->leftJoin('manifest_costs as mc', 'mc.id', 'ctd.manifest_cost_id')
    ->leftJoin('manifests as m', 'm.id', 'mc.header_id')
    ->leftJoin('delivery_manifests as dm', 'dm.id', 'm.id')
    ->leftJoin('delivery_order_drivers as dod', 'dod.id', 'dm.delivery_order_driver_id')
    ->leftJoin('vehicles as v', 'v.id', 'dod.vehicle_id')
    ->leftJoin('contacts as sopir', 'sopir.id', 'dod.driver_id')
    ->whereRaw($wr)
    ->orderBy('ct.date_transaction', 'DESC')
    ->select(
      'ct.date_transaction', 
      'ct.code', 
      'ct.reff', 
      'accounts.code as kode_akun', 
      'accounts.name as nama_akun',
      'cost_types.code as kode_biaya',
      'cost_types.name as nama_biaya',
      'ctd.description',
      'jo.code as kode_jo',
      'm.code as kode_manifest',
      'sopir.name as nama_sopir',
      'v.nopol as nopol',
      DB::raw('IF(ct.type = 1, "KAS", "BANK") as type'),
      'ctd.amount',
    )
    ->get();

    return json_decode(json_encode($data), true);
  }

  public function preview_register_pengeluaran_jo(Request $request){
    $resp['data'] = $this->get_register_pengeluaran_jo($request);
    return view('finance.register_pengeluaran_jo.preview', $resp);
  }

  public function excel_register_pengeluaran_jo(Request $request){
    $now = date("d_m_Y");
    $fileName = 'Register Pengeluaran Per JO ';
    return Excel::download(new RegisterPengeluaranJo($request), "$fileName$now.xlsx");
  }

  public function export_register_pengeluaran_jo(Request $request){
    $item['data'] = $this->get_register_pengeluaran_jo($request);
    $item['remark'] = FacadesDB::table('print_remarks')
      ->where('company_id', $request->company_id)
      ->first();
    $pdf = PDF::loadView('finance.register_pengeluaran_jo.export', $item);
    $now = date("d_m_Y");
    return $pdf->stream("Laporan Register Pengeluaran Per JO $now.pdf");
  }

  public function laba_rugi_jo_excel(Request $request) {

    $parent = Account::with('parent', 'parent.parent')->where('group_report', 2)->where('type_id', 54)->where('deep', '!=', '0');
    $parent = $parent->orderBy('code')->get();

    $datasHeader=[];
    $countChildAll = 0;
    $dataParent = [];
    foreach ($parent as $value) { 
      if ($value->deep == 1) {
        $dataParent[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'child' => []
        ];
      } else {
        $datasHeader[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id
        ];
        $countChildAll++;
      }
    }

    $dataJo = [];
    $jo = DB::table('job_orders')
    ->leftJoin('job_order_details', 'job_order_details.header_id', '=', 'job_orders.id')
    ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', '=', 'job_order_details.id')
    ->leftJoin('manifests', 'manifest_details.header_id', '=', 'manifests.id')
    ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', '=', 'manifests.id')
    ->leftJoin('delivery_order_drivers', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
    ->leftJoin('contacts as driver', 'driver.id', '=', 'delivery_order_drivers.driver_id')
    ->leftJoin('vehicles', 'vehicles.id', '=', 'delivery_order_drivers.vehicle_id')
    ->groupBy('job_orders.id')
    ->whereNotNull('job_orders.code')
    ->select(
      'job_orders.id',
      'job_orders.code',
      DB::raw('DATE_FORMAT(job_orders.shipment_date, "%Y-%m-%d") as shipment_date'),
      'job_orders.total_price',
      'job_orders.code_invoice',
      DB::raw('GROUP_CONCAT(DISTINCT vehicles.nopol) as nopol'),
      DB::raw('GROUP_CONCAT(DISTINCT driver.name) as driver_name')
    );

    if(isset($request->company_id)){
      $jo = $jo->where('job_orders.company_id', $request->company_id);
    }

    if(isset($request->is_invoice) && $request->is_invoice == 1){
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
      $jo = $jo->whereNotNull('job_orders.code_invoice')->where('job_orders.is_invoiced', 1);
    } else {
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
    }

    $dataJo = $jo->get();

    // CREATE HEADER EXCEL AND INSERT COST TO JO BASED ON AKUN BIAYA
    foreach ($dataParent as $key => $valueParent) {
      $parentId = $valueParent->id;
      foreach ($datasHeader as $keyChild => $valueChild) {
        if ($valueChild->parent == $parentId) {
            $dataParent[$key]->child[] = $valueChild;

            $valueChildId = $valueChild->id;

            $wrChild = "1=1";

            if(isset($request->is_invoice) && $request->is_invoice == 1){
              $wrChild .= " AND DATE_FORMAT(job_orders.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
              $wrChild .= " AND job_orders.code_invoice IS NOT NULL AND job_orders.is_invoiced = 1";
            } else {
              $wrChild .= " AND DATE_FORMAT(job_orders.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
            }

            foreach ($dataJo as $keyJo => $valueJo) {
              $joc = DB::table('job_order_costs')
              ->leftJoin('job_orders', 'job_orders.id', 'job_order_costs.header_id')
              ->leftJoin('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
              ->where('job_order_costs.status', '!=', 4)
              ->where('header_id', $valueJo->id)
              ->where('cost_types.akun_biaya', $valueChildId)
              ->whereRaw($wrChild)
              ->select(DB::raw('SUM(job_order_costs.qty_real*job_order_costs.price_real) as total'))->first();

              $dataJo[$keyJo]->child[$key][] = $joc->total;
            }
        }
      }
    };

    $data = [
      'dataHeader' => $dataParent,
      'countChildAll' => $countChildAll,
      'dataJo'=>$dataJo,
      'jenis' => 'jo'
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi_jo';
    return FacadesExcel::download(New LabaRugi($data), "$fileName$now.xlsx");
  }

  public function laba_rugi_nopol_excel(Request $request) {
    
    $parent = Account::with('parent', 'parent.parent')->where('group_report', 2)->where('type_id', 54)->where('deep', '!=', '0');
    $parent = $parent->orderBy('code')->get();

    $datasHeader=[];
    $countChildAll = 0;
    $dataParent = [];
    foreach ($parent as $value) { 
      if ($value->deep == 1) {
        $dataParent[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'child' => []
        ];
      } else {
        $datasHeader[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id
        ];
        $countChildAll++;
      }
    }

    // START UNTUK AMBIL PENDAPATAN NOPOL DARI JO 
    $dataJo = [];
    $jo = DB::table('job_orders')
    ->leftJoin('job_order_details', 'job_order_details.header_id', '=', 'job_orders.id')
    ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', '=', 'job_order_details.id')
    ->leftJoin('manifests', 'manifest_details.header_id', '=', 'manifests.id')
    ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', '=', 'manifests.id')
    ->leftJoin('delivery_order_drivers', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
    ->leftJoin('contacts as driver', 'driver.id', '=', 'delivery_order_drivers.driver_id')
    ->leftJoin('vehicles', 'vehicles.id', '=', 'delivery_order_drivers.vehicle_id')
    ->whereNotNull('job_orders.code')
    ->groupBy('job_orders.id')
    ->select(
      'job_orders.id',
      'job_orders.code',
      DB::raw('COUNT(DISTINCT manifests.id) as manifest_count'),
      DB::raw('DATE_FORMAT(job_orders.shipment_date, "%Y-%m-%d") as shipment_date'),
      'job_orders.total_price',
      'job_orders.code_invoice',
      DB::raw('GROUP_CONCAT(DISTINCT vehicles.id) as id_vehicle'),
      DB::raw('GROUP_CONCAT(DISTINCT vehicles.nopol) as nopol'),
      DB::raw('GROUP_CONCAT(DISTINCT driver.name) as driver_name')
    );

    $vehicles = DB::table('vehicles')
    ->leftJoin('companies as company', 'company.id', 'vehicles.company_id')
    ->select(
      'vehicles.id',
      'vehicles.code',
      'vehicles.nopol',
      'company.name as company_name',
    );

    if(isset($request->company_id)){
      $jo = $jo->where('job_orders.company_id', $request->company_id);
      $vehicles = $vehicles->where('vehicles.company_id', $request->company_id);
    }

    if(isset($request->is_invoice) && $request->is_invoice == 1){
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
      $jo = $jo->whereNotNull('job_orders.code_invoice')->where('job_orders.is_invoiced', 1);
    } else {
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
    }

    $dataJo = $jo->get();
    $vehicles = $vehicles->get();

    foreach ($dataJo as $key => $jo) {
      $idVehicles = explode(',', $jo->id_vehicle);

      foreach ($idVehicles as $idVehicle) {
          $vehicle = collect($vehicles)->firstWhere('id', $idVehicle);

          if ($vehicle) {
            if (!isset($vehicle->pendapatan)) {
              $vehicle->pendapatan = 0;
            }

            $vehicle->pendapatan += ($jo->total_price / count($idVehicles));
          }
      }
    }
    // END UNTUK AMBIL PENDAPATAN NOPOL DARI JO 

    // START UNTUK AMBIL COST NOPOL DARI JO 
    $wr = "1=1";
    if ($request->company_id) {
      $wr .= " and jo.company_id = $request->company_id";
    }
    if(isset($request->is_invoice) && $request->is_invoice == 1){
      $wr .= " AND DATE_FORMAT(jo.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
      $wr .= " AND jo.code_invoice IS NOT NULL AND jo.is_invoiced = 1";
    } else {
      $wr .= " AND DATE_FORMAT(jo.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
    }

    // CREATE HEADER EXCEL AND INSERT COST TO JO BASED ON AKUN BIAYA
    foreach ($dataParent as $key => $valueParent) {
      $parentId = $valueParent->id;
      foreach ($datasHeader as $keyChild => $valueChild) {
        if ($valueChild->parent == $parentId) {
            if (!isset($dataParent[$key])) {
              $dataParent[$key] = new stdClass(); // Initializing the parent object
              $dataParent[$key]->child = []; // Initializing the child array
            }

            $dataParent[$key]->child[] = $valueChild;

            $valueChildId = $valueChild->id;

            foreach ($vehicles as $keyVehicle => $valueVehicle) {
              $wr2 = "";
              $wr2 .= "cost_types.akun_biaya = '".$valueChildId."' and vehicles.id = '".$valueVehicle->id."'";

              $cost=FacadesDB::select("select 
                case 
                    when ct.is_co_driver=1 then co_driver.name
                    when ct.is_driver=1 then supir.name
                    when supir.name is null then vendor.name
                    end as name,
                case 
                    when ct.is_co_driver=1 and ct.is_driver is not null then 'Co-Driver'
                    when ct.is_driver=1 and ct.is_co_driver!=1 then 'Driver'
                    when supir.name is null then 'Vendor'
                    end as kategori,
                if(dod.vehicle_id is not null,vehicles.id, dod.vehicle_id) as id_kendaraan,
                if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
                dod.created_at AS tgl_sj,
                dod.code AS sj_code,
                dod.co_driver_id,
                dod.driver_id,
                ma.code AS ma_code,
                ma.no_sj_customer,
                joc.cost_type_id,
                COALESCE(mc.qty_real,joc.qty_real)qty,
                COALESCE(mc.price ,joc.price) AS price_rencana,
                (COALESCE(mc.qty_real,joc.qty_real)*COALESCE(mc.price_real,joc.price_real)) as price,
                ct.name as cost_name,
                ct.is_driver,
                ct.is_co_driver,
                CASE
                    WHEN joc.status = 10 THEN 'Sudah Terealisasi'
                    ELSE 'Belum Diajukan'
                END as status_cost_name,
                wo.code as wo_code,
                ct.name as cost_type_name,
                jo.created_at AS tgl_jo,
                jo.code AS jo_code
                from job_order_costs joc
                left join cost_types ct on ct.id = joc.cost_type_id
                left join contacts as vendor on vendor.id = joc.vendor_id
                left join job_orders jo on jo.id =joc.header_id 
                left join work_orders wo on wo.id=jo.work_order_id 
                left join companies comp on comp.id=jo.company_id
                LEFT JOIN services as s ON s.id = jo.service_id
                LEFT JOIN routes ON routes.id = jo.route_id
                LEFT JOIN contacts as sender ON sender.id = jo.sender_id
                LEFT JOIN contacts as receiver ON receiver.id = jo.receiver_id
                left join job_order_details jod on jod.header_id=jo.id
                LEFT JOIN pieces ON pieces.id = jod.piece_id
                LEFT JOIN quotations ON quotations.id = jod.quotation_id
                LEFT JOIN contacts as co ON co.id = jo.customer_id
                LEFT JOIN container_types as cty ON cty.id = jo.container_type_id
                LEFT JOIN modas ON modas.id = jo.moda_id
                LEFT JOIN kpi_statuses as kpi_status ON kpi_status.id = jo.kpi_id
                LEFT JOIN contacts as collect ON collect.id = jo.collectible_id
                LEFT JOIN manifest_costs as mc ON mc.id = joc.manifest_cost_id
                LEFT JOIN manifests as ma ON ma.id = mc.header_id
                LEFT JOIN containers ON containers.id = ma.container_id
                LEFT JOIN cost_types ON cost_types.id = mc.cost_type_id
                LEFT JOIN delivery_manifests as dm ON dm.manifest_id = ma.id
                LEFT JOIN delivery_order_drivers as dod ON dod.id = dm.delivery_order_driver_id
                LEFT JOIN vehicles ON vehicles.id = dod.vehicle_id
                LEFT JOIN contacts as supir ON supir.id = dod.driver_id
                LEFT JOIN contacts as co_driver ON co_driver.id = dod.co_driver_id
                LEFT JOIN banks as bank_supir ON bank_supir.id = supir.rek_bank_id
                LEFT JOIN banks as bankvendor ON bankvendor.id = vendor.rek_bank_id
                LEFT JOIN banks as bankcdv ON bankcdv.id = co_driver.rek_bank_id
                where $wr and $wr2 and (dod.status < 3 or dod.status is null) and jo.is_cancel <> 1
              order by shipment_date DESC, jo_code DESC");

              $totalCost = 0;

              foreach ($cost as $keyCost => $valueCost) {
                $totalCost += $valueCost->price;
              }

              $vehicles[$keyVehicle]->child[$key][] = $totalCost;
            }
        }
      }
    };
    // END UNTUK AMBIL COST NOPOL DARI JO 

    // dd($vehicles);

    $data = [
      'dataHeader' => $dataParent,
      'countChildAll' => $countChildAll,
      'dataVehicle'=>$vehicles,
      'jenis' => 'nopol'
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi_nopol';
    return FacadesExcel::download(New LabaRugi($data), "$fileName$now.xlsx");
  }

  public function laba_rugi_driver_excel(Request $request) {
    
    $parent = Account::with('parent', 'parent.parent')->where('group_report', 2)->where('type_id', 54)->where('deep', '!=', '0');
    $parent = $parent->orderBy('code')->get();

    $datasHeader=[];
    $countChildAll = 0;
    $dataParent = [];
    foreach ($parent as $value) { 
      if ($value->deep == 1) {
        $dataParent[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id,
          'child' => []
        ];
      } else {
        $datasHeader[] = (object)[
          'id' => $value->id,
          'deep' => $value->deep,
          'code' => $value->code,
          'name' => $value->name,
          'is_base' => $value->is_base,
          'parent' => $value->parent_id
        ];
        $countChildAll++;
      }
    }

    // START UNTUK AMBIL PENDAPATAN NOPOL DARI JO 
    $dataJo = [];
    $jo = DB::table('job_orders')
    ->leftJoin('job_order_details', 'job_order_details.header_id', '=', 'job_orders.id')
    ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', '=', 'job_order_details.id')
    ->leftJoin('manifests', 'manifest_details.header_id', '=', 'manifests.id')
    ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', '=', 'manifests.id')
    ->leftJoin('delivery_order_drivers', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
    ->leftJoin('contacts as driver', 'driver.id', '=', 'delivery_order_drivers.driver_id')
    ->leftJoin('vehicles', 'vehicles.id', '=', 'delivery_order_drivers.vehicle_id')
    ->whereNotNull('job_orders.code')
    ->groupBy('job_orders.id')
    ->select(
      'job_orders.id',
      'job_orders.code',
      DB::raw('COUNT(DISTINCT manifests.id) as manifest_count'),
      DB::raw('DATE_FORMAT(job_orders.shipment_date, "%Y-%m-%d") as shipment_date'),
      'job_orders.total_price',
      'job_orders.code_invoice',
      DB::raw('GROUP_CONCAT(DISTINCT driver.id) as id_driver'),
      DB::raw('GROUP_CONCAT(DISTINCT driver.name) as driver_name')
    );

    $drivers = DB::table('contacts')
    ->leftJoin('companies as company', 'company.id', 'contacts.company_id')
    ->where('is_driver', 1)
    ->select(
      'contacts.id',
      'contacts.name',
      'company.name as company_name',
    );

    if(isset($request->company_id)){
      $jo = $jo->where('job_orders.company_id', $request->company_id);
      $drivers = $drivers->where('contacts.company_id', $request->company_id);
    }

    if(isset($request->is_invoice) && $request->is_invoice == 1){
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
      $jo = $jo->whereNotNull('job_orders.code_invoice')->where('job_orders.is_invoiced', 1);
    } else {
      $jo = $jo->whereRaw(" DATE_FORMAT(job_orders.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'");
    }

    $dataJo = $jo->get();
    $drivers = $drivers->get();

    foreach ($dataJo as $key => $jo) {
      $idDrivers = explode(',', $jo->id_driver);

      foreach ($idDrivers as $idDriver) {
        $driver = collect($drivers)->firstWhere('id', $idDriver);

        if ($driver) {
          if (!isset($driver->pendapatan)) {
            $driver->pendapatan = 0;
          }

          $driver->pendapatan += ($jo->total_price / count($idDrivers));
        }
      }
    }
    // END UNTUK AMBIL PENDAPATAN NOPOL DARI JO 

    // START UNTUK AMBIL COST NOPOL DARI JO 
    $wr = "1=1";
    if ($request->company_id) {
      $wr .= " and jo.company_id = $request->company_id";
    }
    if(isset($request->is_invoice) && $request->is_invoice == 1){
      $wr .= " AND DATE_FORMAT(jo.date_invoice, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
      $wr .= " AND jo.code_invoice IS NOT NULL AND jo.is_invoiced = 1";
    } else {
      $wr .= " AND DATE_FORMAT(jo.created_at, '%m-%Y') BETWEEN '".$request->start_month."' AND '".$request->end_month."'";
    }

    // CREATE HEADER EXCEL AND INSERT COST TO JO BASED ON AKUN BIAYA
    foreach ($dataParent as $key => $valueParent) {
      $parentId = $valueParent->id;
      foreach ($datasHeader as $keyChild => $valueChild) {
        if ($valueChild->parent == $parentId) {
            if (!isset($dataParent[$key])) {
              $dataParent[$key] = new stdClass(); // Initializing the parent object
              $dataParent[$key]->child = []; // Initializing the child array
            }

            $dataParent[$key]->child[] = $valueChild;

            $valueChildId = $valueChild->id;

            foreach ($drivers as $keyDriver => $valueDriver) {
              $wr2 = "";
              $wr2 .= "cost_types.akun_biaya = '".$valueChildId."' and dod.driver_id = '".$valueDriver->id."'";

              $cost=FacadesDB::select("select 
                case 
                    when ct.is_co_driver=1 then co_driver.name
                    when ct.is_driver=1 then supir.name
                    when supir.name is null then vendor.name
                    end as name,
                case 
                    when ct.is_co_driver=1 and ct.is_driver is not null then 'Co-Driver'
                    when ct.is_driver=1 and ct.is_co_driver!=1 then 'Driver'
                    when supir.name is null then 'Vendor'
                    end as kategori,
                if(dod.vehicle_id is not null,vehicles.id, dod.vehicle_id) as id_kendaraan,
                if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
                dod.created_at AS tgl_sj,
                dod.code AS sj_code,
                dod.co_driver_id,
                dod.driver_id,
                ma.code AS ma_code,
                ma.no_sj_customer,
                joc.cost_type_id,
                COALESCE(mc.qty_real,joc.qty_real)qty,
                COALESCE(mc.price ,joc.price) AS price_rencana,
                (COALESCE(mc.qty_real,joc.qty_real)*COALESCE(mc.price_real,joc.price_real)) as price,
                ct.name as cost_name,
                ct.is_driver,
                ct.is_co_driver,
                CASE
                    WHEN joc.status = 10 THEN 'Sudah Terealisasi'
                    ELSE 'Belum Diajukan'
                END as status_cost_name,
                wo.code as wo_code,
                ct.name as cost_type_name,
                jo.created_at AS tgl_jo,
                jo.code AS jo_code
                from job_order_costs joc
                left join cost_types ct on ct.id = joc.cost_type_id
                left join contacts as vendor on vendor.id = joc.vendor_id
                left join job_orders jo on jo.id =joc.header_id 
                left join work_orders wo on wo.id=jo.work_order_id 
                left join companies comp on comp.id=jo.company_id
                LEFT JOIN services as s ON s.id = jo.service_id
                LEFT JOIN routes ON routes.id = jo.route_id
                LEFT JOIN contacts as sender ON sender.id = jo.sender_id
                LEFT JOIN contacts as receiver ON receiver.id = jo.receiver_id
                left join job_order_details jod on jod.header_id=jo.id
                LEFT JOIN pieces ON pieces.id = jod.piece_id
                LEFT JOIN quotations ON quotations.id = jod.quotation_id
                LEFT JOIN contacts as co ON co.id = jo.customer_id
                LEFT JOIN container_types as cty ON cty.id = jo.container_type_id
                LEFT JOIN modas ON modas.id = jo.moda_id
                LEFT JOIN kpi_statuses as kpi_status ON kpi_status.id = jo.kpi_id
                LEFT JOIN contacts as collect ON collect.id = jo.collectible_id
                LEFT JOIN manifest_costs as mc ON mc.id = joc.manifest_cost_id
                LEFT JOIN manifests as ma ON ma.id = mc.header_id
                LEFT JOIN containers ON containers.id = ma.container_id
                LEFT JOIN cost_types ON cost_types.id = mc.cost_type_id
                LEFT JOIN delivery_manifests as dm ON dm.manifest_id = ma.id
                LEFT JOIN delivery_order_drivers as dod ON dod.id = dm.delivery_order_driver_id
                LEFT JOIN vehicles ON vehicles.id = dod.vehicle_id
                LEFT JOIN contacts as supir ON supir.id = dod.driver_id
                LEFT JOIN contacts as co_driver ON co_driver.id = dod.co_driver_id
                LEFT JOIN banks as bank_supir ON bank_supir.id = supir.rek_bank_id
                LEFT JOIN banks as bankvendor ON bankvendor.id = vendor.rek_bank_id
                LEFT JOIN banks as bankcdv ON bankcdv.id = co_driver.rek_bank_id
                where $wr and $wr2 and (dod.status < 3 or dod.status is null) and jo.is_cancel <> 1
              order by shipment_date DESC, jo_code DESC");

              $totalCost = 0;

              foreach ($cost as $keyCost => $valueCost) {
                $totalCost += $valueCost->price;
              }

              $drivers[$keyDriver]->child[$key][] = $totalCost;
            }
        }
      }
    };
    // END UNTUK AMBIL COST NOPOL DARI JO 

    // dd($drivers);

    $data = [
      'dataHeader' => $dataParent,
      'countChildAll' => $countChildAll,
      'dataVehicle'=>$drivers,
      'jenis' => 'driver'
    ];
    $now = date("d_m_Y_H_i_s");
    $fileName = " ";
    $fileName .= 'laba_rugi_driver';
    return FacadesExcel::download(New LabaRugi($data), "$fileName$now.xlsx");
  }
}
