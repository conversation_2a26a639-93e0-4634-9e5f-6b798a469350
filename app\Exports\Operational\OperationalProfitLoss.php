<?php

namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OperationalProfitLoss implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        return collect($report_controller->getProfitLoss($this->request));
    }

    public function headings(): array
    {
        $header = [];
        if ($this->request->tipe_laporan == 'nopol') {
            $header =  [
                'No. Polisi',
                'Revenue',
                'Total Real',
                'Profit/Loss'
            ];
        }

        if ($this->request->tipe_laporan == 'driver') {
            $header =  [
                'Nama Driver',
                'Revenue',
                'Total Real',
                'Profit/Loss'
            ];
        }
        return $header;
    }
    public function map($data): array
    {
        $mapping = [];
        if ($this->request->tipe_laporan == 'nopol') {
            $mapping = [
                $data['nopol'] ?? '-',
                number_format($data['revenue']) ?? '-',
                number_format($data['total_real']) ?? '-',
                number_format($data['revenue'] - $data['total_real']) ?? '-'
            ];
        }

        if ($this->request->tipe_laporan == 'driver') {
            $mapping = [
                $data['name'] ?? '-',
                number_format($data['revenue']) ?? '-',
                number_format($data['total_real']) ?? '-',
                number_format($data['revenue'] - $data['total_real']) ?? '-'
            ];
        }

        return $mapping;
    }
}
