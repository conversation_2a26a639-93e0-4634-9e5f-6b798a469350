<?php
namespace App\Abstracts\Inventory;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use App\Utils\TransactionCode;
use App\Abstracts\Setting\Setting;
use App\Abstracts\Setting\Checker;
use App\Abstracts\Inventory\Warehouse;
use App\Abstracts\Inventory\ReturDetail;
use App\Abstracts\Inventory\ReturStatus;
use App\Abstracts\Inventory\ReturType;
use App\Abstracts\PurchaseOrder;
use App\Model\ReturDetail as ModelReturDetail;
use App\Model\StockTransaction;
use App\Model\WarehouseStockDetail;
use App\Model\WarehouseReceiptDetail as WRD;
use Illuminate\Support\Facades\DB;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Model\TypeTransaction;
class Retur
{
    protected static $table = 'returs';
    public static $type_transaction = 'retur';

    /*
      Date : 05-03-2021
      Description : Mengquery data
      Developer : Didin
      Status : Create
    */
    public static function query() {
        $dt = DB::table(self::$table);

        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Menampilkan daftar nama item condition
      Developer : Didin
      Status : Create
    */
    public static function index() {
        $dt = self::query();
        $dt = $dt->get();

        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Mengambil parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($args) {
        $params = [];

        $params['date_transaction'] = $args['date_transaction'] ?? null;
        Checker::checkDate($params['date_transaction']);
        $params['date_transaction'] = Carbon::parse($params['date_transaction'])->format('Y-m-d');
        $params['description'] = $args['description'] ?? null;
        $params['supplier_id'] = $args['supplier_id'] ?? null;
        $params['warehouse_id'] = $args['warehouse_id'] ?? null;
        $params['create_by'] = $args['create_by'] ?? null;
        $params['purchase_order_id'] = $args['purchase_order_id'] ?? null;

        if(!$params['warehouse_id']) {
            throw new Exception('Warehouse is required');
        } else {
            $wh = Warehouse::show($params['warehouse_id']);
            $params['company_id'] = $wh->company_id;
        }

        return $params;
    }

    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();

        if(!$dt) {
            throw new Exception('Retur not found');
        }
    }

    /*
      Date : 29-08-2021
      Description : Menampilkan detail kategori barang
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);
        $dt = DB::table(self::$table);
        $dt = $dt->leftJoin('companies', 'companies.id', self::$table . '.company_id');
        $dt = $dt->leftJoin('warehouses', 'warehouses.id', self::$table . '.warehouse_id');
        $dt = $dt->leftJoin('contacts', 'contacts.id', self::$table . '.supplier_id');
        $dt = $dt->leftJoin('retur_statuses', 'retur_statuses.id', self::$table . '.status');
        $dt = $dt->where(self::$table . '.id', $id);
        $dt = $dt->select(self::$table . '.*', 'warehouses.name AS warehouse_name', 'companies.name AS company_name', 'retur_statuses.name AS status_name', 'contacts.name AS supplier_name');
        $dt = $dt->first();

        return $dt;
    }

    /*
      Date : 29-08-2021
      Description : Menyimpan data
      Developer : Didin
      Status : Create
    */
    public static function store($params) {
        $insert = self::fetch($params);

        $company_id = $insert['company_id'];
        $code = new TransactionCode($company_id, 'retur');
        $code->setCode();
        $trx_code = $code->getCode();
        $insert['created_at'] = Carbon::now();
        $insert['code'] = $trx_code;
        $insert['status'] = ReturStatus::getDraft();
        $insert['type_retur'] = ReturType::getItem();
        // dd($insert);
        // die();
        $id = DB::table(self::$table)->insertGetId($insert);

        ReturDetail::storeMultiple($params['detail'], $id);

        return $id;
    }

    /*
      Date : 29-08-2021
      Description : Update data
      Developer : Didin
      Status : Create
    */
    public static function update($params, $id) {
        self::validateIsApproved($id);
        $detail = $params ['detail'] ?? null;
        $update = self::fetch($params);

        DB::table(self::$table)
        ->whereId($id)
        ->update($update);

        if($detail && is_array($detail)) {
            ReturDetail::clearStock($id);
            ReturDetail::clear($id);

            ReturDetail::storeMultiple($detail, $id);
        }
    }

    /*
      Date : 14-03-2021
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validate($id);
        DB::table(self::$table)
        ->whereId($id)
        ->delete();
    }

    /*
      Date : 14-03-2021
      Description : Menghapus stok
      Developer : Didin
      Status : Create
    */
    /*
      Date : 13-08-2021
      Description : Ubah Class StockTransaction
      Developer : Hendra
      Status : Edit
    */
    public static function clearStock($job_order_id) {
        $items = self::index($job_order_id)->where('requested_stock_transaction_id', '!=', null)->pluck('requested_stock_transaction_id')->toArray();
        DB::table(self::$table)->whereHeaderId($job_order_id)->update([
            'requested_stock_transaction_id' => null
        ]);
        StockTransaction::destroyMultiple($items);
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya draft / request
      Developer : Didin
      Status : Create
    */
    public static function getRequestedStatus() {
        return 1;
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya item keluar
      Developer : Didin
      Status : Create
    */
    public static function getOutboundStatus() {
        return 2;
    }

    /*
      Date : 23-03-2021
      Description : Memperoleh status yang tipe nya item masuk
      Developer : Didin
      Status : Create
    */
    public static function getInboundStatus() {
        return 3;
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dikeluarkan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsTakeout($id) {
        $dt = self::show($id);
        $approveStatus = self::getOutboundStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was take out');
        }
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dimasukkan di rak tujuan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsTakein($id) {
        $dt = self::show($id);
        $approveStatus = self::getInboundStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was take in at destination bin location');
        }
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dimasukkan di rak tujuan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsRequested($id) {
        $dt = self::show($id);
        $approveStatus = self::getRequestedStatus();
        if($dt->status == $approveStatus) {
            throw new Exception('Data still requested');
        }
    }

    /*
      Date : 29-08-2021
      Description : Membuat pengeluaran barang
      Developer : Didin
      Status : Create
    */
    public static function itemOut($approve_by, $id) {
        self::validateIsTakeout($id);
        self::validateIsTakein($id);

        DB::table(self::$table)->whereId($id)
        ->update([
            'status' => self::getOutboundStatus(),
            'approve_by' => $approve_by,
            'date_approve' => Carbon::now()
        ]);

        ReturDetail::doMultipleOutbound($id);
    }

    /*
      Date : 29-08-2021
      Description : Membuat penerimaan barang
      Developer : Didin
      Status : Create
    */
    public static function itemIn($approve_by, $id) {
        self::validateIsRequested($id);
        self::validateIsTakein($id);

        DB::table(self::$table)->whereId($id)
        ->update([
            'status' => self::getInboundStatus(),
            'approve_by' => $approve_by,
            'date_approve' => Carbon::now()
        ]);

        ReturDetail::doMultipleInbound($id);
    }

    /*
      Date : 23-03-2021
      Description : Memvalidasi apakah data sudah dikeluarkan atau belum
      Developer : Didin
      Status : Create
    */
    public static function validateIsApproved($id) {
        $dt = self::show($id);
        $approveStatus = ReturStatus::getApproved();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was approved');
        }
    }

    /*
    *  Date : 18-08-2021
    *  Description : Memvalidasi apakah data memiliki status reject atau tidak
    *  Developer : Hendra
    *  Status : Create
    */
    public static function validateIsRejected($id) {
        $dt = self::show($id);
        $approveStatus = ReturStatus::getRejected();
        if($dt->status == $approveStatus) {
            throw new Exception('Data was rejected');
        }
    }

    /*
      Date : 29-07-2021
      Description : Membuat pengeluaran barang
      Developer : Didin
      Status : Create
    */
    /*
      Date : 13-08-2021
      Description : Fix change Class UsingItemDetail to ReturDetail
      Developer : Hendra
      Status : Edit
    */
    public static function approve($id) {
        self::validateIsApproved($id);
        self::validateIsRejected($id);
        
        DB::beginTransaction();

        DB::table(self::$table)->whereId($id)
        ->update([
            'status' => ReturStatus::getApproved(),
            'updated_at' => Carbon::now()
        ]);

        $retur=DB::table(self::$table)->whereId($id)->first();
        ReturDetail::doMultipleOutbound($id);//mengurangi stok transaksi stocktransaction
        $returDetail = ModelReturDetail::where('header_id', $id)->get();
        
        //create jurnal
        if(Journal::is_use_finance()){ // improve jika pake modul finance
          self::journal_data_retur($id);
        }
        //end create jurnal retur

        // normalize onhand_qty and qty
        foreach($returDetail as $rd){ //update j
          $wr_detail= DB::table('warehouse_receipt_details')->where('id',$rd->warehouse_receipt_detail_id)->first();
          if($wr_detail != null){
              $update_wr_detail=  WRD::find($wr_detail->id);
              $update_wr_detail->return_qty = $wr_detail->return_qty+$rd->qty_retur;
              $update_wr_detail->qty= $wr_detail->qty-$rd->qty_retur;
              $update_wr_detail->save();
          }

          $wsd = WarehouseStockDetail::where('warehouse_receipt_detail_id', $rd->warehouse_receipt_detail_id)
                                    ->where('rack_id', $rd->rack_id)
                                    ->where('item_id', $rd->item_id)
                                    ->first();
          if($wsd){
            $wsd->onhand_qty -= ($rd->qty_retur * 2);
            $wsd->available_qty += $rd->qty_retur;
            $wsd->qty -= $rd->qty_retur;
            $wsd->save();
          }
        }

        //update status po
        if($retur->purchase_order_id != null){
          PurchaseOrder::outstanding($retur->purchase_order_id);
        }
        DB::commit();
    }
    public static function journal_data_retur($id){
      $retur=DB::table(self::$table)->whereId($id)->first();//get retur
      $data=DB::table('warehouse_receipts')
      ->leftJoin('warehouse_receipt_details','warehouse_receipt_details.header_id','warehouse_receipts.id')
      ->leftJoin('retur_details','retur_details.warehouse_receipt_detail_id','warehouse_receipt_details.id')
      ->leftJoin('returs','returs.id','retur_details.header_id')
      ->where('retur_details.header_id',$id)
      ->selectRaw('
          warehouse_receipts.code,
          warehouse_receipts.purchase_order_id,
          warehouse_receipt_details.*')
      ->first();
      $po_id=$retur->purchase_order_id==$data->purchase_order_id?$retur->purchase_order_id:$data->purchase_order_id;
      $data_detail=DB::table('retur_details')->where('retur_details.header_id',$id)->get();
      $dataPO= DB::table('purchase_orders')->where('id',$po_id)->first();
      $j=Journal::create([
          'company_id' => $retur->company_id,
          'type_transaction_id' => TypeTransaction::where(['slug'=>'retur','is_journal'=>1])->first()->id,
          'date_transaction' => Carbon::now()->format('Y-m-d'),
          'created_by' => auth()->id(),
          'code' => $retur->code,
          'description' => 'Telah direncanakan PO retur pada transaksi #' . @$dataPO->code,
          'debet' => 0,
          'credit' => 0
        ]);
      foreach($data_detail as $x){
          $item=DB::table('items')->where('id',$x->item_id)->first();
          $dataPODetail= DB::table('purchase_order_details')->where('header_id',@$dataPO->id)->where('item_id',$x->item_id)->first();
          // create jurnal detail
          $account_default = DB::table('account_defaults')->first();
          //get account debet
          if($item->is_stock == 1){ //check if item stock
              if($item->account_id != null){
                  $account_debet=$item->account_id;
              }else{
                  $account_debet= $account_default->persediaan;
              }
              if($account_debet == null) {
                  throw new Exception('Akun persediaan belum di-set pada setting default akun');
              }
          }else{
              if($item->account_usage != null){
                  $account_debet=$item->account_usage;
              }else{
                  $account_debet= $account_default->item_usage;
              }
              if($account_debet == null) {
                  throw new Exception('Akun biaya penggunan item belum di-set pada setting default akun');
              }
          }
          if($x->item_id == @$dataPODetail->item_id){
              $debet= $x->qty_retur * $dataPODetail->price;
          }
          $journalDetailData=JournalDetail::where('header_id',$j->id)->where('account_id',$account_debet)->first();
          if($journalDetailData == null){
              JournalDetail::create([
                  'header_id' => $j->id,
                  'account_id' => $account_debet,
                  'debet' => $debet ?? 0,
                  'description' => 'Retur atas '.$item->code.'#'.$item->name,
              ]);
          }else{
              $updateJournalDebetDetail=JournalDetail::find($journalDetailData->id);
              $updateJournalDebetDetail->debet= $updateJournalDebetDetail->debet+$debet;
              $updateJournalDebetDetail->save();
          }
      }
      $contact= DB::table('contacts')->where('id',@$dataPO->supplier_id)->first();
          if(@$contact->akun_hutang != null){
              $account_credit= $contact->akun_hutang;
          }else{
              $account_credit= $account_default->hutang;
          }
      //get account credit
      if($account_credit == null) {
          throw new Exception('Akun credit belum di-set pada setting default akun');
      }

      $journalDataTotal= JournalDetail::where('header_id',$j->id)->sum('debet');
      JournalDetail::create([
          'header_id' => $j->id,
          'account_id' => $account_credit,
          'credit' => $journalDataTotal,
          'description' => 'Retur atas '.$item->code.'#'.$item->name,
      ]);

      //update debet credit total
      $journal= Journal::find($j->id);
      $journal->debet= $journalDataTotal;
      $journal->credit= $journalDataTotal;
      $journal->save();
  }

    /**
     * Date : 18-08-2021
     * Description : Merubah status menjadi reject
     * Developer : Hendra
     * Status : Create
     */
    public static function reject($id) {
        self::validateIsApproved($id);
        self::validateIsRejected($id);

        DB::table(self::$table)->whereId($id)
        ->update([
            'status' => ReturStatus::getRejected(),
            'updated_at' => Carbon::now()
        ]);

        ReturDetail::clearStock($id);
    }



}
