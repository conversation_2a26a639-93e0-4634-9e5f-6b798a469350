<?php

namespace App\Http\Controllers\Api\v5;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Setting\JobStatus;
use App\Model\Manifest;
use App\Model\ManifestStatusLog;
use App\Model\DeliveryOrderDriver as SJ;
use Exception;
use Illuminate\Support\Facades\DB;

class ManifestController extends Controller
{
   public function submitStatus(Request $request, $id)
   {
      $resp = [];
      $resp['message'] = 'OK';
      $resp['data'] = null;
      $status_code = 200;

      DB::beginTransaction();
      try {
         $jo = DB::table('manifests')
         ->leftJoin('manifest_details', 'manifest_details.header_id','manifests.id')
         ->leftJoin('job_order_details', 'job_order_details.id','manifest_details.job_order_detail_id')
         ->leftJoin('job_orders', 'job_orders.id','job_order_details.header_id')
         ->where('manifests.id' , $id)
         ->select('job_orders.service_type_id')
         ->first();

         // if(in_array($jo->service_type_id, [1,2,20,21,22,23,24,25,26,27,28])){
         //    $status = [
         //        0 => 2,     //Assigned To Driver
         //        1 => 6,     //Muat Barang
         //        2 => 9,     //Bongkar Muatan
         //        3 => 10,    //Selesai Bongkar
         //        4 => 17,    //Muat Container
         //        5 => 19,    //Bongkar Container
         //        6 => 0,     //Status tidak bisa terupdate
         //    ];
         // }else{
         //       $status = [
         //          0 => 2,     //Assigned To Driver
         //          1 => 6,     //Muat Barang
         //          2 => 9,     //Bongkar Muatan
         //          3 => 10,    //Selesai Bongkar
         //          4 => 0,     //Status tidak bisa terupdate
         //    ];
         // } 

         if(in_array($jo->service_type_id, [1,2,3,4])){
            $status = [
               0 => 2,     //Assigned To Driver
               1 => 6,     //Muat Barang
               2 => 9,     //Bongkar Muatan
               3 => 10,    //Selesai Bongkar
               4 => 0,     //Status tidak bisa terupdate
           ];
        }
        elseif(in_array($jo->service_type_id, [20,21,24])){
            $status = [
               1 => 2,     //Assigned To Driver
               2 => 17,    //Muat Container
               3 => 19,    //Bongkar Container
               4 => 10,    //Selesai Bongkar
               5 => 0,     //Status tidak bisa terupdate
         ];
        }
        elseif(in_array($jo->service_type_id, [22,27])){
            $status = [
               1 => 2,     //Assigned To Driver
               2 => 17,    //Muat Container
               3 => 9,     //Bongkar Muatan
               4 => 10,    //Selesai Bongkar
               5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [23])){
            $status = [
               0 => 2,     //Assigned To Driver
               1 => 17,    //Muat Container
               2 => 9,     //Bongkar Muatan
               3 => 19,    //Bongkar Container
               4 => 10,    //Selesai Bongkar
               5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [28])){
            $status = [
               0 => 2,     //Assigned To Driver
               1 => 17,    //Muat Container
               2 => 6,     //Muat Barang
               3 => 19,    //Bongkar Container
               4 => 10,    //Selesai Bongkar
               5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [25,26])){
            $status = [
               0 => 2,     //Assigned To Driver
               1 => 6,     //Muat Barang
               2 => 19,    //Bongkar Container
               3 => 10,    //Selesai Bongkar
               4 => 0,     //Status tidak bisa terupdate
            ];
        }else{
            $status = [
               0 => 2,     //Assigned To Driver
               1 => 6,     //Muat Barang
               2 => 9,     //Bongkar Muatan
               3 => 10,    //Selesai Bongkar
               4 => 17,    //Muat Container
               5 => 19,    //Bongkar Container
               6 => 0,     //Status tidak bisa terupdate
            ];
        }
        
         $manifest = Manifest::find($id);
         $status_no = array_search($manifest->job_status_id, $status);
         $next_status = $status_no + 1;

          //Cek status apakah masih bisa diupdate ke next status
         if($status[$next_status] == 0){
            $resp['message'] = 'Status sudah selesai tidak bisa diupdate';
            $status_code = 500;

            return response()->json($resp, $status_code);
         }

         //Menyimpan data manifest status log
         ManifestStatusLog::create([
            'manifest_id' => $id,
            'job_status_id' => $status[$next_status],
            'created_by' => auth()->id(),
         ]);

         //Update status manifest
         $manifest->update([
            'job_status_id' => $status[$next_status]
         ]);

         $status_name = DB::table('job_statuses')->where('id', $status[$next_status])->select('name')->first();

         $resp['data'] = ['current_status' => $status_name->name];

         DB::commit();
      } catch (Exception $e) {
         DB::rollback();
         $resp['message'] = $e->getMessage();
         $status_code = 421;
     }

     return response()->json($resp, $status_code);
   }

   public function massSubmitStatus(Request $request){
      $get_mf = $request->manifest_id;

      $arrayNextStatus = [];

      $idUpdate = [];

      $message = "No Data Received";

      if($get_mf){
         foreach ($get_mf as $key => $value) {
            $jo = DB::table('manifests')
            ->leftJoin('manifest_details', 'manifest_details.header_id','manifests.id')
            ->leftJoin('job_order_details', 'job_order_details.id','manifest_details.job_order_detail_id')
            ->leftJoin('job_orders', 'job_orders.id','job_order_details.header_id')
            ->where('manifests.id' , $value)
            ->select('job_orders.service_type_id')
            ->first();

            if(in_array($jo->service_type_id, [1,2,3,4])){
               $status = [
                  0 => 2,     //Assigned To Driver
                  1 => 6,     //Muat Barang
                  2 => 9,     //Bongkar Muatan
                  3 => 10,    //Selesai Bongkar
                  4 => 0,     //Status tidak bisa terupdate
               ];
            }
            elseif(in_array($jo->service_type_id, [20,21,24])){
                  $status = [
                     1 => 2,     //Assigned To Driver
                     2 => 17,    //Muat Container
                     3 => 19,    //Bongkar Container
                     4 => 10,    //Selesai Bongkar
                     5 => 0,     //Status tidak bisa terupdate
               ];
            }
            elseif(in_array($jo->service_type_id, [22,27])){
                  $status = [
                     1 => 2,     //Assigned To Driver
                     2 => 17,    //Muat Container
                     3 => 9,     //Bongkar Muatan
                     4 => 10,    //Selesai Bongkar
                     5 => 0,     //Status tidak bisa terupdate
                  ];
            }
            elseif(in_array($jo->service_type_id, [23])){
                  $status = [
                     0 => 2,     //Assigned To Driver
                     1 => 17,    //Muat Container
                     2 => 9,     //Bongkar Muatan
                     3 => 19,    //Bongkar Container
                     4 => 10,    //Selesai Bongkar
                     5 => 0,     //Status tidak bisa terupdate
                  ];
            }
            elseif(in_array($jo->service_type_id, [28])){
                  $status = [
                     0 => 2,     //Assigned To Driver
                     1 => 17,    //Muat Container
                     2 => 6,     //Muat Barang
                     3 => 19,    //Bongkar Container
                     4 => 10,    //Selesai Bongkar
                     5 => 0,     //Status tidak bisa terupdate
                  ];
            }
            elseif(in_array($jo->service_type_id, [25,26])){
                  $status = [
                     0 => 2,     //Assigned To Driver
                     1 => 6,     //Muat Barang
                     2 => 19,    //Bongkar Container
                     3 => 10,    //Selesai Bongkar
                     4 => 0,     //Status tidak bisa terupdate
                  ];
            }else{
                  $status = [
                     0 => 2,     //Assigned To Driver
                     1 => 6,     //Muat Barang
                     2 => 9,     //Bongkar Muatan
                     3 => 10,    //Selesai Bongkar
                     4 => 17,    //Muat Container
                     5 => 19,    //Bongkar Container
                     6 => 0,     //Status tidak bisa terupdate
                  ];
            }
            
            $manifest = Manifest::find($value);
            $status_no = array_search($manifest->job_status_id, $status);
            $next_status = $status_no + 1;
            array_push($arrayNextStatus, $next_status);
         }

         $minIndex = min($arrayNextStatus);

         $keys = array_keys($arrayNextStatus, $minIndex);

         foreach ($keys as $key => $value) {
            $id = $get_mf[$value];
            $this->submitStatus($request, $id);
            $message = "OK";
            array_push($idUpdate, $id);
         }
      }

      return response()->json(['message' => $message, 'id' => $idUpdate]);
   }
}