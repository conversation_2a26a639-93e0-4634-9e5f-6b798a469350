<?php

namespace App\Exports\Marketing\Report;

use App\Model\Company;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class Report2Export implements FromView, WithStyles, ShouldAutoSize
{
    protected $month = [
        1 => 'JAN',
        2 => 'FEB',
        3 => 'MAR',
        4 => 'APR',
        5 => 'MEI',
        6 => 'JUN',
        7 => 'JUL',
        8 => 'AGS',
        9 => 'SEP',
        10 => 'OKT',
        11 => 'NOV',
        12 => 'DES',
    ];

    public function __construct($request) {

        $this->request = $request;
    }

    public function styles(Worksheet $sheet)
    {
        $cols = $sheet->getColumnDimensions();
        foreach($cols as $col){
            $col->setAutoSize(true);
        }
    }

    public function view(): View
    {
        $request = $this->request;
        
        $wr="1=1";
        if ($request->company_id) {
            $wr.=" AND company_id = $request->company_id";
            $company=Company::find($request->company_id);
        }
        if ($request->year) {
            $wr.=" AND YEAR(created_at) = '$request->year'";
        }
        $data=[];
        for ($i=1; $i <= 12; $i++) {
            $lead=DB::table('leads')
                ->whereRaw($wr." AND MONTH(created_at) = '$i'")
                ->select([
                    DB::raw("count(*) as `lead`"),
                    DB::raw("SUM(IF(step = 6,1,0)) as lead_failed"),
                ])
                ->first();

            $opportunity_inquery=DB::table('inqueries')
                                    ->whereRaw($wr." AND MONTH(created_at) = '$i'")
                                    ->select([
                                        DB::raw("SUM(IF(cancel_opportunity_by is null,1,0)) as opportunity_success"),
                                        DB::raw("SUM(IF(cancel_opportunity_by is not null,1,0)) as opportunity_failed"),
                                        DB::raw("SUM(IF(cancel_inquery_by is null,1,0)) as inquery_success"),
                                        DB::raw("SUM(IF(cancel_inquery_by is not null,1,0)) as inquery_failed"),
                                    ])
                                    ->first();

            $quot=DB::table('quotations')
                    ->whereRaw($wr." AND MONTH(created_at) = '$i'")
                    ->select([
                        DB::raw("count(*) as quotation"),
                        DB::raw("SUM(IF(status_approve = 5,1,0)) as quotation_failed"),
                    ])
                    ->first();

            $wo=DB::table('work_orders')
                    ->whereRaw($wr." AND MONTH(created_at) = '$i'")
                    ->select([
                        DB::raw("count(*) as wo"),
                    ])
                    ->first();

            $data[]=[
                'month' => $this->month[$i],
                'lead' => $lead,
                'opportunity_inquery' => $opportunity_inquery,
                'quotation' => $quot,
                'wo' => $wo,
            ];
        }

        $dts['item'] = $data;
        $dts['company'] = $company ?? null;
        $dts['year'] = $request->year ?? null;

        return view('export.marketing.report.report-2', [
            'data' => $dts
        ]);
    }
}
