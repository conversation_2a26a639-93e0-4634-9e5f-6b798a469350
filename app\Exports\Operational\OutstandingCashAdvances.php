<?php
namespace App\Exports\Operational;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class OutstandingCashAdvances implements FromCollection, WithHeadings, WithMapping, WithColumnFormatting, WithStyles
{
    protected $request;
    protected $rowNumber = 0;
    protected $totalAmount = 0; 
    protected $total_uang_keluar = 0; 
    protected $total_realisasi = 0; 
    protected $total_selisih = 0; 

    function __construct(Request $request) {
        $this->request = $request;
    }

    /**
     * Ambil data dari controller
     */
    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        $data = $report_controller->getOutstandingKasbon($this->request);

        $this->totalAmount = $data->sum('total_cash_advance');
        $this->total_uang_keluar = $data->sum('total_uang_keluar');
        $this->total_realisasi = $data->sum('total_realisasi');
        $this->total_selisih = $data->sum('selisih');

        $totalRow = new \stdClass();
        $totalRow->employee = 'TOTAL';
        $totalRow->date_transaction = null;
        $totalRow->no_wo = null;
        $totalRow->code = null;
        $totalRow->detail_advance = null;
        $totalRow->total_cash_advance = $this->totalAmount;
        $totalRow->total_uang_keluar = $this->total_uang_keluar;
        $totalRow->total_realisasi = $this->total_realisasi;
        $totalRow->total_selisih = $this->total_selisih;
        $totalRow->aging = null;
        $totalRow->wo_status = null;

        return $data->push($totalRow);
    }

    /**
     * Header kolom di Excel
     */
    public function headings(): array
    {
        return [
            'No',
            'Dibayarkan Kepada',
            'Tanggal Transfer',
            'No Work Order',
            'No Cash Advance',
            'Detail Advance',
            'Jumlah Total',
            'Jumlah Uang Keluar',
            'Jumlah Realisasi',
            'Jumlah Selisih',
            'Kurang/Lebih Bayar',
            'Sudah Dibayar/Dikembalikan',
            'Aging',
            'Status'
        ];
    }

    /**
     * Format data yang diekspor ke Excel
     */
    public function map($data): array
    {
        $this->rowNumber++;

        $statusWo = [
            1 => 'Proses',
            2 => 'Selesai',
            3 => 'Void',
            4 => 'Job Order',
            5 => 'Cash Advance',
            6 => 'LPJ',
            7 => 'Invoice',
            8 => 'Collection'
        ];

        if ($data->employee === 'TOTAL') {
            return [
                '',
                'TOTAL', 
                '', '', '', '',
                $data->total_cash_advance,
                $data->total_uang_keluar,
                $data->total_realisasi,
                $data->total_selisih,
                '', ''
            ];
        }

        return [
            $this->rowNumber,
            $data->employee, 
            $data->date_transaction ? date('d-m-Y', strtotime($data->date_transaction)) : 'N/A',
            $data->no_wo,
            $data->code,
            $data->detail_advance,
            $data->total_cash_advance,
            $data->total_uang_keluar,
            $data->total_realisasi,
            $data->selisih,
            $data->kurang_lebih_bayar,
            $data->status_paid,
            $data->aging,
            $statusWo[$data->wo_status] ?? '-'
        ];
    }

    /**
     * Format kolom tertentu (misalnya angka & tanggal)
     */
    public function columnFormats(): array
    {
        return [
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, 
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, 
            'I' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, 
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1, 
        ];
    }

    /**
     * Styling untuk header agar bold
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
