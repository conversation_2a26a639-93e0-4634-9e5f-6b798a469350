<?php
namespace App\Exports\Finance;
/*
    Date : 05-10-2022 By : <PERSON>izky
    Description : 13. Report - Finance Accounting
    Keterangan : Fungsi Export Excel untuk Biaya Selisih
*/

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
// use Illuminate\Contracts\View\View;
// use Maatwebsite\Excel\Concerns\FromView;

class BiayaSelisih implements FromCollection, WithHeadings, WithMapping
// class BiayaSelisih implements FromView
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    /**
    * @return \Illuminate\Support\Collection
    */

    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Export\PdfController;
        return $report_controller->cost_balance($this->request);
    }
    // public function __construct($data) {
    //     $this->data = collect($data);
    // }
    public function headings(): array
    {
        return [
            'Job Order/Manifest',
            'Jenis Biaya',
            'Keterangan',
            'Total Biaya',
            'Total Terbayar',
            'Total Selisih'
        ];
    }
    public function map($data): array
    {
        $selisih = number_format($data->total_price - $data->paid);

        return [
            $data->code ?? '-',
            $data->name ?? '-',
            $data->description ?? '-',
            number_format($data->total_price) ?? 0,
            number_format($data->paid) ?? 0,
            $selisih,
        ];
    }
    // public function view(): View
    // {
    //     $params = [];
    //     $params['costs'] = $this->data;
    //     return view('pdf.cost_balance', $params);
    // }
}
// Close Description : 13. Report - Finance Accounting
?>