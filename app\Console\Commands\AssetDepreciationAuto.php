<?php

namespace App\Console\Commands;

use App\Model\AccountDefault;
use App\Model\Asset;
use App\Model\AssetDepreciation;
use Illuminate\Console\Command;
use App\Model\CompanyNumbering;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Utils\TransactionCode;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class AssetDepreciationAuto extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'asset_depreciation:auto';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Insert Asset Depresiasi per Akhir Bulan';

  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct()
  {
    parent::__construct();
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
      $today = date('Y-m-d');
      $first_day_of_month = date('Y-m-01');
      $last_day_of_month = date('Y-m-t');

      if ($today != $last_day_of_month) {
        $this->info('Sekarang belum akhir bulan, Skip Auto Depresiasi!');
        return;
      }

      $account_default = AccountDefault::first();
      if (empty($account_default->saldo_awal)) {
          $this->error('Akun Saldo Awal belum diset!');
          return;
      }

      $assets = Asset::where('nilai_buku', '>', 0)->cursor();

      foreach ($assets as $asset) {
          $this->info("Memproses asset: {$asset->code} - {$asset->name}");
          
          // Cek apakah depresiasi bulan ini sudah ada untuk aset ini
          $alreadyDepreciated = AssetDepreciation::where('header_id', $asset->id)
              ->whereBetween('date_utility', [$first_day_of_month, $last_day_of_month])
              ->exists();

          if ($alreadyDepreciated) {
              $this->info("Depresiasi bulan ini sudah ada untuk asset AA {$asset->code}. Skip.");
              continue;
          }

          // Ambil bulan terakhir depresiasi, atau bulan mulai penyusutan
          $lastDep = AssetDepreciation::where('header_id', $asset->id)
              ->orderBy('date_utility', 'desc')
              ->first();

          $startDate = $lastDep 
              ? date('Y-m-01', strtotime('+1 month', strtotime(date('Y-m-01', strtotime($lastDep->date_utility)))))
              : date('Y-m-01', strtotime($asset->date_purchase));

          while ($startDate <= $first_day_of_month) {
              $dateUtility = date('Y-m-t', strtotime($startDate)); // akhir bulan untuk tanggal ini

              // Cek apakah sudah ada depresiasi bulan ini
              $exists = AssetDepreciation::where('header_id', $asset->id)
                  ->where('date_utility', $dateUtility)
                  ->exists();

              if ($exists) {
                  $this->info("Depresiasi sudah ada untuk {$asset->code} bulan " . date('F Y', strtotime($startDate)) . ". Skip.");
                  $startDate = date('Y-m-01', strtotime('+1 month', strtotime($startDate)));
                  continue;
              }

              DB::beginTransaction();
              try {
                  $sum_depreciation = AssetDepreciation::where('header_id', $asset->id)
                      ->where('date_utility', '<', $dateUtility)
                      ->sum('depreciation_cost');

                  $depreciation_cost = $asset->beban_bulan;

                  // Hitung umur tersisa
                  $umur_bulan = $asset->umur_ekonomis * 12;
                  $sudah_didepresiasi = AssetDepreciation::where('header_id', $asset->id)->count();
                  $sisa_bulan = $umur_bulan - $sudah_didepresiasi;

                  $is_last_month = $sisa_bulan <= 1;

                  if ($is_last_month) {
                      $depreciation_cost = round($asset->purchase_price - $sum_depreciation, 2);
                  }

                  if ($depreciation_cost <= 0) {
                      $this->warn("Nilai depresiasi 0 atau negatif untuk asset {$asset->code}, dilewati.");
                      DB::rollBack();
                      break;
                  }

                  $trx = new TransactionCode($asset->company_id, 'depresiasiAsset');
                  $trx->setCode();
                  $trx_code = $trx->getCode();

                  // Update Asset
                  $total_akumulasi = $sum_depreciation + $depreciation_cost;
                  $nilai_buku_baru = max(0, $asset->purchase_price - $total_akumulasi);

                  $asset->update([
                      'beban_akumulasi' => $total_akumulasi,
                      'nilai_buku' => $nilai_buku_baru,
                      'terhitung_tanggal' => $dateUtility
                  ]);

                  AssetDepreciation::create([
                      'header_id' => $asset->id,
                      'date_utility' => $dateUtility,
                      'code' => $trx_code,
                      'depreciation_cost' => $depreciation_cost
                  ]);

                  $journal = Journal::create([
                      'company_id' => $asset->company_id,
                      'type_transaction_id' => 47,
                      'date_transaction' => $dateUtility,
                      'created_by' => 1,
                      'code' => $trx_code,
                      'description' => 'Penyusutan Asset Tetap ' . $trx_code
                  ]);

                  JournalDetail::create([
                      'header_id' => $journal->id,
                      'account_id' => $asset->account_accumulation_id,
                      'debet' => 0,
                      'credit' => $depreciation_cost,
                      'description' => 'Penyusutan Asset - ' . $asset->code
                  ]);

                  JournalDetail::create([
                      'header_id' => $journal->id,
                      'account_id' => $asset->account_depreciation_id,
                      'debet' => $depreciation_cost,
                      'credit' => 0,
                      'description' => 'Penyusutan Asset - ' . $asset->code
                  ]);

                  DB::commit();
                  $this->info("Depresiasi berhasil untuk {$asset->code} bulan " . date('F Y', strtotime($startDate)));

              } catch (\Exception $e) {
                  DB::rollBack();
                  Log::error("Gagal depresiasi asset {$asset->code} bulan " . date('F Y', strtotime($startDate)) . ": " . $e->getMessage());
                  $this->error("Gagal proses depresiasi {$asset->code}");
                  break; // Hentikan untuk asset ini agar tidak terus error
              }

              // Lanjut ke bulan berikutnya
              $startDate = date('Y-m-01', strtotime('+1 month', strtotime($startDate)));
          }
      }

      $this->info("Semua depresiasi selesai diproses.");
  }

}
