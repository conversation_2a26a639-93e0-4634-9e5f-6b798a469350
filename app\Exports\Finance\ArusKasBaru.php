<?php

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ArusKasBaru implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    protected $data;

    public function __construct($data) {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];

        $rows[] = [env('PROJECT'), ''];
        $rows[] = ['ARUS KAS BARU', ''];
        $rows[] = [
            'Cabang: ' . ($this->data['company']->name ?? 'Semua Cabang'),
            'Periode: ' . ($this->data['start'] ?? '') . ' s/d ' . ($this->data['end'] ?? '')
        ];
        $rows[] = ['', ''];
        $rows[] = ['Akun & Kategori', 'Amount'];

        foreach ($this->data['data'] as $item) {
            $amount = '';
            if (!$item->is_header) {
                $amount = number_format($item->amount, 0, ',', '.');
            }
            $rows[] = [$item->name, $amount];
        }

        return $rows;
    }

    public function headings(): array
    {
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:B1')->getFont()->setBold(true);
        $sheet->getStyle('A1:B1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A2:B2')->getFont()->setBold(true);
        $sheet->getStyle('A2:B2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A5:B5')->getFont()->setBold(true);
        $sheet->getStyle('B5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $dataStartRow = 6;
        $dataEndRow = $dataStartRow + count($this->data['data']) - 1;

        $sheet->getStyle('B' . $dataStartRow . ':B' . $dataEndRow)
              ->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        foreach ($this->data['data'] as $index => $item) {
            $rowNum = $dataStartRow + $index;
            if ($item->is_bold) {
                $sheet->getStyle('A' . $rowNum . ':B' . $rowNum)->getFont()->setBold(true);
            }
        }

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 50,
            'B' => 20,
        ];
    }
}
