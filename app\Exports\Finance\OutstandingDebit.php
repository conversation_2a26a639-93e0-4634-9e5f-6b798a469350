<?php

namespace App\Exports\Finance;

use App\Invoice;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\{FromView,FromCollection,WithHeadings,WithMapping, WithStyles, ShouldAutoSize};
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;

class OutstandingDebit implements FromView, WithStyles, ShouldAutoSize
{
    public function __construct($data) {
        $this->data = $data;
    }

    public function view(): View
    {
        $params = [];
        $params['columns'] = $this->data;
        return view('report/finance/outsanding_debit', $params);
    }

    public function styles(Worksheet $sheet)
    {

        $sheet->getStyle('A1:'.$sheet->getHighestColumn().'1')->getFont()->setBold(true);
        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . $sheet->getHighestRow())
        ->getBorders()
        ->getAllBorders()
        ->setBorderStyle(Border::BORDER_THIN)
        ->setColor(new Color('000000'));

        $styleArray = [
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . 1)
        ->applyFromArray($styleArray);

        foreach (self::excelColumnRange('A', 'ZZ') as $value) {
            $sheet->getColumnDimension($value)
            ->setAutoSize(true);
        }
    }

    private function excelColumnRange($lower, $upper) {
        ++$upper;
        for ($i = $lower; $i !== $upper; ++$i) {
            yield $i;
        }
    }
}
