<?php

namespace App\Http\Controllers\Api\v5;

use App\Abstracts\Contact\ContactLocation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Operational\V2\DeliveryOrderDriver;
use App\Abstracts\Operational\DeliveryOrderDriverDocument;
use App\Abstracts\Operational\Manifest;
use App\Abstracts\Operational\ManifestDetail;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Setting\JobStatus;
use App\Abstracts\Vehicle\VehicleDriver;
use App\Abstracts\JobOrder;
use App\Abstracts\Operational\DeliveryOrderDriver as OperationalDeliveryOrderDriver;
use App\Model\JobOrder as ModelJobOrder;
use App\Model\DeliveryManifest;
use App\Model\DeliveryOrderDriver as SJ;
use Exception;
use Illuminate\Support\Facades\DB;

class DeliveryOrderController extends Controller
{

    /*
      Date : 09-07-2021
      Description : Login
      Developer : Didin
      Status : Create
    */
    public function store(Request $request)
    {
        $resp = [];
        $resp['message'] = 'OK';
        $statusCode = 200;
        DB::beginTransaction();
        try {
            $params = $request->all();
            $params['contact_id'] = auth()->user()->contact_id;
            ContactLocation::store($params);
            DB::commit();
        } catch(\Exception $e) {
            DB::rollback();
            $statusCode = 421;
            $resp['message'] = $e->getMessage();
        }

        return response()->json($resp, $statusCode);
    }

    public static function index(Request $request) {
        $params = [];
        $user = auth()->user();
        $params['driver_id'] = $user->contact_id;
        $vehicle = VehicleDriver::showVehicleByDriver($user->contact_id);
        if($vehicle) {
            $params['vehicle_id'] = $vehicle->id;
        }
        if($request['start_date']){
            $params['start_date'] = $request['start_date'];
        }
        if($request['end_date']){
            $params['end_date'] = $request['end_date'];
        }
        $dt = DeliveryOrderDriver::query($params);
        $dt = $dt->select(
            'delivery_order_drivers.id',
            'delivery_order_drivers.pick_date',
            'delivery_order_drivers.code',
            DB::raw('"internal" AS shipping_by'),
            'delivery_order_drivers.created_at',
            'routes.name AS route',
            'vehicles.nopol AS police_no',
            'driver.name AS driver',
            'delivery_order_drivers.job_status_id AS status_id',
            'job_statuses.name AS status_name'
        )->orderBy('delivery_order_drivers.created_at', 'desc')->groupBy('delivery_order_drivers.id');

        $r = $dt->paginate(10);

        return response()->json($r);
    }

    public static function show($id) {
        $resp = [];
        $resp['message'] = 'OK';
        $data = DeliveryOrderDriver::show($id);
        $detail = self::indexManifest($id, false);

        $resp['data'] = $data;
        if($resp['data']){
            $resp['data']->details = $detail;
        }

        return response()->json($resp);
    }

    /**
     * Description: Menampilkan list lokasi pengiriman
     * Developer: Hendra
     * Status: Create
     */
    public static function listLocation($id)
    {
        // $dt = OperationalDeliveryOrderDriver::listLocation($id);
        $dt = OperationalDeliveryOrderDriver::getDeliveryRoute($id);
        return response()->json($dt);
    }

    /**
     * Description: Menampilkan list JO yg ada di lokasi tersebut
     * Developer: Hendra
     * Status: Create
     */
    public static function detailLocation(Request $request, $id)
    {
        $type = $request->location_type;
        $locationId = $request->location_id;
        if(!in_array($type, ['contact', 'depo', 'dock'])){
            throw new Exception('Tipe lokasi tidak valid!');
        };
        $dt = OperationalDeliveryOrderDriver::detailLocation($id, $type, $locationId);

        return response()->json($dt);
    }

    /**
     * Date : 26 Nov 2021
     * Description : Menampilkan list item manifest (detail DO)
     * Developer : Hendra
     * Status : Edit
     */
    public static function indexManifest($id, $respJson = true, $params = []) {
        DeliveryOrderDriver::validate($id);
        $resp = [];
        $resp['message'] = 'OK';
        $deliveryOrder = ManifestDetail::query([
            'delivery_order_id' => $id,
            'contact_id' => $params['contact_id'] ?? null,
            'depo_id' => $params['depo_id'] ?? null,
            'dock_id' => $params['dock_id'] ?? null,
            'type' => $params['type'] ?? null,
        ]);

        $deliveryOrder = $deliveryOrder->select(
            "manifest_details.id AS manifest_detail_id",
            "manifest_details.job_status_id AS manifest_detail_job_status_id",
            "job_statuses.name AS manifest_detail_job_status_name",
            "job_statuses.type AS manifest_detail_job_status_type",
            "job_statuses.urut AS manifest_detail_job_status_urut",
            "job_statuses.urut_full AS manifest_detail_job_status_urut_full",
            "job_orders.id AS job_order_id",
            "job_orders.code AS code",
            "manifests.id AS manifest_id",
            "job_order_details.item_name AS commodity_name",
            'customers.name AS receiver_name',
            DB::raw('true AS is_approved'),
            'manifest_details.requested_qty as requested_qty',
            'manifest_details.discharged_qty as discharged_qty',
            'manifest_details.transported as transported_qty',
        );

        $deliveryOrder = DB::query()->fromSub($deliveryOrder, "delivery_orders");
        $deliveryOrder = $deliveryOrder->select(
            "manifest_id",
            DB::raw("JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'manifest_detail_id', manifest_detail_id,
                        'manifest_detail_job_status_id', manifest_detail_job_status_id,
                        'manifest_detail_job_status_name', manifest_detail_job_status_name,
                        'manifest_detail_job_status_type', manifest_detail_job_status_type,
                        'manifest_detail_job_status_urut', manifest_detail_job_status_urut,
                        'manifest_detail_job_status_urut_full', manifest_detail_job_status_urut_full,
                        'code', code,
                        'job_order_id', job_order_id,
                        'manifest_id', manifest_id,
                        'commodity_name', commodity_name,
                        'receiver_name', receiver_name,
                        'plan', requested_qty,
                        'load', transported_qty,
                        'discharge', discharged_qty
                    )
            ) AS items")
        );
        $deliveryOrder = $deliveryOrder->groupBy('manifest_id');


        $dt = DB::table('manifests')
                ->leftJoin('job_statuses', 'job_statuses.id' , 'manifests.job_status_id');
        $dt = $dt->joinSub($deliveryOrder, "delivery_orders", function ($query){
            $query->on("delivery_orders.manifest_id", 'manifests.id');
        });
        $dt = $dt->select(
            'manifests.id AS manifest_id',
            'manifests.code AS manifest_code',
            'job_statuses.name AS manifest_status',
            'job_statuses.id AS manifest_status_id',
            'delivery_orders.items'
        );
        $dt = $dt->get();
        $dt = $dt->map(function ($v) {
            $v->items = json_decode($v->items);
            return $v;
        });
        $data = $dt;
        $resp['data'] = $data;

        if(!$respJson){
            return $data;
        }
        return response()->json($resp);
    }

    public static function detailManifest(Request $request, $id, $jobOrderId)
    {
        $type = null;
        $type = self::getDeliveryTypeFromJobOrder($request, $id, $jobOrderId);
        // dd($type);
        $detail = DeliveryOrderDriver::showDetail($id, $jobOrderId, $type);

        return response()->json($detail);
    }

    public static function indexReceiver($id) {
        DeliveryOrderDriver::validate($id);
        $resp = [];
        $resp['message'] = 'OK';

        $dt = JobOrder::query(['delivery_order_id' => $id]);
        // $dt = $dt->groupBy('customers.id');
        $dt = $dt->select(
            "job_orders.id AS id",
            "customers.id AS receiver_id",
            "customers.name AS receiver_name",
            "job_orders.code AS code"
        );
        $dt = $dt->get();

        $data = $dt;
        $resp['data'] = $data;

        return response()->json($resp);
    }

    /*
      Date : 14-09-2020
      Description : Meng-update qty yang terangkut
      Developer : Didin
      Status : Create
    */
    public static function loadItem(Request $request, $id, $manifest_detail_id) {
        DeliveryOrderDriver::validate($id);
        $request->validate([
            'qty' => 'required|numeric'
        ], [
            'qty.required' => 'Qty wajib diisi',
            'qty.numeric' => 'Qty harus berupa angka',
        ]);

        $resp = [];
        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;

        if($request->qty <= 0){
            $resp['message'] = 'Qty harus lebih dari 0';
            $resp['data'] = null;
            $status_code = 421;
            return response()->json($resp, $status_code);
        }

        DB::beginTransaction();
        try {
            ManifestDetail::updateTransportedQty($manifest_detail_id, $request->qty);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    /*
      Date : 14-09-2020
      Description : Meng-update qty yang dibongkar
      Developer : Didin
      Status : Create
    */
    public static function dischargeItem(Request $request, $id, $manifest_detail_id) {
        DeliveryOrderDriver::validate($id);
        $resp = [];
        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;

        $request->validate([
            'qty' => 'required|numeric'
        ], [
            'qty.required' => 'Qty wajib diisi',
            'qty.numeric' => 'Qty harus berupa angka',
        ]);

        if($request->qty <= 0){
            $resp['message'] = 'Qty harus lebih dari 0';
            $resp['data'] = null;
            $status_code = 421;
            return response()->json($resp, $status_code);
        }
        DB::beginTransaction();
        try {
            ManifestDetail::updateDischargedQty($manifest_detail_id, $request->qty);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    /*
      Date : 14-09-2020
      Description : Meng-update qty yang dibongkar
      Developer : Didin
      Status : Create
    */
    public static function submitStatus(Request $request, $id) {
        DeliveryOrderDriver::validate($id);
        $resp = [];
        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;
        
        DB::beginTransaction();
        try {
            //get service type berdasarkan jo yang diangkut
            $jo = DB::table('delivery_order_drivers')
                        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', 'delivery_order_drivers.id')
                        ->leftJoin('manifests', 'manifests.id','delivery_manifests.manifest_id')
                        ->leftJoin('manifest_details', 'manifest_details.header_id','manifests.id')
                        ->leftJoin('job_order_details', 'job_order_details.id','manifest_details.job_order_detail_id')
                        ->leftJoin('job_orders', 'job_orders.id','job_order_details.header_id')
                        ->where('delivery_order_drivers.id', $id)
                        ->select('job_orders.service_type_id')
                        ->first();


            // if(in_array($jo->service_type_id, [1,2,20,21,22,23,24,25,26,27,28])){
            //     $status = [
            //         0 => 2,     //Assigned To Driver
            //         1 => 3,     //Terima Job
            //         2 => 4,     //Pengambilan
            //         3 => 5,     //Tiba dilokasi pengambilan
            //         4 => 7,     //Berangkat
            //         5 => 8,     //Tiba dilokasi
            //         6 => 11,    //Selesai
            //         7 => 15,    //Menuju Depo/Dock Awal
            //         8 => 16,    //Tiba di Depo/Dock Awal
            //         9 => 18,    //Menuju Depo/Dock Tujuan
            //         10 => 20,   //Tiba di Depo/Dock Tujuan
            //         11 => 0,    //Status tidak bisa terupdate
            //     ];
            // }else{
            //     $status = [
            //         0 => 2,     //Assigned To Driver
            //         1 => 3,     //Terima Job
            //         2 => 4,     //Pengambilan
            //         3 => 5,     //Tiba dilokasi pengambilan
            //         4 => 7,     //Berangkat
            //         5 => 8,     //Tiba dilokasi
            //         6 => 11,    //Selesai
            //         7 => 0,     //Status tidak bisa terupdate
            //     ];
            // }

            if(in_array($jo->service_type_id, [1,2,3,4])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 4,     //Pengambilan
                    3 => 5,     //Tiba dilokasi pengambilan
                    4 => 7,     //Berangkat
                    5 => 8,     //Tiba dilokasi
                    6 => 11,    //Selesai
                    7 => 0,     //Status tidak bisa terupdate
                ];
            }
            elseif(in_array($jo->service_type_id, [20,21,24])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 15,    //Menuju Depo/Dock Awal
                    3 => 16,    //Tiba di Depo/Dock Awal
                    4 => 18,    //Menuju Depo/Dock Tujuan
                    5 => 20,   //Tiba di Depo/Dock Tujuan
                    6 => 11,    //Selesai
                    7 => 0,    //Status tidak bisa terupdate
                ];
            }
            elseif(in_array($jo->service_type_id, [22,27])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 15,    //Menuju Depo/Dock Awal
                    3 => 16,    //Tiba di Depo/Dock Awal
                    4 => 7,     //Berangkat
                    5 => 8,     //Tiba dilokasi
                    6 => 11,    //Selesai
                    7 => 0,    //Status tidak bisa terupdate
                ];
            }
            elseif(in_array($jo->service_type_id, [23])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 15,    //Menuju Depo/Dock Awal
                    3 => 16,    //Tiba di Depo/Dock Awal
                    4 => 7,     //Berangkat
                    5 => 8,     //Tiba dilokasi
                    6 => 18,    //Menuju Depo/Dock Tujuan
                    7 => 20,   //Tiba di Depo/Dock Tujuan
                    8 => 11,    //Selesai
                    9 => 0,     //Status tidak bisa terupdate
                ];
            }
            elseif(in_array($jo->service_type_id, [28])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 15,    //Menuju Depo/Dock Awal
                    3 => 16,    //Tiba di Depo/Dock Awal
                    4 => 4,     //Pengambilan
                    5 => 5,     //Tiba dilokasi pengambilan
                    6 => 18,    //Menuju Depo/Dock Tujuan
                    7 => 20,    //Tiba di Depo/Dock Tujuan
                    8 => 11,    //Selesai
                    9 => 0,     //Status tidak bisa terupdate
                ];
            }
            elseif(in_array($jo->service_type_id, [25,26])){
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 4,     //Pengambilan
                    3 => 5,     //Tiba dilokasi pengambilan
                    4 => 18,    //Menuju Depo/Dock Tujuan
                    5 => 20,    //Tiba di Depo/Dock Tujuan
                    6 => 11,    //Selesai
                    7 => 0,     //Status tidak bisa terupdate
                ];
            }else{
                $status = [
                    0 => 2,     //Assigned To Driver
                    1 => 3,     //Terima Job
                    2 => 4,     //Pengambilan
                    3 => 5,     //Tiba dilokasi pengambilan
                    4 => 7,     //Berangkat
                    5 => 8,     //Tiba dilokasi
                    6 => 11,    //Selesai
                    7 => 0,     //Status tidak bisa terupdate
                ];
            }

            $sj = SJ::find($id);
            $status_no = array_search($sj->job_status_id, $status);
            $next_status = $status_no + 1;

            // LAT LONG UPDATE WHEN TERIMA JOB
            if ($status[$next_status] == 3) {
                $sj->update([
                    'latitude_start' => $request->lat,
                    'longitude_start' => $request->long
                ]);
            }

            // LAT LONG UPDATE WHEN SELESAI JOB
            if ($status[$next_status] == 11) {
                $sj->update([
                    'latitude_end' => $request->lat,
                    'longitude_end' => $request->long
                ]);
            }

            //Validasi status manifest didalam do
            // if(in_array($status[$next_status], [7,11,18,20])){
            //     $validation_manifest = self::validationManifestStatus($sj->id, $status[$next_status]);
            //     if(!$validation_manifest['validation']){
            //         $resp['message'] = $validation_manifest['message'];
            //         $status_code = 500;

            //         return response()->json($resp, $status_code);
            //     }
            // }

            //Cek status apakah masih bisa diupdate ke next status
            if($status[$next_status] == 0){
                $resp['message'] = 'Status sudah selesai tidak bisa diupdate';
                $status_code = 500;

                return response()->json($resp, $status_code);
            }

            $sj->update([
                'job_status_id' => $status[$next_status],
                'delivery_order_status_id' => 2
            ]);

            $params = [];
            $params['created_by'] = auth()->id();
            $params['job_status_id'] = $status[$next_status];
            $params['delivery_order_driver_id'] = $id;
            DeliveryOrderStatusLog::store($params);

            $status_name = DB::table('job_statuses')->where('id', $status[$next_status])->select('name')->first();

            // $status = DeliveryOrderDriver::updateToNextStatus($id, auth()->id());

            $resp['data'] = ['current_status' => $status_name->name];
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    public static function validationManifestStatus($sj_id, $next_status)
    {

        $resp['message'] = null;
        $resp['validation'] = true;
        
        $manifest = DeliveryManifest::with('manifest:id,job_status_id')->where('delivery_order_driver_id', $sj_id)->get();
        //Validasi semua status manifest didalm do ini harus sudah muat barang baru bisa lanjut ke status berangkat
        if($next_status == 7){
            foreach($manifest as $item){
                if($item->manifest->job_status_id !== '6'){
                    // dd($item->manifest);
                    $resp['message'] = 'Status manifest masih ada yang belum Muat Barang';
                    $resp['validation'] = false;
                }
            }
        }

        if($next_status == 11){
            foreach($manifest as $item){
                if($item->manifest->job_status_id !== '10'){
                    $resp['message'] = 'Status manifest masih ada yang belum Selesai Bongkar';
                    $resp['validation'] = false;
                }
            }
        }

        if($next_status == 18){
            foreach($manifest as $item){
                if($item->manifest->job_status_id !== '17'){
                    $resp['message'] = 'Status manifest masih ada yang belum Muat Container';
                    $resp['validation'] = false;
                }
            }
        }

        if($next_status == 20){
            foreach($manifest as $item){
                if($item->manifest->job_status_id !== '19'){
                    $resp['message'] = 'Status manifest masih ada yang belum Bongkar Container';
                    $resp['validation'] = false;
                }
            }
        }

        return $resp;

    }

    public static function submitStatusDetail(Request $request, $id, $jobOrderId)
    {
        DeliveryOrderDriver::validate($id);
        DeliveryOrderDriver::validateJo($jobOrderId);
        $request->validate([
            // 'type' => 'required',
            'location_id' => 'required',
            'location_type' => 'required',
        ], [
            // 'type.required' => 'Tipe pengiriman wajib diisi',
            'location_type.required' => 'Tipe lokasi wajib diisi',
            'location_id.required' => 'ID lokasi wajib diisi',
        ]);
        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;
        DB::beginTransaction();
        try {
            $type = null;
            $type = self::getDeliveryTypeFromJobOrder($request, $id, $jobOrderId);
            if($type){
                $request->merge([
                    'type' => $type
                ]);
            }
            // dd($jo, $type);
            $status = DeliveryOrderDriver::updateToNextStatusDetail($id, auth()->id(), $jobOrderId, $request);
            $resp['data'] = ['current_status' => $status];
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    /**
     * Date : 9 Dec 2021
     * Description: Update bulk status by location
     */
    public static function submitStatusLocation(Request $request, $id)
    {
        DeliveryOrderDriver::validate($id);

        throw new Exception('This feature is under development', 500);

        $request->validate([
            'location_type' => 'required|in:contact,dock,depo',
            'location_id' => 'required',
            'type' => 'required|in:delivery,pickup,optional'
        ], [
            'location_type.required' => 'Tipe lokasi wajib diisi!',
            'location_type.in' => 'Tipe lokasi tidak valid!',
            'location_id.required' => 'Id lokasi wajib diisi!',
            'type.required' => 'Tipe wajib diisi!',
            'type.in' => 'Tipe tidak valid!',
        ]);

        $locationType = $request->location_type;
        $locationId = $request->location_id;
        $type = $request->type;

        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;
        DB::beginTransaction();
        try {
            $status = DeliveryOrderDriver::updateStatusByLocation($id, $locationType, $locationId, $type);
            $resp['data'] = ['current_status' => $status];
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    /**
     * Date : 26 Nov 2021
     * Description : Menampilkan list Proof of Delivery
     * Developer : Hendra
     * Status: Create
     */
    public static function getFiles($id, $jobOrderId = null)
    {
        $data = DeliveryOrderDriverDocument::index($id, $jobOrderId);
        $resp = [];
        $resp['message'] = 'OK';
        $resp['data'] = $data;

        return response()->json($resp, 200);
    }

    /*
      Date : 14-09-2020
      Description : Meng-update qty yang dibongkar
      Developer : Didin
      Status : Create
    */
    public static function storeFile(Request $request, $id, $jobOrderId = null) {
        $resp = [];
        $resp['message'] = 'OK';
        $resp['data'] = null;
        $status_code = 200;
        DB::beginTransaction();
        try {
            DeliveryOrderDriverDocument::store($id, $request->file('file'), $jobOrderId);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $resp['message'] = $e->getMessage();
            $status_code = 421;

        }

        return response()->json($resp, $status_code);
    }

    /*
      Date : 21-07-2021
      Description : Menampilkan jumlah surat jalan hari inii
      Developer : Didin
      Status : Create
    */
    public static function showSummary($driver_id = null, $vehicle_id = null) {
        $resp['message']  = 'OK';
        $user = auth()->user();
        $vehicle = VehicleDriver::showVehicleByDriver($user->contact_id);
        $dt = [];
        $dt['job_today'] = 0;
        $dt['job_this_month'] = 0;
        if($vehicle){
            $dt['job_today'] = DeliveryOrderDriver::amountToday($user->contact_id, $vehicle->id);
            $dt['job_this_month'] = DeliveryOrderDriver::amountThisMonth($user->contact_id, $vehicle->id);
        }
        $resp['data'] = $dt;

        return response()->json($resp);
    }

    public static function getDeliveryTypeFromJobOrder($request, $id, $jobOrderId)
    {
        if(!$request->location_type){
            throw new Exception('Tipe lokasi wajib diisi!', 500);
        }
        if(!$request->location_id){
            throw new Exception('ID lokasi wajib diisi!', 500);
        }
        $type = null;
        $jo = ModelJobOrder::find($jobOrderId);
        // dd($jo, $request->all());
        if($jo->service_type_id == 20){
            if($request->location_type == 'dock'){
                $type = 'pickup';
                if($request->location_id != $jo->dock_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'depo'){
                $type = 'delivery';
                if($request->location_id != $jo->depo_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 21){
            if($request->location_type == 'depo'){
                $type = 'pickup';
                if($request->location_id != $jo->depo_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'dock'){
                $type = 'delivery';
                if($request->location_id != $jo->dock_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 22){
            if($request->location_type == 'dock'){
                $type = 'pickup';
                if($request->location_id != $jo->dock_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'contact'){
                $type = 'delivery';
                if($request->location_id != $jo->receiver_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 23){
            if($request->location_type == 'dock'){
                $type = 'pickup';
                if($request->location_id != $jo->dock_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'contact'){
                $type = 'delivery';
                if($request->location_id != $jo->receiver_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'depo'){
                $type = 'optional';
                if($request->location_id != $jo->depo_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 24){
            if($request->location_type == 'depo'){
                if($jo->depo_awal_id != $jo->depo_tujuan_id){
                    if($request->location_id == $jo->depo_awal_id){
                        $type = 'pickup';
                    } else if($request->location_id == $jo->depo_tujuan_id){
                        $type = 'delivery';
                    }
                } else {
                    $showDetail = OperationalDeliveryOrderDriver::showDetail($id, $jobOrderId);
                    if($showDetail->manifest_detail_job_status_id <= 3){
                        $type = 'pickup';
                    } else {
                        $type = $showDetail->next_status_type;
                    }
                }
            }
        } else if($jo->service_type_id == 25){
            if($request->location_type == 'contact'){
                $type = 'pickup';
                if($request->location_id != $jo->sender_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'depo'){
                $type = 'delivery';
                if($request->location_id != $jo->depo_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 26){
            if($request->location_type == 'contact'){
                $type = 'pickup';
                if($request->location_id != $jo->sender_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'dock'){
                $type = 'delivery';
                if($request->location_id != $jo->dock_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 27){
            if($request->location_type == 'depo'){
                $type = 'pickup';
                if($request->location_id != $jo->depo_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'contact'){
                $type = 'delivery';
                if($request->location_id != $jo->receiver_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else if($jo->service_type_id == 28){
            if($request->location_type == 'depo'){
                $type = 'pickup';
                if($request->location_id != $jo->depo_awal_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'contact'){
                $type = 'delivery';
                if($request->location_id != $jo->sender_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            } else if($request->location_type == 'dock'){
                $type = 'optional';
                if($request->location_id != $jo->dock_tujuan_id){
                    throw new Exception('Kombinasi tipe lokasi dan id lokasi tidak valid!', 500);
                }
            }
        } else {
            if($request->location_type == 'contact'){
                if($jo->sender_id != $jo->receiver_id){
                    if($request->location_id == $jo->sender_id){
                        $type = 'pickup';
                    } else if($request->location_id == $jo->receiver_id){
                        $type = 'delivery';
                    } else {
                        throw new Exception('ID lokasi tidak valid!', 500);
                    }
                } else {
                    $showDetail = OperationalDeliveryOrderDriver::showDetail($id, $jobOrderId);
                    if($jo->sender_id != $request->location_id || $jo->receiver_id != $request->location_id){
                        throw new Exception('ID lokasi tidak valid!', 500);
                    }
                    if($showDetail->manifest_detail_job_status_id <= 3){
                        $type = 'pickup';
                    } else {
                        $type = $showDetail->next_status_type;
                    }
                }
            }
        }

        return $type;
    }
}
