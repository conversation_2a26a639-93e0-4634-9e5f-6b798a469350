<?php

namespace App\Abstracts;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use App\Utils\TransactionCode;
use App\Abstracts\Inventory\PurchaseOrderStatus;
use App\Abstracts\Journal;
use App\Abstracts\Contact;
use App\Model\User;
use App\Abstracts\Inventory\PurchaseRequestDetail;
use App\Abstracts\Sales\CustomerOrder;
use App\Model\PurchaseOrder as ModelPO;
use App\Model\PurchaseOrderDetail as ModelPOD;
use App\Model\PurchaseRequestDetail as ModelPRD;
use App\Model\CustomerOrderDetail as ModelCOD;
use App\Model\CustomerOrder as ModelCO;
use App\Model\SalesOrder;
use App\Model\Journal as J;
use App\Model\JournalDetail as JD;
use App\Model\Notification;
use App\Model\NotificationUser;
use App\Model\PurchaseOrder as ModelPurchaseOrder;
use DateTime;
use Illuminate\Support\Facades\DB;

class PurchaseOrder
{
    protected static $table = 'purchase_orders';

    /*
      Date : 29-08-2021
      Description : Mengambil parameter
      Developer : Didin
      Status : Create
    */
    public static function fetchFilter($args = []) {
        $params = [];
        $params['po_status'] = $args['po_status'] ?? null;
        $params['vehicle_maintenance_isnull'] = $args['vehicle_maintenance_isnull'] ?? null;
        $params['company_id'] = $args['company_id'] ?? null;
        $params['warehouse_id'] = $args['warehouse_id'] ?? null;
        $params['supplier_id'] = $args['supplier_id'] ?? null;
        $params['status'] = $args['status'] ?? null;
        $params['is_pallet'] = $args['is_pallet'] ?? null;
        $params['is_merchandise'] = $args['is_merchandise'] ?? null;
        $params['start_date'] = $args['start_date'] ?? null;
        $params['end_date'] = $args['end_date'] ?? null;
        $params['is_approved'] = $args['is_approved'] ?? null;
        $params['is_transit'] = $args['is_transit'] ?? null;
        $params['has_receipt'] = $args['has_receipt'] ?? null;
        $params['is_tire'] = $args['is_tire'] ?? null;

        return $params;
    }

    /*
      Date : 05-03-2021
      Description : Memvalidasi limit hutang supplier
      Developer : Didin
      Status : Create
    */
    public static function validasiLimitHutang($id) {
        $dt = self::show($id);
        $total_price = PurchaseOrderDetail::getTotalPrice($id);
        Contact::validasiLimitHutang($dt->supplier_id, $total_price);

    }

    /*
      Date : 29-08-2021
      Description : Meng-query purchase order
      Developer : Didin
      Status : Create
    */
    public static function query($params = []) {
        $request = self::fetchFilter($params);
        $wr="1=1";
        if ($request['po_status']) {
            $wr.=" and purchase_orders.po_status = " . $request['po_status'];
            if($request['po_status'] == 1) {
                $wr.=" or receipts.status = 1";
            }
        }
        if ($request['vehicle_maintenance_isnull']) {
          $wr.=" and purchase_orders.vehicle_maintenance_id IS NULL";
        }

        $purchase_request = DB::raw('(SELECT id, code, is_pallet FROM purchase_requests WHERE is_pallet = 0) AS purchase_requests');

        $item = DB::table('purchase_orders')
            ->leftJoin('purchase_order_statuses', 'purchase_order_statuses.id', self::$table . '.status')
            ->leftJoin('warehouses', 'warehouses.id', self::$table . '.warehouse_id')
            ->leftJoin('warehouse_receipts', 'warehouse_receipts.purchase_order_id', self::$table . '.id')
            ->join("contacts", "contacts.id", "purchase_orders.supplier_id")
            ->join("companies", "companies.id", "purchase_orders.company_id")
            // ->leftJoin("purchase_order_purchase_request","purchase_order_purchase_request.po_id","purchase_orders.id")
            // ->leftJoin($purchase_request,'purchase_requests.id','purchase_order_purchase_request.pr_id')
            ->leftJoin('receipts','receipts.po_id','purchase_orders.id')
            ->whereRaw($wr);

        $start_date = $request['start_date'];
        $start_date = $start_date != null ? new DateTime($start_date) : '';
        $end_date = $request['end_date'];
        $end_date = $end_date != null ? new DateTime($end_date) : '';

        if($start_date) {
            $item = $item->where('po_date', '>=', $start_date);
        }

        if($end_date) {
            $item = $item->where('po_date', '<=', $end_date);
        }

        if($request['has_receipt'] == 1){
            $item = $item->whereRaw('purchase_orders.id IN (SELECT warehouse_receipts.purchase_order_id FROM warehouse_receipts WHERE warehouse_receipts.purchase_order_id IS NOT NULL)');
        }

        if($request['warehouse_id']){
            $item = $item->where(self::$table . '.warehouse_id', $request['warehouse_id']);
        }

        $company_id = $request['company_id'];
        $item = $company_id != '' ? $item->where(DB::raw('purchase_orders.company_id'), $company_id) : $item;

        $supplier_id = $request['supplier_id'];
        $item = $supplier_id != '' ? $item->where(DB::raw('purchase_orders.supplier_id'), $supplier_id) : $item;

        $status = $request['status'];
        $item = $status != '' ? $item->where(self::$table . '.status', $status) : $item;


        if($request['is_pallet'] == 1) {
            $item = $item->whereRaw('purchase_orders.id IN (SELECT purchase_order_details.header_id FROM purchase_order_details JOIN items ON items.id = purchase_order_details.item_id JOIN categories ON categories.id = items.category_id LEFT JOIN categories AS parents ON parents.id = categories.parent_id WHERE categories.is_pallet = 1 OR parents.is_pallet = 1)');
        }

        if($request['is_tire'] == 1) {
            $item = $item->whereRaw('purchase_orders.id IN (SELECT purchase_order_details.header_id FROM purchase_order_details JOIN items ON items.id = purchase_order_details.item_id JOIN categories ON categories.id = items.category_id LEFT JOIN categories AS parents ON parents.id = categories.parent_id WHERE categories.is_tire = 1 OR parents.is_tire = 1)');
        }

        if($request['is_merchandise'] == 1) {
            $item = $item->whereRaw('purchase_orders.id IN (SELECT purchase_order_details.header_id FROM purchase_order_details JOIN items ON items.id = purchase_order_details.item_id WHERE items.is_merchandise = 1)');
        }

        if($request['is_approved'] == 1) {
            $approvedStatus = PurchaseOrderStatus::getApproved();
            $item = $item->where(self::$table . '.status', $approvedStatus);
        }

        if(isset($request['is_transit'])) {
            if($request['is_transit'] == 0){

                $item = $item->where(DB::raw('warehouses.is_transit'), 0);

            }

        }

        $item = $item->select(
            'purchase_orders.*',
            DB::raw('receipts.status AS receipt_status'),
            'warehouses.name AS warehouse_name',
            'warehouses.address AS warehouse_address',
            'contacts.name AS supplier_name',
            'contacts.address AS supplier_address',
            'contacts.phone AS supplier_phone',
            'contacts.contact_person AS supplier_cp',
            'contacts.contact_person_no AS supplier_cp_no',
            'companies.name AS company_name',
            'purchase_order_statuses.name  AS status_name',
            'purchase_order_statuses.slug  AS status_slug',
            // 'purchase_requests.code as pr_code'
            'warehouse_receipts.id as warehouse_receipt_id',
            DB::raw('(
              SELECT GROUP_CONCAT(DISTINCT(pr.code) SEPARATOR ", ") as pr_code FROM purchase_order_details pod
              LEFT JOIN purchase_request_details prd ON prd.id = pod.purchase_request_detail_id
              LEFT JOIN purchase_requests pr ON prd.header_id = pr.id
              WHERE pod.header_id = purchase_orders.id
              GROUP BY pod.header_id
            ) AS pr_code'),
            DB::raw('(
                SELECT pr.id as pr_id FROM purchase_order_details pod
                LEFT JOIN purchase_request_details prd ON prd.id = pod.purchase_request_detail_id
                LEFT JOIN purchase_requests pr ON prd.header_id = pr.id
                WHERE pod.header_id = purchase_orders.id
                GROUP BY pod.header_id
              ) AS pr_id')
        );

        $item->orderBy('purchase_orders.created_at','desc');

        // dd($item->toSql());

        return $item;
    }

    /*
      Date : 29-08-2021
      Description : Menangkap parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($params = []) {
        $args = [];
        $args['company_id'] = $params['company_id'] ?? null;
        $args['supplier_id'] = $params['supplier_id'] ?? null;
        $args['warehouse_id'] = $params['warehouse_id'] ?? null;
        $args['po_date'] = $params['po_date'] ?? null;
        $args['po_date'] = Carbon::parse($args['po_date'])->format('Y-m-d');
        $args['po_by'] = $params['po_by'] ?? auth()->id();
        $args['description'] = $params['description'] ?? null;
        $args['payment_type'] = $params['payment_type'] ?? 1;
        // $args['purchase_request_id'] = $params['purchase_request_id'] ?? null;
        $dt = $args;
        return $dt;
    }

    /*
      Date : 29-08-2021
      Description : Meng-nampilkan daftar order
      Developer : Didin
      Status : Create
    */
    public static function index($keyword = null) {
        $dt = self::query();
        if($keyword) {
            $dt = $dt->where('purchase_orders', 'like', "%$keyword%");
        }

        $dt = $dt->select('purchase_orders.id', 'purchase_orders.code');
        $dt = $dt->get();

        return $dt;
    }

    /*
      Date : 29-08-2021
      Description : Menyimpan purchase order
      Developer : Didin
      Status : Create
    */
    public static function store($params = []) {
        // dd($params);
        // die();
        $args = self::fetch($params);
        $detail = $params['detail'] ?? [];
        if($args['company_id']) {
            $code = new TransactionCode($args['company_id'], 'purchaseOrder');
            $code->setCode();
            $trx_code = $code->getCode();
            $args['code'] = $trx_code;
        } else {
            $trx_code = null;
        }
        $args['created_at'] = Carbon::now();
        if(isset($params['due_date'])){
            $args['due_date'] = $params['due_date'];

        }
        $args['status'] = PurchaseOrderStatus::getRequested();
        // dd($args);
        // die();
        $po_by = $args['po_by'];
        $users=DB::table('users')->where('id', $po_by)->first();
        $id = DB::table('purchase_orders')->insertGetId([
          'company_id' => $args['company_id'],
          'supplier_id' => $args['supplier_id'],
          'warehouse_id' => $args['warehouse_id'],
          'po_date' => $args['po_date'],
          'po_by' => $args['po_by'],
          'description' => $args['description'],
          'payment_type' => $args['payment_type'],
          'code' => $args['code'],
          'created_at' => $args['created_at'],
          'status' => $args['status'],
          'due_date' => $args['due_date'] ?? NULL,
        ]);
        $userList=DB::table('notification_type_users')
        ->leftJoin('users','users.id','=','notification_type_users.user_id')
        ->whereRaw("notification_type_users.notification_type_id = 32")
        ->select('users.id','users.is_admin','users.company_id')->get();
        $n=Notification::create([
            'notification_type_id' => 32,
            'name' => 'Purchase Order Baru telah Dibuat!',
            'description' => 'No. PO '.$trx_code.', Dibuat oleh '.$users->name.'',
            'slug' => str_random(6),
            'route' => 'inventory.purchase_order.show',
            'parameter' => json_encode(['id' => $id])
        ]);

        foreach ($userList as $un) {
            if ($un->is_admin) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            } else {
              if ($un->company_id==auth()->user()->company_id) {
                NotificationUser::create([
                  'notification_id' => $n->id,
                  'user_id' => $un->id
                ]);
              }
              //abaikan
            }
        }
        PurchaseOrderDetail::storeMultiple($detail, $id);

        return $id;
    }

    /*
      Date : 29-08-2021
      Description : Menyimpan purchase order
      Developer : Didin
      Status : Create
    */
    public static function update($params = [], $id) {
        $args = self::fetch($params);
        $po = self::show($id);
        if($po->company_id != ($args['company_id'] ?? null)) {
            if($args['company_id']) {
                $code = new TransactionCode($args['company_id'], 'purchaseOrder');
                $code->setCode();
                $trx_code = $code->getCode();
                $args['code'] = $trx_code;
            }
        }
        $id = DB::table('purchase_orders')
        ->whereId($id)
        ->update($args);

        return $id;
    }

    /*
      Date : 29-08-2021
      Description : Menampilkan detail sales order
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        $dt = self::query();
        $dt = $dt->where('purchase_orders.id', $id)
          ->first();

        return $dt;
    }

    /*
      Date : 15-10-2021
      Description : Menampilkan detail item receipt berdasarkan PO
      Developer : Hendra
      Status : Create
    */
    public static function showReceipt($id) {
        $dt = DB::table(self::$table);
        $dt = $dt->join('purchase_order_details as pod', 'pod.header_id', self::$table . '.id');
        $dt = $dt->join('warehouse_receipts', 'warehouse_receipts.purchase_order_id', self::$table . '.id');
        $dt = $dt->join('warehouse_receipt_details', 'warehouse_receipt_details.header_id', 'warehouse_receipts.id');
        $dt = $dt->join('items', 'items.id', 'warehouse_receipt_details.item_id');
        $dt = $dt->leftJoin('racks', 'racks.id', 'warehouse_receipt_details.rack_id');
        $dt = $dt->where(self::$table . '.id', $id);
        $dt = $dt->where('pod.receive', '>', 0);
        $dt = $dt->select(self::$table . '.*',
            'pod.qty as pod_qty',
                'warehouse_receipts.id as warehouse_receipt_id',
                'warehouse_receipts.code as warehouse_receipt_code',
                'warehouse_receipt_details.id as warehouse_receipt_detail_id',
                'warehouse_receipt_details.item_id',
                'warehouse_receipt_details.rack_id',
                'racks.code as rack_code',
                'items.code as item_code',
                DB::raw('IFNULL(warehouse_receipt_details.item_name, items.name) as item_name'),
                'warehouse_receipt_details.qty'
                );
        $dt = $dt->get();
        return $dt;
    }

    /*
      Date : 29-08-2021
      Description : Menghapus sales order
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validateWasApproved($id);
        PurchaseOrderDetail::clear($id);
        DB::table(self::$table)
        ->whereId($id)
        ->delete();
    }

    /*
      Date : 29-08-2021
      Description : Validasi data apakah sudah di-approve
      Developer : Didin
      Status : Create
    */
    public static function validateWasApproved($id) {
        $dt = self::show($id);
        if($dt->status == PurchaseOrderStatus::getApproved()) {
            throw new Exception('Data was approved');
        }
    }
    
    public static function validateWasApprovedByPimpinan($id) {
        $dt = self::show($id);
        if($dt->status != PurchaseOrderStatus::getApprovedPimpinan()) {
            throw new Exception("The PO wasn't approved by Pimpinan");
        }
    }

    /*
      Date : 16-10-2021
      Description : Validasi data apakah sudah di-cancel
      Developer : Hendra
      Status : Create
    */
    public static function validateWasCancelled($id) {
        $dt = self::show($id);
        if($dt->status == PurchaseOrderStatus::getCancelled()) {
            throw new Exception('Data was cancelled');
        }
    }

    /*
      Date : 16-10-2021
      Description : Validasi data apakah sudah di-cancel-partially
      Developer : Hendra
      Status : Create
    */
    public static function validateWasCancelledPartially($id) {
        $dt = self::show($id);
        if($dt->status == PurchaseOrderStatus::getCancelledPartially()) {
            throw new Exception('Data was partially cancelled');
        }
    }

    /*
      Date : 29-08-2021
      Description : Approve data
      Developer : Didin
      Status : Create
    */
    public static function approve($id) {
        self::validateWasApprovedByPimpinan($id);
        self::validateWasCancelled($id);
        self::validateWasCancelledPartially($id);
        self::validasiLimitHutang($id);
        DB::table(self::$table)->whereId($id)->update([
            'status' => PurchaseOrderStatus::getApproved()
        ]);
        $po = ModelPurchaseOrder::find($id);
        $userList=DB::table('notification_type_users')
        ->leftJoin('users','users.id','=','notification_type_users.user_id')
        ->whereRaw("notification_type_users.notification_type_id = 31")
        ->select('users.id','users.is_admin','users.company_id')->get();
        $n=Notification::create([
            'notification_type_id' => 31,
            'name' => 'Purchase Order Telah di Approve Pimpinan!',
            'description' => 'No. PO '.$po->code.', Telah di approve..',
            'slug' => str_random(6),
            'route' => 'inventory.purchase_order.show',
            'parameter' => json_encode(['id' => $id])
        ]);

        foreach ($userList as $un) {
            if ($un->is_admin) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            } else {
              if ($un->company_id==auth()->user()->company_id) {
                NotificationUser::create([
                  'notification_id' => $n->id,
                  'user_id' => $un->id
                ]);
              }
              //abaikan
            }
        }
        //Journal::setJournal(14, $id); //update [improve]  hide Jurnal Pembelian
        $detail= DB::table('purchase_order_details')->where('purchase_order_details.header_id',$id)
        ->leftJoin('items','items.id','purchase_order_details.item_id')
        ->leftJoin('categories','categories.id','items.category_id')
        ->where('categories.is_tire',1)
        ->select('purchase_order_details.id as id')
        ->get();
    }

    public static function approve_pimpinan($id) {
        self::validateWasCancelled($id);
        self::validateWasCancelledPartially($id);
        self::validasiLimitHutang($id);
        DB::table(self::$table)->whereId($id)->update([
            'status' => PurchaseOrderStatus::getApprovedPimpinan()
        ]);
        $po = ModelPurchaseOrder::find($id);
        $userList=DB::table('notification_type_users')
        ->leftJoin('users','users.id','=','notification_type_users.user_id')
        ->whereRaw("notification_type_users.notification_type_id = 30")
        ->select('users.id','users.is_admin','users.company_id')->get();
        $n=Notification::create([
            'notification_type_id' => 30,
            'name' => 'Purchase Order Telah di Approve Pimpinan!',
            'description' => 'No. PO '.$po->code.', Telah di approve oleh Pimpinan',
            'slug' => str_random(6),
            'route' => 'inventory.purchase_order.show',
            'parameter' => json_encode(['id' => $id])
        ]);

        foreach ($userList as $un) {
            if ($un->is_admin) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            } else {
              if ($un->company_id==auth()->user()->company_id) {
                NotificationUser::create([
                  'notification_id' => $n->id,
                  'user_id' => $un->id
                ]);
              }
              //abaikan
            }
        }
        //Journal::setJournal(14, $id); //update [improve]  hide Jurnal Pembelian
        $detail= DB::table('purchase_order_details')->where('purchase_order_details.header_id',$id)
        ->leftJoin('items','items.id','purchase_order_details.item_id')
        ->leftJoin('categories','categories.id','items.category_id')
        ->where('categories.is_tire',1)
        ->select('purchase_order_details.id as id')
        ->get();
        //  if(count($detail)>0){
        //      $type_data="ban";
        //  }
        //  else{
        //     $type_data="inventory";

        //  }
        //  self::journal_pembelian($id,$type_data);

    }

    /*
      Date : 16-10-2021
      Description : Reject/Cancel data
      Developer : Hendra
      Status : Create
    */
    public static function cancel($id) {
        self::validateWasCancelled($id);
        self::validateWasCancelledPartially($id);

        $po = PurchaseOrderDetail::index($id);

        $status = PurchaseOrderStatus::getCancelled();
        foreach($po as $i){
            if($i->received_qty < $i->qty && $i->received_qty > 0) {
                $status = PurchaseOrderStatus::getCancelledPartially();
                break;
            }
        }
        if($status){
            DB::table(self::$table)->whereId($id)->update([
                'status' => $status
            ]);
        }
    }

    /*
      Date : 02-06-2021
      Description : Menyelesaikan transaksi penerimaan barang
      Developer : Didin
      Status : Create
    */
    public static function finishReceipt($id) {
        $items = PurchaseOrderDetail::index($id);
        $finished = true;
        foreach ($items as $i) {
            if($i->received_qty < $i->qty) {
                $finished = false;
                break;
            }
        }
        if($finished) {

            DB::table(self::$table)->whereId($id)->update([
                'status' => PurchaseOrderStatus::getFinished()
            ]);

            //generate SO

            //get purchase order detail
            $po_detail = ModelPOD::where('header_id',$id)->get();
            foreach($po_detail as $po){
                $purchase_req= ModelPRD::where('id',$po->purchase_request_detail_id)->first();
                if($purchase_req){
                    $co_detail= ModelCOD::where('id',$purchase_req->customer_order_details_id)->first();
                    if($co_detail){
                        $co_receipt_detail= ModelCOD::where('header_id',$co_detail->header_id)->where('is_receipt',1)->get();
                        $all_co_detail= ModelCOD::where('header_id',$co_detail->header_id)->get();
                        if(count($co_receipt_detail) == count($all_co_detail)){
                            $statusApproved = DB::table('customer_order_statuses')->where('slug', 'approved')->first();

                            $co =ModelCO::find($co_detail->header_id);
                            $co->customer_order_status_id = $statusApproved->id;
                            $co->save();
                             //create so
                             $check_so= SalesOrder::where('customer_order_id',$co->id)->first();
                             if($check_so == null){
                                CustomerOrder::generateSo($co->id);

                             }
                        }
                    }
                }
            }

        }
    }

            /**
     * Date : 12-11-2021
     * Description : Merubah status ke outstanding
     * Developer : Atika
     * Status : Create
     */

    public static function outstanding($id){

        DB::table(self::$table)->whereId($id)->update([
            'status' => PurchaseOrderStatus::getOutstanding()
        ]);
    }

                /**
     * Date : 29-11-2021
     * Description : Journal Pembelian ban
     * Developer : Atika
     * Status : Create
     */
    public static function journal_pembelian($id,$type){
        $po= DB::table('purchase_orders')->where('id',$id)->first();
        if($type == "ban"){
            $type= DB::table('type_transactions')->where('slug','pembelian_ban')->first();
            $description= "Pembelian Ban ";
        }
        else{
            $type= DB::table('type_transactions')->where('slug','pembelian_inventory')->first();
            $description= "Pembelian Inventory ";

        }
        $account_default= DB::table('account_defaults')->first();

          //journal
      $j=J::create([
        'company_id' => $po->company_id,
        'type_transaction_id' => $type->id, //pembelian ban or inventory
        'date_transaction' => now(),
        'created_by' => auth()->id(),
        'code' => $po->code,
        'description' => $description.'Atas '.$po->code,
        'status' => 2
      ]);

      $detail=DB::table('purchase_order_details')->where('header_id',$id)->get();
      foreach($detail as $d){
          $item= DB::table('items')->where('items.id',$d->item_id)
          ->leftJoin('categories','categories.id','items.category_id')
          ->select('items.id as item_id','items.name as item_name','items.account_id as account_id','categories.is_tire as is_tire')
          ->first();

          $account_persediaan=$account_default->persediaan;

          if($item->account_id != null){
            $account_persediaan=$item->account_id;
          }

          if(!$account_persediaan){
            throw new Exception('Akun persediaan pada Default Setting Belum Dipilih!');
          }
              //persediaan
              JD::create([
                'header_id' => $j->id,
                'account_id' => $account_persediaan,
                'debet' => round($d->total),
                'description' => 'Persediaan item '.$item->item_name.' atas '.$po->code,
              ]);

              $company=DB::table('companies')->where('id',$po->company_id)->first();
              $account_cash= $company->cash_account_id;
              if($account_cash == NULL){
                  $account_cash= $account_default->account_cash;
              }
              if(!$account_cash){
                throw new Exception('Akun kas pada default account Belum Dipilih!');
              }
              //kas
              $search_kas=DB::table('journal_details')->where('header_id',$j->id)->where('account_id',$company->cash_account_id)->where('debet',0)->first();
              if($search_kas != null){
                  $updateKas= JD::find($search_kas->id);
                  $updateKas->credit= $updateKas->credit+round($d->total);
                  $updateKas->save();
              }
              else{
                JD::create([
                    'header_id' => $j->id,
                    'account_id' => $account_cash,
                    'credit' => round($d->total),
                    'description' => 'Kas atas '.$po->code,
                  ]);
              }


      }

    }

    function denominator($nilai)
    {
        $huruf = array("", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen");
        $tens = array("", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety");
    
        $temp = "";
    
        if ($nilai < 20) {
            $temp = $huruf[$nilai];
        } else if ($nilai < 100) {
            $temp = $tens[(int)($nilai / 10)] . " " . self::denominator($nilai % 10);
        } else if ($nilai < 1000) {
            $temp = $huruf[(int)($nilai / 100)] . " hundred " . self::denominator($nilai % 100);
        } else if ($nilai < 1000000) {
            $temp = self::denominator((int)($nilai / 1000)) . " thousand " . self::denominator($nilai % 1000);
        } else if ($nilai < 1000000000) {
            $temp = self::denominator((int)($nilai / 1000000)) . " million " . self::denominator($nilai % 1000000);
        } else if ($nilai < 1000000000000) {
            $temp = self::denominator((int)($nilai / 1000000000)) . " billion " . self::denominator($nilai % 1000000000);
        } else if ($nilai < 1000000000000000) {
            $temp = self::denominator((int)($nilai / 1000000000000)) . " trillion " . self::denominator($nilai % 1000000000000);
        }
    
        return trim($temp);
    }

    public static function approved_create_po($id) {
      self::validateWasApproved($id);
      self::validateWasCancelled($id);
      self::validateWasCancelledPartially($id);
      self::validasiLimitHutang($id);
      DB::table(self::$table)->whereId($id)->update([
          'status' => PurchaseOrderStatus::getApproved(),
          'approved_by' => auth()->id()
      ]);
      $po = ModelPurchaseOrder::find($id);
      $userList=DB::table('notification_type_users')
      ->leftJoin('users','users.id','=','notification_type_users.user_id')
      ->whereRaw("notification_type_users.notification_type_id = 31")
      ->select('users.id','users.is_admin','users.company_id')->get();
      $n=Notification::create([
          'notification_type_id' => 31,
          'name' => 'Purchase Order Telah di Approve Pimpinan!',
          'description' => 'No. PO '.$po->code.', Telah di approve..',
          'slug' => str_random(6),
          'route' => 'inventory.purchase_order.show',
          'parameter' => json_encode(['id' => $id])
      ]);

      foreach ($userList as $un) {
          if ($un->is_admin) {
            NotificationUser::create([
              'notification_id' => $n->id,
              'user_id' => $un->id
            ]);
          } else {
            if ($un->company_id==auth()->user()->company_id) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            }
            //abaikan
          }
      }
      //Journal::setJournal(14, $id); //update [improve]  hide Jurnal Pembelian
      $detail= DB::table('purchase_order_details')->where('purchase_order_details.header_id',$id)
      ->leftJoin('items','items.id','purchase_order_details.item_id')
      ->leftJoin('categories','categories.id','items.category_id')
      ->where('categories.is_tire',1)
      ->select('purchase_order_details.id as id')
      ->get();
  }
    


}
