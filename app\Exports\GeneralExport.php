<?php
/*
usage : 
return Excel::download(new GeneralExport($request,$collections,$heading{keys:value})," laporan_rekap.xlsx");
*/
namespace App\Exports;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\{FromCollection,WithHeadings,WithMapping, WithStyles, ShouldAutoSize};
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;

class GeneralExport implements FromCollection, WithHeadings, WithMapping,WithStyles, ShouldAutoSize
{
    protected $request;
    protected $head;//array
    protected $collection;//array / object
    protected $arrayKeys;
    function __construct(Request $request,$colection=[],$head=null) {
      $this->request = $request;
      $this->head = $head;
      $this->collection=$colection;
      if (count($this->collection) > 0) {
          $this->arrayKeys=array_keys((array)$this->collection[0]);
        } else {
          $this->arrayKeys=array_keys((array)$this->collection);
      }
    }
    public function collection()
    {
        return ($this->collection instanceof \Illuminate\Database\Eloquent\Collection?$this->collection:collect($this->collection));
    }
    public function headings(): array
    {
        return !empty($this->head)?array_values($this->head):$this->arrayKeys;
    }
    public function map($data): array
    {
        if(!empty($this->head)){
            foreach(array_keys($this->head) as $h){
                $ar[]=is_object($data)?$data->$h??'-':(is_array($data)?$data[$h]??'-':'-');
            }
        }else{
            foreach($this->arrayKeys as $h){
                $ar[]=is_object($data)?$data->$h??'-':(is_array($data)?$data[$h]??'-':'-');
            }
        }
        return $ar;
    }

    public function styles(Worksheet $sheet)
    {

        $sheet->getStyle('A1:'.$sheet->getHighestColumn().'1')->getFont()->setBold(true);
        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . $sheet->getHighestRow())
        ->getBorders()
        ->getAllBorders()
        ->setBorderStyle(Border::BORDER_THIN)
        ->setColor(new Color('000000'));

        $styleArray = [
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet
        ->getStyle('A1:'. $sheet->getHighestColumn() . 1)
        ->applyFromArray($styleArray);

        foreach (self::excelColumnRange('A', 'ZZ') as $value) {
            $sheet->getColumnDimension($value)
            ->setAutoSize(true);
        }
    }

    private function excelColumnRange($lower, $upper) {
        ++$upper;
        for ($i = $lower; $i !== $upper; ++$i) {
            yield $i;
        }
    }
}
