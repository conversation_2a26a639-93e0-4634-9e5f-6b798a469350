<?php
namespace App\Http\Controllers\Api;
use App\Abstracts\AdditionalField;
use App\Abstracts\JobOrder as JO;
use App\Abstracts\Operational\DeliveryOrderDriver as DOD;
use App\Abstracts\Operational\ManifestDetail as MD;
use App\Abstracts\Operational\DeliveryOrderStatusLog;
use App\Abstracts\Operational\Invoice;
use App\Http\Controllers\Controller;
use App\Abstracts\Contact;
use App\Model\{City, Container, CustomerJobOrder, CustomerOrder, DeliveryOrderDriver, Invoice as ModelInvoice, InvoiceVendor, KpiLog, Manifest, Service, VoyageSchedule};
use Auth;
use Carbon\Carbon;
// use App\Model\CustomerJobOrder;
use Curl;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Response;
use Yajra\DataTables\Facades\DataTables;
class OperationalApiController extends Controller
{
    /**
     * Date : 21-01-2022
     * Description : Menambahkan fungsi generate_datatable() untuk menu Generate Biaya
     * Developer : Syahrul
     * Status : Create
     */
    public function generate_datatable(Request $request)
    {
        $item = DB::table('generate_costs as gc');
        $item = $item->leftJoin('companies as c', 'c.id', 'gc.company_id');
        $item = $item->leftJoin('cost_types as ct', 'ct.id', 'gc.cost_type_id');
        $item = $item->leftJoin('contacts as v', 'v.id', 'gc.vendor_id');
        $item = $item->leftJoin('contacts as d', 'd.id', 'gc.driver_id');
        $item = $item->leftJoin('contacts as h', 'h.id', 'gc.helper_id');
        $item = $item->leftJoin('vehicles as ve', 've.id', 'gc.vehicle_id');
        $item = $item->selectRaw('
        gc.*,
        c.name as company,
        ct.name as cost,
        v.name as vendor,
        d.name as driver,
        h.name as helper,
        ve.nopol as vehicle
        ');
        if ($request->company_id) {
            $item = $item->where('gc.company_id', $request->company_id);
        }
        $item = $item->orderBy('gc.updated_at', 'DESC');
        return DataTables::of($item)->toJson();
    }
    public function voyage_schedule_datatable(Request $request)
    {
        return DataTables::of(self::voyage_schedule_query($request))
            ->filterColumn('total', function ($query, $keyword) {
                $sql = "Y.total like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->make(true);
    }
    /*
    Date : 31-03-2020
    Description : Menampilkan daftar kontainer dalam format datatable
    Developer : Didin
    Status : Edit
     */
    public function container_datatable(Request $request)
    {
        return DataTables::of(self::container_query($request))->addIndexColumn()
            ->addColumn('action_choose', function ($item) {
                $html = "<button ng-click='chooseContainer($item->id,\"$item->container_no\")' class='btn btn-xs btn-success'>Pilih</button>";
                return $html;
            })
            ->editColumn('is_fcl', function ($item) {
                $stt = [
                    1 => 'FCL',
                    0 => 'LCL',
                ];
                return $stt[$item->is_fcl];
            })
            ->rawColumns(['action_choose'])
            ->make(true);
    }
    public function job_order_datatable(Request $request)
    {
        $wr = "1=1";
        if (isset($request->is_loco)) {
            $wr .= " AND job_orders.is_loco = " . $request->is_loco;
        }
        if (isset($request->is_invoiced)) {
            $wr .= " AND job_orders.is_invoiced = " . $request->is_invoiced;
        }
        if (isset($request->status_wo)) {
            $wr .= " AND work_orders.status IN ($request->status_wo)";
        }
        if (isset($request->is_cancel)) {
            $wr .= " AND job_orders.is_cancel = " . $request->is_cancel;
        }
        if (isset($request->kpi_status_name)) {
            $wr .= " AND kpi_statuses.name = '" . $request->kpi_status_name . "'";
        }
        if (isset($request->service_id)) {
            $wr .= " AND job_orders.service_id = " . $request->service_id;
        }
        if (isset($request->vessel_id)) {
            $wr .= " AND containers.vessel_id = " . $request->vessel_id;
        }
        if (isset($request->voyage_id)) {
            $wr .= " AND containers.voyage_schedule_id = " . $request->voyage_id;
        }
        if (isset($request->is_operational_done)) {
            $wr .= " AND job_orders.is_operational_done = " . $request->is_operational_done;
        }
        if (isset($request->kpi_id)) {
            $wr .= " AND job_orders.kpi_id = " . $request->kpi_id;
        }
        if (isset($request->collectible_id)) {
            $wr .= " AND job_orders.collectible_id = $request->collectible_id";
        }
        // if (isset($request->not_invoice)) {
        //   $wr.=" AND job_orders.invoice_id is null";
        // }
        if (isset($request->customer_id)) {
            $wr .= " AND job_orders.customer_id = " . $request->customer_id;
        }
        if (isset($request->customer_id_array)) {
            $arr = explode(",", $request->customer_id_array);
            $wr.=' AND job_orders.customer_id IN (';
            foreach ($arr as $key => $value) {
              if (empty($value)) {
                continue;
              }
              if (end($arr) == $value) {
                $wr.="'".$value."'";
              } else {
                $wr.="'".$value."'".',';
              }
            }
            $wr.=")";
        }
        if (isset($request->not_customer_id_array)) {
            $arr = explode(",", $request->not_customer_id_array);
            $wr.=' AND job_orders.customer_id NOT IN (';
            foreach ($arr as $key => $value) {
              if (empty($value)) {
                continue;
              }
              if (end($arr) == $value) {
                $wr.="'".$value."'";
              } else {
                $wr.="'".$value."'".',';
              }
            }
            $wr.=")";
        }
        $user = Auth::user();
        if (isset($request->is_handling)) {
            $wr .= " AND job_orders.is_handling = " . $request->is_handling;
        }
        if (isset($request->is_stuffing)) {
            $wr .= " AND job_orders.is_stuffing = " . $request->is_stuffing;
        }
        if (isset($request->is_warehouserent)) {
            $wr .= " AND job_orders.is_warehouserent = " . $request->is_warehouserent;
        }
        if (isset($request->is_packaging)) {
            $wr .= " AND job_orders.is_packaging = " . $request->is_packaging;
        }
        if ($user->is_admin == 0 && $user->contact != null && (isset($request->is_handling) || isset($request->is_stuffing) || isset($request->is_warehouserent) || isset($request->is_packaging))) {
            if ($user->contact->is_staff_gudang == 1) {
                $wr .= " AND kpi_statuses.is_done = 0";
            }
        }
        if (isset($request->service_not_in)) {
            foreach ($request->service_not_in as $key => $value) {
                $wr .= " AND job_orders.service_type_id != " . $value;
            }
        }
        if (isset($request->is_done)) {
            $wr .= " AND kpi_statuses.is_done = $request->is_done";
        }
        if ($request->company_id) {
            $wr .= " AND job_orders.company_id = $request->company_id";
        } else {
            if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
                $wr .= " AND job_orders.company_id = " . auth()->user()->company_id;
            }
        }
        if ($request->exclude_borongan) {
            $wr .= " and IF(job_orders.quotation_id is not null, quotations.bill_type=1, 1=1)";
        }
        if ($request->filled('start_date') || $request->filled('end_date')) {
            if (isset($request->start_date)) {
                $start = dateDB($request->start_date);
                if (isset($request->end_date)) {
                    $end = dateDB($request->end_date);
                } else {
                    $end = $start;
                }
            } else if (isset($request->end_date)) {
                $end = dateDB($request->end_date);
                $start = $end;
            }
            $wr .= " AND shipment_date BETWEEN '" . $start . "' AND '" . $end . "'";
        }
        if ($request->filled('created_at')) {
            if (isset($request->created_at)) {
                $created_at = dateDB($request->created_at);
            }
            $wr .= " AND job_orders.created_at = '" . $created_at ."'";
        }
        $item = DB::table('job_orders')
            ->leftJoin('companies', 'companies.id', 'job_orders.company_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('contacts AS receivers', 'receivers.id', 'job_orders.receiver_id')
            ->leftJoin('contacts AS senders', 'senders.id', 'job_orders.sender_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
            ->leftJoin('quotations', 'quotations.id', '=', 'job_orders.quotation_id')
            ->leftJoin('work_orders', 'work_order_id', '=', 'work_orders.id')
            ->leftJoin('manifests', 'manifests.job_order_id', '=', 'job_orders.id')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('vessels', 'vessels.id', 'containers.vessel_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->whereRaw($wr)
            ->select('job_orders.*', 
                'companies.name AS company_name', 
                'contacts.name AS customer_name', 
                'routes.name AS route_name', 
                'receivers.name AS receiver_name', 
                'senders.name AS sender_name', 
                'services.name AS service_name', 
                'service_types.name AS service_type_name', 
                'kpi_statuses.name AS kpi_status_name',
                DB::raw("if(manifests.container_id is null, '-', containers.container_no) as container_no"),
                DB::raw("if(manifests.container_id is null, '-', containers.seal_no) as seal_no"),
                DB::raw("if(manifests.container_id is null, '-', vessels.name) as new_vessel_name"),
                DB::raw("if(manifests.container_id is null, '-', voyage_schedules.voyage) as voyage_schedules_name"),
            );
        $item = $item->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)');
        if ($request->is_depo_service == 1) {
            $item = $item->whereIn('services.service_type_id', [7, 12, 13, 15]);
        }
        if (isset($request->show_invoice)) {
            if ($request->show_invoice == 0) {
                $item->where('job_orders.is_invoiced', 0);
            } else {
                $item = $item->join('invoice_job_order', 'job_orders.id', 'invoice_job_order.job_order_id')
                    ->join('invoices', 'invoices.id', 'invoice_job_order.invoice_id');
            }
        }
        $item->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM job_packets)');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('jobOrder', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(job_orders.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM job_orders) AS additional_job_orders";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_job_orders.id', 'job_orders.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_job_orders.' . $a]);
            }
        }
        return DataTables::of($item->groupBy('job_orders.id'))
            ->addColumn('action_customer', function ($item) {
                $html = "<a ui-sref='main.job_order.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                return $html;
            })
            ->addColumn('action_vendor', function ($item) use ($request) {
                if ($request->user()->hasRole('vendor.job_order.detail')) {
                    return "<a ui-sref='main.job_order.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                }
                return '';
            })
            ->addColumn('action_choose', function ($item) {
                $html = "<a ng-click='selectJO($item->id,\"$item->code\")' class='btn btn-xs btn-success'>Pilih</a>";
                return $html;
            })
            /*
                Date : 09-11-2022
                Description : Membawa value id, code dan kpi_status pada checkbox
                Developer : Sendy
                Status : Edit
            */
            ->addColumn('checklist', function ($item) {
                return "<div class='checkbox checkbox-primary checkbox-inline' ><input type='checkbox' ng-model='myCheckbox.val$item->id' ng-true-value='(\"$item->id,$item->code,$item->kpi_status_name\")' ng-false-value='0'><label for='tr-" . $item->id . "'></label></div>";
            })
            ->editColumn('no_bl', function ($item) {
                $str = "";
                $explode = explode(',', $item->no_bl);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->editColumn('aju_number', function ($item) {
                $str = "";
                $explode = explode(',', $item->aju_number);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->rawColumns(['checklist', 'action_choose', 'action_customer', 'no_bl', 'aju_number'])
            ->make(true);
    }
    public function invoice_manifest_datatable(Request $request)
    {
        $wr = "1=1";
        if (isset($request->is_invoiced)) {
            $wr .= " AND manifests.is_invoice = " . $request->is_invoiced;
        }
        if (isset($request->kpi_status_name)) {
            $wr .= " AND kpi_statuses.name = '" . $request->kpi_status_name . "'";
        }
        if (isset($request->service_id)) {
            $wr .= " AND job_orders.service_id = " . $request->service_id;
        }
        if (isset($request->is_operational_done)) {
            $wr .= " AND job_orders.is_operational_done = " . $request->is_operational_done;
        }
        if (isset($request->kpi_id)) {
            $wr .= " AND job_orders.kpi_id = " . $request->kpi_id;
        }
        if (isset($request->collectible_id)) {
            $wr .= " AND job_orders.collectible_id = $request->collectible_id";
        }
        if (isset($request->customer_id)) {
            $wr .= " AND job_orders.customer_id = " . $request->customer_id;
        }
        $user = Auth::user();
        if (isset($request->is_handling)) {
            $wr .= " AND job_orders.is_handling = " . $request->is_handling;
        }
        if (isset($request->is_stuffing)) {
            $wr .= " AND job_orders.is_stuffing = " . $request->is_stuffing;
        }
        if (isset($request->is_warehouserent)) {
            $wr .= " AND job_orders.is_warehouserent = " . $request->is_warehouserent;
        }
        if (isset($request->is_packaging)) {
            $wr .= " AND job_orders.is_packaging = " . $request->is_packaging;
        }
        if ($user->is_admin == 0 && $user->contact != null && (isset($request->is_handling) || isset($request->is_stuffing) || isset($request->is_warehouserent) || isset($request->is_packaging))) {
            if ($user->contact->is_staff_gudang == 1) {
                $wr .= " AND kpi_statuses.is_done = 0";
            }
        }
        if (isset($request->service_not_in)) {
            foreach ($request->service_not_in as $key => $value) {
                $wr .= " AND job_orders.service_type_id != " . $value;
            }
        }
        if (isset($request->is_done)) {
            $wr .= " AND kpi_statuses.is_done = $request->is_done";
        }
        if ($request->company_id) {
            $wr .= " AND job_orders.company_id = $request->company_id";
        } else {
            if (auth()->user()->is_admin == 0) {
                $wr .= " AND job_orders.company_id = " . auth()->user()->company_id;
            }
        }
        if ($request->exclude_borongan) {
            $wr .= " and IF(job_orders.quotation_id is not null, quotations.bill_type=1, 1=1)";
        }
        if ($request->filled('start_date') || $request->filled('end_date')) {
            if (isset($request->start_date)) {
                $start = dateDB($request->start_date);
                if (isset($request->end_date)) {
                    $end = dateDB($request->end_date);
                } else {
                    $end = $start;
                }
            } else if (isset($request->end_date)) {
                $end = dateDB($request->end_date);
                $start = $end;
            }
            $wr .= " AND shipment_date BETWEEN '" . $start . "' AND '" . $end . "'";
        }
        if ($request->manifest_list_append) {
            $wr .= " AND manifests.id not in ($request->manifest_list_append)";
        }
        $item = DB::table('job_orders')
            ->leftJoin('companies', 'companies.id', 'job_orders.company_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('contacts AS receivers', 'receivers.id', 'job_orders.receiver_id')
            ->leftJoin('contacts AS senders', 'senders.id', 'job_orders.sender_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
            ->leftJoin('quotations', 'quotations.id', '=', 'job_orders.quotation_id')
            ->leftJoin('work_orders', 'work_order_id', '=', 'work_orders.id')
            ->leftJoin('job_order_details', 'job_order_details.header_id', '=', 'job_orders.id')
            ->leftJoin('manifest_details', 'manifest_details.job_order_detail_id', '=', 'job_order_details.id')
            ->leftJoin('manifests', 'manifest_details.header_id', '=', 'manifests.id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', '=', 'manifests.id')
            ->leftJoin('delivery_order_drivers', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
            ->leftJoin('vehicles', 'vehicles.id', '=', 'delivery_order_drivers.vehicle_id')
            ->leftJoin('pieces', 'job_order_details.piece_id', '=', 'pieces.id')
            ->whereRaw($wr)
            ->select(
                'delivery_order_drivers.code as no_sj',
                'vehicles.nopol',
                'job_order_details.item_name',
                DB::raw(" IF (
                    job_order_details.imposition = 1,
                    job_order_details.volume,
                    IF
                        ( 
                            job_order_details.imposition = 2, job_order_details.weight, job_order_details.qty )
                        ) 
                AS sj_qty"),
                'pieces.name AS piece',
                DB::raw('GROUP_CONCAT(job_orders.no_po_customer SEPARATOR ",") AS no_wo_po'),
                DB::raw("SUM(job_orders.total_price) AS total_price_item"),
                DB::raw("COUNT(job_orders.id) AS total_jo"),
                'job_orders.*',
                'manifests.id AS manifest_id',
                'manifests.code AS manifest_code',
                'companies.name AS company_name',
                'contacts.name AS customer_name',
                'routes.name AS route_name',
                'receivers.name AS receiver_name',
                'senders.name AS sender_name',
                'services.name AS service_name',
                'service_types.name AS service_type_name',
                'kpi_statuses.name AS kpi_status_name'
            )
            ->groupBy('manifests.code');
        $item = $item->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)');
        if ($request->is_depo_service == 1) {
            $item = $item->whereIn('services.service_type_id', [7, 12, 13, 15]);
        }
        if (isset($request->show_invoice)) {
            if ($request->show_invoice == 0) {
                $item->where('job_orders.is_invoiced', 0);
            } else {
                $item = $item->join('invoice_job_order', 'job_orders.id', 'invoice_job_order.job_order_id')
                    ->join('invoices', 'invoices.id', 'invoice_job_order.invoice_id');
            }
        }
        $item->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM job_packets)');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('jobOrder', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(job_orders.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM job_orders) AS additional_job_orders";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_job_orders.id', 'job_orders.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_job_orders.' . $a]);
            }
        }
        // echo $item->toSql();exit;
        return DataTables::of($item)
            ->addColumn('action_customer', function ($item) {
                $html = "<a ui-sref='main.job_order.show({id:$item->manifest_id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                return $html;
            })
            ->addColumn('action_vendor', function ($item) use ($request) {
                if ($request->user()->hasRole('vendor.job_order.detail')) {
                    return "<a ui-sref='main.job_order.show({id:$item->manifest_id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                }
                return '';
            })
            ->addColumn('action_choose', function ($item) {
                $html = "<a ng-click='selectJO($item->manifest_id,\"$item->manifest_code\")' class='btn btn-xs btn-success'>Pilih</a>";
                return $html;
            })
            /*
                Date : 09-11-2022
                Description : Membawa value id, code dan kpi_status pada checkbox
                Developer : Sendy
                Status : Edit
            */
            ->addColumn('checklist', function ($item) {
                return "<div class='checkbox checkbox-primary checkbox-inline' ><input type='checkbox' ng-model='myCheckbox.val$item->manifest_id' ng-true-value='(\"$item->manifest_id,$item->manifest_code,$item->kpi_status_name\")' ng-false-value='0'><label for='tr-" . $item->manifest_id . "'></label></div>";
            })
            ->editColumn('no_bl', function ($item) {
                $str = "";
                $explode = explode(',', $item->no_bl);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->editColumn('aju_number', function ($item) {
                $str = "";
                $explode = explode(',', $item->aju_number);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->rawColumns(['checklist', 'action_choose', 'action_customer', 'no_bl', 'aju_number'])
            ->make(true);
    }
    /*
    Date : 10-07-2021
    Description : Menampilkan daftar job order detail datatable
    Developer : Hendra
    Status : Create
     */
    public function job_order_detail_datatable(Request $request)
    {
        $dt = DB::table('job_order_details')
            ->leftJoin('commodities', 'commodities.id', 'job_order_details.commodity_id')
            ->select(
                'job_order_details.id',
                'commodities.name AS commodity_name',
                'job_order_details.commodity_id',
                'job_order_details.price',
                'job_order_details.qty',
                'job_order_details.description',
                'job_order_details.total_price'
            );
        if ($request->filled('job_order_id')) {
            $dt = $dt->where('job_order_details.header_id', $request->job_order_id);
        }
        return DataTables::of($dt)
            ->make(true);
    }
    public function vendor_job_datatable(Request $request)
    {
        $manifest_details = DB::raw('(SELECT job_order_details.header_id AS job_order_id, manifest_details.header_id AS manifest_id FROM manifest_details JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id GROUP BY manifest_details.header_id) AS manifest_details');
        $manifest = DB::table('manifest_costs')
            ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'manifest_costs.vendor_job_status_id')
            ->join('contacts AS vendors', 'vendors.id', 'manifest_costs.vendor_id')
            ->join('manifests', 'manifests.id', 'manifest_costs.header_id')
            ->join($manifest_details, 'manifest_details.manifest_id', 'manifests.id')
            ->join('job_orders', 'job_orders.id', 'manifest_details.job_order_id')
            ->join('contacts', 'contacts.id', 'job_orders.customer_id')
            ->join('companies', 'companies.id', 'manifests.company_id')
            ->join('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
            ->selectRaw('manifest_costs.id, "Manifest" AS source_name, "manifest" AS source, companies.name AS company_name, contacts.name AS customer_name, manifests.code, cost_types.name AS cost_type_name, manifest_costs.qty, manifest_costs.price, manifest_costs.total_price, vendor_job_statuses.name AS vendor_job_status_name, vendor_job_statuses.id AS vendor_job_status_id, vendors.name AS vendor_name');
        if ($request->filled('customer_id')) {
            $manifest->where('job_orders.customer_id', $request->customer_id);
        }
        if ($request->filled('vendor_id')) {
            $manifest->where('manifest_costs.vendor_id', $request->vendor_id);
        }
        // [Improve] Penambahan pada select filter Menu - Vendor Job
        if ($request->filled('cost_type_id')) {
            $manifest->where('manifest_costs.cost_type_id', $request->cost_type_id);
        }
        if ($request->filled('vendor_job_status_id')) {
            $manifest->where('manifest_costs.vendor_job_status_id', $request->vendor_job_status_id);
        }
        // end [Improve] Penambahan pada select filter Menu - Vendor Job
        $item = DB::table('job_order_costs')
            ->join('job_orders', 'job_orders.id', 'job_order_costs.header_id')
            ->join('contacts AS vendors', 'vendors.id', 'job_order_costs.vendor_id')
            ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'job_order_costs.vendor_job_status_id')
            ->join('contacts', 'contacts.id', 'job_orders.customer_id')
            ->join('companies', 'companies.id', 'job_orders.company_id')
            ->join('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
            ->where('job_order_costs.status', '!=', 1)
            ->selectRaw('job_order_costs.id, "Job Order" AS source_name, "job_order" AS source, companies.name AS company_name, contacts.name AS customer_name, job_orders.code, cost_types.name AS cost_type_name, job_order_costs.qty, job_order_costs.price, job_order_costs.total_price, vendor_job_statuses.name AS vendor_job_status_name, vendor_job_statuses.id AS vendor_job_status_id, vendors.name AS vendor_name');
        if ($request->filled('customer_id')) {
            $item->where('job_orders.customer_id', $request->customer_id);
        }
        if ($request->filled('vendor_id')) {
            $item->where('job_order_costs.vendor_id', $request->vendor_id);
        }
        // [Improve] Penambahan pada select filter Menu - Vendor Job
        if ($request->filled('cost_type_id')) {
            $item->where('job_order_costs.cost_type_id', $request->cost_type_id);
        }
        if ($request->filled('vendor_job_status_id')) {
            $item->where('job_order_costs.vendor_job_status_id', $request->vendor_job_status_id);
        }
        // end [Improve] Penambahan pada select filter Menu - Vendor Job
        $item = $item->union($manifest);
        $item = $item->get()->sortByDesc('id');
        return DataTables::of($item)
            ->make(true);
    }
    public function transaction_approval_datatable(Request $request)
    {
        $manifest_details = DB::raw('(SELECT job_order_details.header_id AS job_order_id, manifest_details.header_id AS manifest_id FROM manifest_details JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id GROUP BY manifest_details.header_id) AS manifest_details');
        $manifest = DB::table('manifest_costs')
            ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'manifest_costs.vendor_job_status_id')
            ->leftJoin('contacts AS vendors', 'vendors.id', 'manifest_costs.vendor_id')
            ->join('manifests', 'manifests.id', 'manifest_costs.header_id')
            ->join($manifest_details, 'manifest_details.manifest_id', 'manifests.id')
            ->join('job_orders', 'job_orders.id', 'manifest_details.job_order_id')
            ->join('contacts', 'contacts.id', 'job_orders.customer_id')
            ->join('companies', 'companies.id', 'manifests.company_id')
            ->join('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
            ->selectRaw('
            manifest_costs.id, 
            manifest_costs.created_at, 
            "Manifest" AS source_name, 
            "manifest" AS source, 
            companies.name AS company_name, 
            contacts.name AS customer_name, 
            manifests.code, 
            cost_types.name AS cost_type_name, 
            manifest_costs.qty, 
            manifest_costs.price, 
            manifest_costs.total_price,
            manifest_costs.qty_real, 
            manifest_costs.price_real, 
            manifest_costs.total_real, 
            manifest_costs.status, 
            vendor_job_statuses.name AS vendor_job_status_name, 
            vendor_job_statuses.id AS vendor_job_status_id, 
            vendors.name AS vendor_name');
        if ($request->filled('customer_id')) {
            $manifest->where('job_orders.customer_id', $request->customer_id);
        }
        if ($request->filled('company_id')) {
            $manifest->where('manifest_costs.company_id', $request->company_id);
        }
        if ($request->filled('start_date') || $request->filled('end_date')) {
            $start_date = strtotime($request->start_date);
            $end_date = strtotime($request->end_date);
            $manifest->whereBetween(DB::raw('DATE(manifest_costs.created_at)'), [date('Y-m-d', $start_date), date('Y-m-d', $end_date)]);
        }
        if ($request->filled('is_internal')) {
            $manifest->where('manifest_costs.is_internal', $request->is_internal);
        }
        if ($request->filled('vendor_id')) {
            $manifest->where('manifest_costs.vendor_id', $request->vendor_id);
        }
        if ($request->filled('cost_type_id')) {
            $manifest->where('manifest_costs.cost_type_id', $request->cost_type_id);
        }
        if ($request->filled('vendor_job_status_id')) {
            $manifest->where('manifest_costs.vendor_job_status_id', $request->vendor_job_status_id);
        }
        $item = DB::table('job_order_costs')
            ->join('job_orders', 'job_orders.id', 'job_order_costs.header_id')
            ->leftJoin('contacts AS vendors', 'vendors.id', 'job_order_costs.vendor_id')
            ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'job_order_costs.vendor_job_status_id')
            ->join('contacts', 'contacts.id', 'job_orders.customer_id')
            ->join('companies', 'companies.id', 'job_orders.company_id')
            ->join('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
            ->where('job_order_costs.manifest_cost_id', null) // kondisi kecuali cost dari manifest
            ->selectRaw('job_order_costs.id, 
            job_order_costs.created_at, 
            "Job Order" AS source_name, 
            "job_order" AS source, 
            companies.name AS company_name, 
            contacts.name AS customer_name, 
            job_orders.code, 
            cost_types.name AS cost_type_name, 
            job_order_costs.qty, 
            job_order_costs.price, 
            job_order_costs.total_price,
            job_order_costs.qty AS qty_real, 
            job_order_costs.price AS price_real, 
            job_order_costs.total_price AS total_real, 
            job_order_costs.status, 
            vendor_job_statuses.name AS vendor_job_status_name, 
            vendor_job_statuses.id AS vendor_job_status_id, 
            vendors.name AS vendor_name');
        if ($request->filled('customer_id')) {
            $item->where('job_orders.customer_id', $request->customer_id);
        }
        if ($request->filled('company_id')) {
            $item->where('job_orders.company_id', $request->company_id);
        }
        if ($request->filled('start_date') || $request->filled('end_date')) {
            $start_date = strtotime($request->start_date);
            $end_date = strtotime($request->end_date);
            $item->whereBetween(DB::raw('DATE(job_order_costs.created_at)'), [date('Y-m-d', $start_date), date('Y-m-d', $end_date)]);
        }
        //[Start] filter apakah cost dari internal
        // if ($request->filled('is_internal')) {
        //     $get_driver = DB::table('manifest_costs')->where('manifest_costs.is_internal', 1)->get();
        //     foreach ($get_driver as $gd){
        //         $item->where('job_order_costs.manifest_cost_id', "!=", $gd->id);
        //     }
        // }
        if ($request->filled('vendor_id')) {
            $item->where('job_order_costs.vendor_id', $request->vendor_id);
        }
        if ($request->filled('cost_type_id')) {
            $item->where('job_order_costs.cost_type_id', $request->cost_type_id);
        }
        if ($request->filled('vendor_job_status_id')) {
            $item->where('job_order_costs.vendor_job_status_id', $request->vendor_job_status_id);
        }
        $item = $item->union($manifest);
        $item = $item->get()->sortByDesc('id');
        return DataTables::of($item)
            ->editColumn('vendor_job_status_name', function ($item) {
                if ($item->vendor_job_status_name == 'DRAFT') {
                    $item->vendor_job_status_name = '';
                } else {
                    $item->vendor_job_status_name = $item->vendor_job_status_name;
                }
                return $item->vendor_job_status_name;
            })
            ->make(true);
    }
    public function transaction_approval(Request $request, $source, $id)
    {
        $item = '';
        if ($source == 'manifest') {
            $manifest_details = DB::raw('(SELECT job_order_details.header_id AS job_order_id, manifest_details.header_id AS manifest_id FROM manifest_details JOIN job_order_details ON job_order_details.id = manifest_details.job_order_detail_id GROUP BY manifest_details.header_id) AS manifest_details');
            $item = DB::table('manifest_costs')
                ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'manifest_costs.vendor_job_status_id')
                ->leftJoin('contacts AS vendors', 'vendors.id', 'manifest_costs.vendor_id')
                ->leftJoin('users AS request', 'request.id', 'manifest_costs.create_by')
                ->leftJoin('users AS approve', 'approve.id', 'manifest_costs.approve_by')
                ->join('manifests', 'manifests.id', 'manifest_costs.header_id')
                ->join($manifest_details, 'manifest_details.manifest_id', 'manifests.id')
                ->join('job_orders', 'job_orders.id', 'manifest_details.job_order_id')
                ->join('contacts', 'contacts.id', 'job_orders.customer_id')
                ->join('companies', 'companies.id', 'manifests.company_id')
                ->join('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
                ->where('manifest_costs.id', $id)
                ->selectRaw('
                manifest_costs.id, 
                manifest_costs.created_at, 
                "Manifest" AS source_name, 
                "manifest" AS source, 
                companies.name AS company_name, 
                contacts.name AS customer_name, 
                manifests.code, 
                cost_types.name AS cost_type_name, 
                manifest_costs.qty, manifest_costs.price, 
                manifest_costs.total_price, 
                vendor_job_statuses.name AS vendor_job_status_name, 
                vendor_job_statuses.id AS vendor_job_status_id, 
                vendors.name AS vendor_name,
                manifest_costs.description,
                manifest_costs.updated_at,
                manifest_costs.status,
                approve.name AS approve_name,
                request.name AS request_name
            ')
                ->first();
        }
        if ($source == 'job_order') {
            $item = DB::table('job_order_costs')
                ->join('job_orders', 'job_orders.id', 'job_order_costs.header_id')
                ->leftJoin('contacts AS vendors', 'vendors.id', 'job_order_costs.vendor_id')
                ->leftJoin('users AS request', 'request.id', 'job_order_costs.create_by')
                ->leftJoin('users AS approve', 'approve.id', 'job_order_costs.approve_by')
                ->leftJoin('vendor_job_statuses', 'vendor_job_statuses.id', 'job_order_costs.vendor_job_status_id')
                ->join('contacts', 'contacts.id', 'job_orders.customer_id')
                ->join('companies', 'companies.id', 'job_orders.company_id')
                ->join('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
                ->where('job_order_costs.id', $id)
                ->selectRaw('
                job_order_costs.id, 
                job_order_costs.created_at, 
                "Job Order" AS source_name, 
                "job_order" AS source, 
                companies.name AS company_name, 
                contacts.name AS customer_name, 
                job_orders.code, 
                cost_types.name AS cost_type_name, 
                job_order_costs.qty, 
                job_order_costs.price, 
                job_order_costs.total_price, 
                vendor_job_statuses.name AS vendor_job_status_name, 
                vendor_job_statuses.id AS vendor_job_status_id, 
                vendors.name AS vendor_name,
                job_order_costs.description,
                job_order_costs.updated_at,
                job_order_costs.status,
                approve.name AS approve_name,
                request.name AS request_name
            ')
                ->first();
        }
        return Response::json($item, 200, [], JSON_NUMERIC_CHECK);
    }
    public function jo_datatable(Request $request)
    {
        // dd($request);
        $wr = "1=1";
        if (isset($request->kpi_status_name)) {
            $wr .= " AND kpi_statuses.name = '" . $request->kpi_status_name . "'";
        }
        if (isset($request->service_id)) {
            $wr .= " AND job_orders.service_id = " . $request->service_id;
        }
        if (isset($request->is_operational_done)) {
            $wr .= " AND job_orders.is_operational_done = " . $request->is_operational_done;
        }
        if (isset($request->kpi_id)) {
            $wr .= " AND job_orders.kpi_id = " . $request->kpi_id;
        }
        if (isset($request->collectible_id)) {
            $wr .= " AND job_orders.collectible_id = $request->collectible_id";
        }
        if ($request->not_invoice) {
            $wr .= " AND job_orders.invoice_id is null";
        }
        if ($request->is_invoice) {
            $wr .= " AND job_orders.invoice_id is not null";
        }
        if (isset($request->customer_id)) {
            $wr .= " AND job_orders.customer_id = " . $request->customer_id;
        }
        if (isset($request->is_handling)) {
            $wr .= " AND job_orders.is_handling = " . $request->is_handling;
        }
        if (isset($request->is_stuffing)) {
            $wr .= " AND job_orders.is_stuffing = " . $request->is_stuffing;
        }
        if (isset($request->is_warehouserent)) {
            $wr .= " AND job_orders.is_warehouserent = " . $request->is_warehouserent;
        }
        if (isset($request->is_packaging)) {
            $wr .= " AND job_orders.is_packaging = " . $request->is_packaging;
        }
        if (isset($request->service_not_in)) {
            foreach ($request->service_not_in as $key => $value) {
                $wr .= " AND job_orders.service_type_id != " . $value;
            }
        }
        if (isset($request->is_done)) {
            $wr .= " AND kpi_statuses.is_done = $request->is_done";
        }
        if ($request->company_id) {
            $wr .= " AND job_orders.company_id = $request->company_id";
        } else {
            if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
                $wr .= " AND job_orders.company_id = " . auth()->user()->company_id;
            }
        }
        if ($request->exclude_borongan) {
            $wr .= " and IF(job_orders.quotation_id is not null, quotations.bill_type=1, 1=1)";
        }
        if ($request->jo_list_append) {
            $wr .= " AND job_orders.id not in ($request->jo_list_append)";
        }
        if (isset($request->start_date) || isset($request->end_date)) {
            if (isset($request->start_date)) {
                $start = dateDB($request->start_date);
                if (isset($request->end_date)) {
                    $end = dateDB($request->end_date);
                } else {
                    $end = $start;
                }
            } else if (isset($request->end_date)) {
                $end = dateDB($request->end_date);
                $start = $end;
            }
            $wr .= " AND job_orders.shipment_date BETWEEN '" . $start . "' AND '" . $end . "'";
        }
        $item = DB::table('job_orders')
            ->leftJoin('quotations', 'quotations.id', 'job_orders.quotation_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', 'job_orders.kpi_id')
            ->leftJoin('work_orders', 'work_orders.id', 'job_orders.work_order_id')
            ->leftJoin('companies', 'work_orders.company_id', 'companies.id')
            ->leftJoin('contacts as receiver', 'receiver.id', 'job_orders.receiver_id')
            ->leftJoin('contacts as sender', 'sender.id', 'job_orders.sender_id')
            ->whereRaw($wr)
            ->selectRaw('
        job_orders.*,
        contacts.name as customer,
        services.name as service,
        routes.name as trayek,
        work_orders.code as wo_code,
        kpi_statuses.name as kpi_status,
        service_types.name as service_type,
        sender.name as sender_name,
        receiver.name as receiver_name,
        concat(services.name,\' (\',service_types.name,\')\') service_full,
        companies.name as company_name
        ')->orderBy('job_orders.id', 'DESC')->orderBy('shipment_date', 'DESC');
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ng-show=\"roleList.includes('operational.job_order.detail')\" ui-sref='operational.job_order.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                if (empty($item->invoice_id)) {
                    $html .= "<a ng-show=\"roleList.includes('operational.job_order.edit')\" ui-sref='operational.job_order.edit({id:$item->id})' ><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
                    $html .= "<a ng-show=\"roleList.includes('operational.job_order.delete')\" ng-click='deletes($item->id)' ><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
                }
                return $html;
            })
            ->addColumn('action_customer', function ($item) {
                $html = "<a ui-sref='main.job_order.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                return $html;
            })
            ->addColumn('action_choose', function ($item) {
                $html = "<a ng-click='selectJO($item->id,\"$item->code\")' class='btn btn-xs btn-success'>Pilih</a>";
                return $html;
            })
            ->addColumn('checklist', function ($item) {
                return "<div class='checkbox checkbox-primary checkbox-inline'><input ng-change='isCheck()' type='checkbox' ng-model='checkData.detail[" . $item->id . "].value' ng-true-value='1' ng-false-value='0' id='tr-{$item->id}'><label for='tr-" . $item->id . "'></label></div>";
            })
            ->editColumn('no_bl', function ($item) {
                $str = "";
                $explode = explode(',', $item->no_bl);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->editColumn('aju_number', function ($item) {
                $str = "";
                $explode = explode(',', $item->aju_number);
                foreach ($explode as $key => $value) {
                    $str .= "$value<br>";
                }
                return $str;
            })
            ->editColumn('shipment_date', function ($item) {
                return dateView($item->shipment_date);
            })
            ->rawColumns(['action', 'checklist', 'action_choose', 'action_customer', 'no_bl', 'aju_number'])
            ->make(true);
        // return $dt->toJson();
    }
    public function job_order_inProgress(Request $request)
    {
        $month = new Carbon(date('Y-m-d', strtotime($request->date)));
        $startPeriode = $month->copy()->startOfMonth()->format('Y-m-d');
        $endPeriode = $month->copy()->endOfMonth()->format('Y-m-d');
        // kpi_statuses where is done 0
        $jo = DB::table('job_orders')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', 'job_orders.kpi_id')
            ->selectRaw("
    sum(if(kpi_statuses.is_done=1,1,0)) as total_done,
    sum(if(kpi_statuses.is_done=0,1,0)) as total_process
    ")
            ->whereBetween('job_orders.shipment_date', [$startPeriode, $endPeriode])->first();
        $response['data'] = $jo;
        return Response::json($response, 200, [], JSON_NUMERIC_CHECK);
    }
    public function invoice_jual_amount(Request $request)
    {
        $date = Carbon::parse($request->date)->format('Y-m-d');
        $month = new Carbon(date('Y-m-d', strtotime($request->date)));
        $startPeriode = $month->copy()->startOfMonth()->format('Y-m-d');
        $endPeriode = $month->copy()->endOfMonth()->format('Y-m-d');
        $period = Carbon::now()->format('Y-m');
        // invoice = 3, terbayar sebagian = 4, lunas = 5
        $unpaid = ModelInvoice::whereIn('status', [3])->whereBetween('date_invoice', [$startPeriode, $endPeriode]);
        if($request->day){
            $all = DB::table('invoices')->whereRaw("date_format(date_invoice, '%Y-%m-%d') = '$date'")
            ->selectRaw('count(id) as total_invoice, count(distinct customer_id) as total_customer, ifnull(sum(grand_total),0) as grand_total')->first();
            $unpaid = DB::table('receivables')
                ->leftJoin('invoices', 'invoices.id', 'receivables.relation_id')
                ->whereRaw("receivables.type_transaction_id = 26 and date_format(invoices.date_invoice, '%Y-%m-%d') = '$date' and (receivables.debet-receivables.credit) > 0")
                ->selectRaw('count(invoices.id) as total_invoice, count(distinct customer_id) as total_customer,ifnull(sum(debet-credit),0) as grand_total')->first();
            $unbill = DB::table('invoices')->whereIn('status', [2,4,5])->whereRaw("date_format(date_invoice, '%Y-%m-%d') = '$date'")
            ->selectRaw('count(id) as total_invoice, ifnull(sum(grand_total),0) as grand_total')->first();
            $paid = DB::table('receivables')
                ->leftJoin('invoices', 'invoices.id', 'receivables.relation_id')
                ->whereRaw("receivables.type_transaction_id = 26 and date_format(invoices.date_invoice, '%Y-%m-%d') = '$date' and (receivables.debet-receivables.credit) = 0")
                ->selectRaw('count(invoices.id) as total_invoice, count(distinct customer_id) as total_customer,ifnull(sum(debet),0) as grand_total')->first();
        } else {
            $all = DB::table('invoices')->whereRaw("date_format(date_invoice, '%Y-%m') = '$period'")
            ->selectRaw('count(id) as total_invoice, ifnull(sum(grand_total),0) as grand_total')->first();
            $unpaid = DB::table('receivables')
                ->leftJoin('invoices', 'invoices.id', 'receivables.relation_id')
                ->whereRaw("receivables.type_transaction_id = 26 and date_format(invoices.date_invoice, '%Y-%m') = '$period' and (receivables.debet-receivables.credit) > 0")
                ->selectRaw('count(invoices.id) as total_invoice, ifnull(sum(debet-credit),0) as grand_total')->first();
            $unbill = DB::table('invoices')->whereIn('status', [2,4,5])->whereRaw("date_format(date_invoice, '%Y-%m') = '$period'")
            ->selectRaw('count(id) as total_invoice, ifnull(sum(grand_total),0) as grand_total')->first();
        }
        $response['data'] = [
            'all' => [
                'count' => $all->total_invoice,
                'customer' => $all->total_customer ?? null,
                'summary' => $all->grand_total,
            ],
            'unpaid' => [
                'count' => $unpaid->total_invoice,
                'summary' => $unpaid->grand_total,
            ],
            'unbill' => [
                'count' => $unbill->total_invoice,
                'summary' => $unbill->grand_total,
            ],
            'paid' => [
                'count' => $paid->total_invoice ?? null,
                'customer' => $paid->total_customer ?? null,
                'summary' => $paid->grand_total ?? null,
            ],
        ];
        return Response::json($response, 200, [], JSON_NUMERIC_CHECK);
    }
    public function invoice_jual_datatable(Request $request)
    {
        $item = Invoice::query($request->all());
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $item = $item->where('invoices.company_id', auth()->user()->company_id);
        }
        return DataTables::of($item)
            ->addColumn('action_customer', function ($item) {
                $html = "<a ui-sref='main.invoice.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                return $html;
            })
            ->editColumn('created_at', function ($item) {
                return dateView($item->created_at);
            })
            ->editColumn('total', function ($item) {
                return formatNumber($item->total);
            })
            ->filterColumn('aju', function ($query, $keyword) {
                $sql = "Y.aju like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('bl', function ($query, $keyword) {
                $sql = "Y.bl like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('total', function ($query, $keyword) {
                $sql = "(grand_total+grand_total_additional) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->addColumn('status_name', function ($item) {
                $stt = [
                    1 => 'Diajukan',
                    2 => 'Disetujui',
                    3 => 'Posting',
                    4 => 'Terbayar Sebagian',
                    5 => 'Lunas',
                    6 => 'Void',
                ];
                return $stt[$item->status];
            })
            ->rawColumns(['action', 'action_customer', 'aju'])
            ->toJson();
    }
    public function manifest_datatable(Request $request, $isDatatable = true)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND manifests.company_id = " . auth()->user()->company_id;
        }
        if ($request->status) {
            $wr .= " and dod.job_status_id = " . $request->status;
        }
        if ($request->supplier_id) {
            $wr .= " and dod.vendor_id = " . $request->supplier_id;
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " AND date(manifests.date_manifest) between '$start' and '$end'";
        }
        $item = DB::table('manifests')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', function ($join) {
                $join->on('dod.id', '=', 'delivery_manifests.delivery_order_driver_id');
                $join->where('dod.status', '<', 3);
            })
            ->leftJoin('job_statuses', 'job_statuses.id', 'dod.job_status_id')
            ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin(DB::raw('(select count(*) as total_item, header_id from manifest_details group by header_id) as md'), 'md.header_id', 'manifests.id')
            ->whereRaw($wr)
            ->selectRaw('
        manifests.id,
        manifests.code,
        manifests.date_manifest,
        manifests.created_at,
        manifests.container_type_id,
        manifests.vehicle_type_id,
        manifests.is_full,
        if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
        if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
        routes.name as trayek,
        companies.name as company,
        job_statuses.name as job_status,
        dod.code as code_sj,
        ifnull(containers.container_no,manifests.container_no) as container_no,
        voyage_schedules.voyage,
        md.total_item
        ');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('manifest', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(manifests.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM manifests) AS additional_manifests";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_manifests.id', 'manifests.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_manifests.' . $a]);
            }
        }
        if ($request->filled('company_id')) {
            $item->where('manifests.company_id', $request->company_id);
        }
        if ($request->source) {
            $item->where('manifests.source', $request->source);
        }
        if ($request->is_crossdocking) {
            $item->where('manifests.is_crossdocking', $request->is_crossdocking);
        }
        if ($request->sales_order_id) {
            $item->leftJoin('manifest_details', 'manifest_details.header_id', 'manifests.id');
            $item->leftJoin('job_order_details as jod', 'manifest_details.job_order_detail_id', 'jod.id');
            $item->leftJoin('sales_orders as so', 'so.job_order_id', 'jod.header_id');
            $item->where('so.id', $request->sales_order_id);
            $item->groupBy(DB::raw('id, code, date_manifest, created_at, sopir, kendaraan, trayek, company, job_status, code_sj,
      container_no, voyage, total_item'));
        }
        if ($isDatatable == false) {
            return $item->paginate();
        }
        return DataTables::of($item)
            ->addColumn('tipe_angkut', function ($item) {
                $type = null;
                if ($item->is_full == 1) {
                    if ($item->container_type_id) {
                        $type = 'FCL';
                    } else {
                        $type = 'FTL';
                    }
                } else {
                    if ($item->container_type_id) {
                        $type = 'LCL';
                    } else {
                        $type = 'LTL';
                    }
                }
                return $type;
            })
            ->filterColumn('tipe_angkut', function ($query, $keyword) {
                $sql = "IF(manifests.is_full=1, IF(manifests.container_type_id IS NOT NULL, 'FCL', 'FTL'), IF(manifests.vehicle_type_id IS NOT NULL, 'LTL', 'LCL')) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->editColumn('job_status', function ($item) {
                if (!$item->job_status) {
                    return "Draft";
                }
                return $item->job_status;
            })
            ->addColumn('date_manifest_format', function ($item) {
                $date = date("Y-m-d", strtotime($item->date_manifest));
                return $date;
            })
            ->make(true);
    }
    /**
     * Date : 07-02-2022
     * Description : Menambahkan query sort berdasarkan manifest code, manifest date, company name
     * Developer : Syahrul
     * Status : Edit
     */
    public function manifest_ftl_datatable(Request $request)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND manifests.company_id = " . auth()->user()->company_id;
        }
        if ($request->status) {
            $wr .= " and dod.job_status_id = " . $request->status;
        }
        if ($request->supplier_id) {
            $wr .= " and dod.vendor_id = " . $request->supplier_id;
        }
        if ($request->filled('generateCostId')) {
            $wr .= " and manifests.generate_cost_id = " . $request->generateCostId;
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " AND date(manifests.date_manifest) between '$start' and '$end'";
        }
        $wr .= " and manifests.is_container = 0";
        $wr .= " and manifests.vehicle_type_id is not null";
        $item = DB::table('manifests')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', function ($join) {
                $join->on('dod.id', '=', 'delivery_manifests.delivery_order_driver_id');
                $join->where('dod.status', '<', 3);
            })
            ->leftJoin('job_statuses', 'job_statuses.id', 'manifests.job_status_id')
            ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
            ->leftJoin('manifest_details as mds', 'mds.header_id', 'manifests.id')
            ->leftJoin('job_order_details as jod', 'jod.id', 'mds.job_order_detail_id')
            ->leftJoin('job_orders as jo', 'jo.id', 'jod.header_id')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin(DB::raw('(select count(*) as total_item, header_id from manifest_details group by header_id) as md'), 'md.header_id', 'manifests.id')
            ->whereRaw($wr)
            ->selectRaw('
          manifests.id,
          jo.shipment_date,
          jo.no_po_customer,
          manifests.code,
          manifests.date_manifest,
          manifests.created_at,
          if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
          if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
          routes.name as trayek,
          companies.name as company,
          job_statuses.name as job_status,
          dod.code as code_sj,
          ifnull(containers.container_no,manifests.container_no) as container_no,
          voyage_schedules.voyage,
          md.total_item,
          if(manifests.is_full=1,\'FTL\',\'LTL\') as tipe_angkut
        ');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('manifest', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(manifests.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM manifests) AS additional_manifests";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_manifests.id', 'manifests.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_manifests.' . $a]);
            }
        }
        if ($request->filled('company_id')) {
            $item->where('manifests.company_id', $request->company_id);
        }
        if ($request->source) {
            $item->where('manifests.source', $request->source);
        }
        if ($request->is_crossdocking) {
            $item->where('manifests.is_crossdocking', $request->is_crossdocking);
        }
        if ($request->sales_order_id) {
            $item->leftJoin('sales_orders as so', 'so.job_order_id', 'jod.header_id');
            $item->where('so.id', $request->sales_order_id);
            $item->groupBy(DB::raw('id, code, date_manifest, created_at, sopir, kendaraan, trayek, company, job_status, code_sj,
          container_no, voyage, total_item, tipe_angkut'));
        }
        $item->groupBy('manifests.code');
        // $item->orderBy('manifests.code');
        // $item->orderBy('manifests.date_manifest');
        // $item->orderBy('companies.name');
        return DataTables::of($item)
            ->filterColumn('tipe_angkut', function ($query, $keyword) {
                $sql = "if(manifests.is_full=1,'FTL','LTL') like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('sopir', function ($query, $keyword) {
                $sql = "if(dod.driver_id is not null,driver.name, dod.driver_name) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('job_statuses.name', function ($query, $keyword) {
                if (strtolower($keyword) == 'draft') {
                    $sql = "job_statuses.name is null";
                } else {
                    $sql = "job_statuses.name like ?";
                }
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->editColumn('job_status', function ($item) {
                if (!$item->job_status) {
                    return "Draft";
                }
                return $item->job_status;
            })
            ->make(true);
    }
    /**
     * Date : 07-02-2022
     * Description : Menambahkan query sort berdasarkan manifest code, manifest date, company name
     * Developer : Syahrul
     * Status : Edit
     */
    public function manifest_fcl_datatable(Request $request)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND manifests.company_id = " . auth()->user()->company_id;
        }
        if ($request->status) {
            $wr .= " and dod.job_status_id = " . $request->status;
        }
        if ($request->company_id) {
            $wr .= " and manifests.company_id = " . $request->company_id;
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " AND date(manifests.date_manifest) between '$start' and '$end'";
        }
        if ($request->supplier_id) {
            $wr .= " and dod.vendor_id = " . $request->supplier_id;
        }
        $wr .= " and manifests.is_container = 1";
        $wr .= " and manifests.container_type_id is not null";
        $item = DB::table('manifests')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->leftJoin('container_types', 'container_types.id', 'containers.container_type_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', function ($join) {
                $join->on('dod.id', 'delivery_manifests.delivery_order_driver_id');
                $join->where('dod.status', '<', 3);
            })
            ->leftJoin('job_statuses', 'job_statuses.id', 'dod.job_status_id')
            ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
            ->leftJoin('contacts as codriver', 'codriver.id', 'dod.co_driver_id')
            ->leftJoin('manifest_details as mds', 'mds.header_id', 'manifests.id')
            ->leftJoin('job_order_details as jod', 'jod.id', 'mds.job_order_detail_id')
            ->leftJoin('job_orders as jo', 'jo.id', 'jod.header_id')
            ->leftJoin('container_types as ct', 'ct.id', 'jo.container_type_id')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin(DB::raw('(select count(*) as total_item, header_id from manifest_details group by header_id) as md'), 'md.header_id', 'manifests.id')
            ->whereRaw($wr)
            ->selectRaw('
        manifests.id,
        jo.shipment_date,
        manifests.code,
        manifests.date_manifest,
        manifests.created_at,
        manifests.is_cancel,
        if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
        if(dod.co_driver_id is not null,codriver.name, dod.co_driver) as codriver,
        if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
        routes.name as trayek,
        companies.name as company,
        job_statuses.name as job_status,
        dod.code as code_sj,
        jo.no_po_customer as no_po_customer,
        containers.container_no,
        ct.code as container_type,
        voyage_schedules.voyage,
        md.total_item,
        if(manifests.is_full=1,\'FCL\',\'LCL\') as tipe_angkut
        ');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('manifest', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(manifests.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM manifests) AS additional_manifests";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_manifests.id', 'manifests.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_manifests.' . $a]);
            }
        }
        $item->groupBy('manifests.code');
        // $item->orderBy('manifests.code');
        // $item->orderBy('manifests.date_manifest');
        // $item->orderBy('companies.name');
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ng-show=\"roleList.includes('operational.manifest.container.detail')\" ui-sref='operational.manifest_fcl.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                if ($item->total_item < 1) {
                    $html .= "<a ng-show=\"roleList.includes('operational.manifest.container.delete')\" ng-click='deletes($item->id)' ><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
                }
                return $html;
            })
            ->filterColumn('sopir', function ($query, $keyword) {
                $sql = "if(dod.driver_id is not null,driver.name, dod.driver_name) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('tipe_angkut', function ($query, $keyword) {
                $sql = "if(manifests.is_full=1,'FCL','LCL') like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->editColumn('job_status', function ($item) {
                if($item->is_cancel == 1){
                    return "Void";
                }else if (!$item->job_status) {
                    return "Manifest";
                }
                return $item->job_status;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function delivery_order_driver_datatable(Request $request)
    {
        $item = DOD::query($request->all());
        $item = $item->selectRaw('
        delivery_order_drivers.*,
        manifests.code as code_pl,
        driver.name as driver,
        routes.name as trayek,
        job_statuses.name as status_name,
        if(delivery_order_drivers.driver_id is not null,driver.name, delivery_order_drivers.driver_name) as sopir,
        if(delivery_order_drivers.vehicle_id is not null,vehicles.nopol, delivery_order_drivers.nopol) as kendaraan
        ')
            ->groupBy('delivery_order_drivers.code');
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ng-show=\"roleList.includes('operational.delivery_order.detail')\" ui-sref='operational.delivery_order_driver.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                return $html;
            })
            ->editColumn('pick_date', function ($item) {
                return dateView($item->pick_date);
            })
            ->editColumn('status_name', function ($item) {
                if ($item->status == 3) {
                    return '-';
                } else {
                    return $item->status_name;
                }
            })
            ->editColumn('status', function ($item) {
                $stt = [
                    1 => 'Ditugaskan',
                    2 => 'Selesai',
                    3 => 'Dibatalkan',
                ];
                return $stt[$item->status] ?? '';
            })
            ->editColumn('is_finish', function ($item) {
                $stt = [
                    1 => '<span class="badge badge-primary">Finished</span>',
                    0 => '<span class="badge badge-warning">Ongoing</span>',
                ];
                return $stt[$item->is_finish];
            })
            ->rawColumns(['action', 'is_finish'])
            ->make(true);
    }
    public function shipment_datatable(Request $request)
    {
        $m = DB::table('manifests')->where('id', $request->manifest_id)->first();
        $jo = DB::table('job_orders as jo')
            ->leftJoin('job_order_details as jod', 'jod.header_id', 'jo.id')
            ->leftJoin('manifest_details as md', 'md.job_order_detail_id', 'jod.id')
            ->where('md.header_id', $m->id)
            ->first();
        if ($jo != null) {
            $request['service_type_id'] = $jo->service_type_id;
        }
        $request['route_id'] = $m->route_id;
        $request['not_status'] = "10,11,12,13,14";
        $item = DOD::query($request->all());
        $item = $item->where('delivery_order_drivers.status', '<', 3);
        $item = $item->selectRaw('
        delivery_order_drivers.*,
        manifests.code as code_pl,
        driver.name as driver,
        vehicles.nopol,
        routes.name as trayek,
        job_statuses.name as status_name,
        if(delivery_order_drivers.driver_id is not null,driver.name, delivery_order_drivers.driver_name) as sopir,
        if(delivery_order_drivers.vehicle_id is not null,vehicles.nopol, delivery_order_drivers.nopol) as kendaraan
        ');
        return DataTables::of($item)
            ->addColumn('action_choose', function ($item) {
                $html = '<a ng-click=\'chooseShipment(' . json_encode($item, JSON_HEX_APOS) . ')\' class="btn btn-xs btn-success">Pilih</a>';
                return $html;
            })
            ->editColumn('pick_date', function ($item) {
                return dateView($item->pick_date);
            })
            ->editColumn('status_name', function ($item) {
                if ($item->status == 3) {
                    return '-';
                } else {
                    return $item->status_name;
                }
            })
            ->editColumn('status', function ($item) {
                $stt = [
                    1 => 'Ditugaskan',
                    2 => 'Selesai',
                    3 => 'Dibatalkan',
                ];
                return $stt[$item->status] ?? '';
            })
            ->editColumn('is_finish', function ($item) {
                $stt = [
                    1 => '<span class="badge badge-primary">Finished</span>',
                    0 => '<span class="badge badge-warning">Ongoing</span>',
                ];
                return $stt[$item->is_finish];
            })
            ->rawColumns(['action_choose', 'is_finish'])
            ->make(true);
    }
    /*
    Date : 24-03-2020
    Description : Menampilkan daftar shipment status dalam
    format datatable
    Developer : Didin
    Status : Edit
     */
    public function shipmentStatusDatatable(Request $request)
    {
        $this->updateShipmentTerkirim();
        $this->updateShipmentSampai();
        $this->updateShipmentSelesai();
        $start_date = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date = Carbon::parse($request->end_date)->format('Y-m-d');
        $item = DB::table('warehouse_receipts AS W')
            ->join('warehouse_receipt_details AS WD', 'WD.header_id', 'W.id')
            ->join('contacts AS C', 'C.id', 'W.customer_id')
            ->join(DB::raw("(SELECT warehouse_receipt_id, MAX(`status`) AS `status` FROM shipment_statuses GROUP BY warehouse_receipt_id) AS S"), 'S.warehouse_receipt_id', 'W.id')
            ->leftJoin('job_order_details AS JD', 'JD.warehouse_receipt_detail_id', 'WD.id')
            ->leftJoin('job_orders AS J', 'J.id', 'JD.header_id')
            ->whereRaw("DATE_FORMAT(W.receive_date, '%Y-%m-%d') BETWEEN '$start_date' AND '$end_date'")
            ->groupBy('W.id')
            ->select(
                'W.id',
                'W.code',
                'C.name AS customer_name',
                'S.status',
                'W.receive_date',
                DB::raw('GROUP_CONCAT(J.code SEPARATOR ",") AS job_order_code'),
                DB::raw('SUM(WD.qty) AS qty'),
                DB::raw('SUM(WD.long * WD.wide * WD.high * WD.qty / 1000000) AS volume'),
                DB::raw('SUM(WD.weight * WD.qty) AS weight')
            );
        return DataTables::of($item)
            ->make(true);
    }
    /*
    Date : 24-03-2020
    Description : Memeriksa KPI Status yang sudah selesai
    Developer : Didin
    Status : Create
     */
    public function updateShipmentSelesai()
    {
        // Periksa FTL
        $job_orders = DB::table('warehouse_receipts AS W')
            ->join('warehouse_receipt_details AS WD', 'WD.header_id', 'W.id')
            ->join('job_order_details AS JD', 'JD.warehouse_receipt_detail_id', 'WD.id')
            ->join('job_orders AS J', 'J.id', 'JD.header_id')
            ->join('kpi_statuses AS K', 'J.kpi_id', 'K.id')
            ->where('K.is_done', 1)
            ->groupBy('J.id')
            ->select('W.id')
            ->get();
        foreach ($job_orders as $job_order) {
            $latest_shipment = DB::table('shipment_statuses')
                ->whereWarehouseReceiptId($job_order->id)
                ->whereStatus(4)
                ->count('id');
            if ($latest_shipment < 1) {
                DB::table('shipment_statuses')
                    ->insert([
                        'warehouse_receipt_id' => $job_order->id,
                        'status_date' => DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'),
                        'status' => 4,
                    ]);
            }
        }
    }
    /*
    Date : 24-03-2020
    Description : Memeriksa manifest yang sudah terkirim
    Developer : Didin
    Status : Create
     */
    public function updateShipmentTerkirim()
    {
        // Periksa FTL
        $job_orders = DB::table('warehouse_receipts AS W')
            ->join('warehouse_receipt_details AS WD', 'WD.header_id', 'W.id')
            ->join('job_order_details AS JD', 'JD.warehouse_receipt_detail_id', 'WD.id')
            ->join('manifest_details AS MD', 'MD.job_order_detail_id', 'JD.id')
            ->join('manifests AS M', 'M.id', 'MD.header_id')
            ->select('W.id', 'M.depart', 'M.container_id', 'M.is_container')
            ->get();
        foreach ($job_orders as $job_order) {
            $now = Carbon::now();
            if ($job_order->is_container == 0 && $job_order->depart != null) {
                $depart = Carbon::parse($job_order->depart);
                if ($now->gt($depart) or $depart->eq($now)) {
                    $latest_shipment = DB::table('shipment_statuses')
                        ->whereWarehouseReceiptId($job_order->id)
                        ->whereStatus(2)
                        ->count('id');
                }
            } else if ($job_order->is_container == 1) {
                $container = DB::table('containers')
                    ->whereId($job_order->container_id)
                    ->first();
                if (($container->stripping ?? null) != null) {
                    $stripping = Carbon::parse($container->stripping);
                    if ($now->gt($stripping) or $stripping->eq($now)) {
                        $latest_shipment = DB::table('shipment_statuses')
                            ->whereWarehouseReceiptId($job_order->id)
                            ->whereStatus(2)
                            ->count('id');
                    }
                }
            }
            if (($latest_shipment ?? 2) < 1) {
                DB::table('shipment_statuses')
                    ->insert([
                        'warehouse_receipt_id' => $job_order->id,
                        'status_date' => DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'),
                        'status' => 2,
                    ]);
            }
        }
    }
    /*
    Date : 24-03-2020
    Description : Memeriksa manifest yang sudah sampai
    Developer : Didin
    Status : Create
     */
    public function updateShipmentSampai()
    {
        // Periksa FTL
        $job_orders = DB::table('warehouse_receipts AS W')
            ->join('warehouse_receipt_details AS WD', 'WD.header_id', 'W.id')
            ->join('job_order_details AS JD', 'JD.warehouse_receipt_detail_id', 'WD.id')
            ->join('manifest_details AS MD', 'MD.job_order_detail_id', 'JD.id')
            ->join('manifests AS M', 'M.id', 'MD.header_id')
            ->select('W.id', 'M.arrive', 'M.container_id', 'M.is_container')
            ->get();
        foreach ($job_orders as $job_order) {
            $now = Carbon::now();
            if ($job_order->is_container == 0 && $job_order->arrive != null) {
                $arrive = Carbon::parse($job_order->arrive);
                if ($now->gt($arrive) or $arrive->eq($now)) {
                    $latest_shipment = DB::table('shipment_statuses')
                        ->whereWarehouseReceiptId($job_order->id)
                        ->whereStatus(3)
                        ->count('id');
                }
            } else if ($job_order->is_container == 1) {
                $container = DB::table('containers')
                    ->whereId($job_order->container_id)
                    ->first();
                if (($container->stuffing ?? null) != null) {
                    $stuffing = Carbon::parse($container->stuffing);
                    if ($now->gt($stuffing) or $stuffing->eq($now)) {
                        $latest_shipment = DB::table('shipment_statuses')
                            ->whereWarehouseReceiptId($job_order->id)
                            ->whereStatus(3)
                            ->count('id');
                    }
                }
            }
            if (($latest_shipment ?? 2) < 1) {
                DB::table('shipment_statuses')
                    ->insert([
                        'warehouse_receipt_id' => $job_order->id,
                        'status_date' => DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'),
                        'status' => 3,
                    ]);
            }
        }
    }
    /*
    Date : 17-03-2020
    Description : Menampilkan daftar biaya job order dalam format
    datatable
    Developer : Didin
    Status : Edit
     */
    public function job_order_cost_datatable(Request $request)
    {
        $wr = "1=1 and job_order_costs.vendor_id is not null and job_order_costs.header_id is not null";
        if ($request->status) {
            $wr .= " and job_order_costs.status = {$request->status}";
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " and date(job_order_costs.created_at) between '$start' and '$end'";
        }
        $item = DB::table('job_order_costs')
            ->leftJoin('contacts', 'contacts.id', 'job_order_costs.vendor_id')
            ->leftJoin('job_orders', 'job_orders.id', 'job_order_costs.header_id')
            ->leftJoin('cost_types', 'cost_types.id', 'job_order_costs.cost_type_id')
            ->leftJoin('sales_orders', 'sales_orders.job_order_id', 'job_orders.id')
            ->whereRaw($wr)
            ->selectRaw('
                job_order_costs.*,
                contacts.name as vendor,
                cost_types.name as cost_type,
                cost_types.akun_kas_hutang AS account_id,
                COALESCE(sales_orders.code, job_orders.code) as code,
                job_orders.shipment_date
            ');
        if ($request->filled('cost_type')) {
            $item->where('cost_types.type', $request->cost_type);
        }
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ui-sref='operational.job_order.show({id:" . $item->header_id . "})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>";
                return $html;
            })
            ->addColumn('action_vendor', function ($item) use ($request) {
                if ($request->user()->hasRole('vendor.job_order.detail')) {
                    return "<a ui-sref='operational.job_order.show({id:" . $item->header_id . "})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                }
                return '';
            })
            ->editColumn('status', function ($item) {
                $stt = [
                    1 => 'Belum Diajukan',
                    2 => 'Diajukan Keuangan',
                    3 => 'Disetujui Keuangan',
                    4 => 'Ditolak',
                    5 => 'Diposting',
                    6 => 'Revisi',
                    7 => 'Diajukan',
                    8 => 'Disetujui Atasan',
                    9 => '',
                    10 => '',
                ];
                return $stt[$item->status];
            })
            ->editColumn('created_at', function ($item) {
                return Carbon::parse($item->created_at)->format('d-m-Y');
            })
            ->rawColumns(['action', 'action_vendor', 'status'])
            ->make(true);
    }
    /*
    Date : 03-03-2020
    Description : Menampilkan daftar biaya manifest dalam format
    datatable
    Developer : Didin
    Status : Create
     */
    public function manifest_cost_datatable(Request $request)
    {
        $item = DB::table('manifest_costs')
            ->leftJoin('contacts', 'contacts.id', 'manifest_costs.vendor_id')
            ->leftJoin('manifests', 'manifests.id', 'manifest_costs.header_id')
            ->leftJoin('cost_types', 'cost_types.id', 'manifest_costs.cost_type_id')
            ->selectRaw('
      manifest_costs.*,
      contacts.name as vendor,
      manifests.code,
      cost_types.akun_kas_hutang as account_id,
      cost_types.name as cost_type
    ');
        if ($request->filled('cost_type')) {
            $item->where('cost_types.type', $request->cost_type);
        }
        return DataTables::of($item)
            ->make(true);
    }
    public function invoice_vendor_datatable(Request $request)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND invoice_vendors.company_id = " . auth()->user()->company_id;
        }
        if ($request->is_tire == 1) {
            $tire = DB::table('items')
            ->join('categories', 'categories.id', 'items.category_id')
            ->join('categories AS parents', 'categories.parent_id', 'parents.id')
            ->whereRaw('categories.is_tire = 1')
            ->select('items.id');
            $tire = $tire->toSql();
            $details = DB::table('invoice_vendor_details')
            ->select('header_id')
            ->whereRaw("item_id IN ($tire)");
            $details = $details->toSql();
            $wr .= " AND invoice_vendors.id IN ($details)";
        }
        
        $joc = DB::table('job_order_costs as joc')
                ->leftJoin('job_orders as jo', 'jo.id', 'joc.header_id')
                ->leftJoin('work_orders as wo', 'wo.id', 'jo.work_order_id')
                ->leftJoin('container_types as cont_type', 'cont_type.id', 'wo.container_type_id')
                ->selectRaw('
                    joc.id,
                    GROUP_CONCAT(DISTINCT wo.code SEPARATOR ", ") as wo_code,
                    GROUP_CONCAT(DISTINCT wo.container_number, " - ", cont_type.name SEPARATOR ", ") as container_number
                ')
                ->groupBy('joc.id');

        $item = InvoiceVendor::with('vendor', 'company')
                ->leftJoin('invoice_vendor_details as ivd', 'ivd.header_id', 'invoice_vendors.id')
                ->leftJoinSub($joc, 'joc', 'joc.id', 'ivd.job_order_cost_id')
                ->whereRaw($wr);
        $start_date = $request->start_date;
        $start_date = $start_date != null ? new DateTime($start_date) : '';
        $end_date = $request->end_date;
        $end_date = $end_date != null ? new DateTime($end_date) : '';
        $item = $start_date != '' && $end_date != '' ? $item->whereBetween('date_invoice', [$start_date->format('Y-m-d'), $end_date->format('Y-m-d')]) : $item;
        $company_id = $request->company_id;
        $company_id = $company_id != null ? $company_id : '';
        $item = $company_id != '' ? $item->where('company_id', $company_id) : $item;
        $vendor_id = $request->vendor_id;
        $vendor_id = $vendor_id != null ? $vendor_id : '';
        $item = $vendor_id != '' ? $item->where('vendor_id', $vendor_id) : $item;
        $status = $request->status;
        $status = $status != null ? $status : '';
        $item = $status != '' ? $item->where('status', $status) : $item;
        $is_vm = $request->is_vehicle_maintenance;
        $is_vm = $is_vm != null ? $is_vm : '';
        $item = $is_vm != '' ? $item->where('is_vehicle_maintenance', $is_vm) : $item;
        $is_gr = $request->is_good_receipt;
        $is_gr = $is_gr != null ? $is_gr : '';
        $item = $is_gr != '' ? $item->where('is_good_receipt', $is_gr) : $item;
        $is_fnc = $request->is_finance_vendor_bills;
        $is_fnc = $is_fnc != null ? $is_fnc : '';
        $item = $is_fnc != '' ? $item->where('is_finance_vendor_bills', $is_fnc) : $item;
        $item = $item->select(
                    'invoice_vendors.*',
                    'joc.wo_code',
                    'joc.container_number'
                    )
                    ->groupBy('invoice_vendors.id');
        if ($request->draw == 1) {
            $item = $item->orderBy('invoice_vendors.id', 'DESC');
        }
        return DataTables::of($item)
            // ->addColumn('action', function($item){
            //   $html="<a ng-show=\"roleList.includes('operational.invoice_vendor.detail')\" ui-sref='operational.invoice_vendor.show({id:".$item->id."})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>";
            //   return $html;
            // })
            ->editColumn('status', function ($item) {
                $stt = [
                    1 => 'Belum Lunas',
                    2 => 'Lunas',
                ];
                return $stt[$item->status];
            })
            // ->editColumn('status_approve', function($item){
            //   $stt=[
            //     0=>'<span class="badge badge-warning">Belum Disetujui</span>',
            //     1=>'<span class="badge badge-primary">Disetujui</span>',
            //     2=>'<span class="badge badge-success">Jurnal disetujui</span>',
            //     4=>'<span class="badge badge-success">Hutang telah dibuat</span>',
            //   ];
            //   return $stt[$item->status_approve];
            // })
            ->editColumn('total', function ($item) {
                return formatNumber($item->total);
            })
            ->addColumn('total_raw', function ($item) {
                return $item->total;
            })
            ->rawColumns(['status'])
            ->make(true);
    }
    public function kpi_log_datatable(Request $request)
    {
        JO::setNullableAdditionals();
        $wr = "kpi_logs.id in (select max(kpi_logs.id) from kpi_logs group by kpi_logs.job_order_id)";
        $wr_jo = "1=1";
        $params = $request->params;
        if (($params['customer_id'] ?? false)) {
            $wr_jo .= " AND customer_id = " . $params['customer_id'];
        }
        if (($params['job_order'] ?? false)) {
            $txt = $params['job_order'];
            $wr_jo .= " AND code LIKE '%$txt%'";
        }
        if (($params['create_by'] ?? false)) {
            $wr .= " AND job_orders.create_by = " . $params['create_by'];
        }
        if (($params['service'] ?? false)) {
            $wr_jo .= " AND service_id = " . $params['service'];
        }
        if (($params['kpi_status_id'] ?? false)) {
            $wr_jo .= " AND kpi_status_id = " . $params['kpi_status_id'];
        }
        if (($params['start_date'] ?? false) && ($params['end_date'] ?? false)) {
            $start = Carbon::parse($params['start_date'])->format('Y-m-d');
            $end = Carbon::parse($params['end_date'])->format('Y-m-d');
            $wr .= " AND date(date_update) between '$start' AND '$end'";
        }
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr_jo .= " AND company_id = " . auth()->user()->company_id;
        }
        //$wr.=" AND (job_orders.kpi_id = kpi_logs.kpi_status_id)";
        $item = KpiLog::with('job_order', 'job_order.service', 'job_order.customer', 'creates', 'kpi_status')
            ->whereHas('job_order', function ($query) use ($wr_jo) {
                $query->whereRaw($wr_jo);
            })
            ->leftJoin('job_orders', 'job_orders.id', 'kpi_logs.job_order_id')
            ->whereRaw($wr)
            ->selectRaw('kpi_logs.*');
        $params = [];
        $params['show_in_operational_progress'] = 1;
        $jobOrderAdditional = AdditionalField::indexKey('jobOrder', $params);
        $additionalColumn = '';
        if (count($jobOrderAdditional) > 0) {
            foreach ($jobOrderAdditional as $i => $v) {
                $item = $item->addSelect([DB::raw("REPLACE(JSON_EXTRACT(job_orders.additional, '$.$v'), '\"', '')  AS $v")]);
            }
        }
        //->groupBy('kpi_logs.job_order_id');
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ng-show=\"roleList.includes('operational.progress.detail')\" ui-sref='operational.job_order.show({id:" . $item->job_order_id . "})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                // $html.="<a ng-click='edit(".json_encode($item,JSON_HEX_APOS).")' ><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
                if (isset($item->file_name)) {
                    $html .= '<a ng-show="roleList.includes(\'operational.progress.detail\')" download="docs_' . $item->job_order_id . '_' . $item->id . '" href="' . url($item->file_name) . '"><i class="fa fa-file"></i></a>';
                }
                return $html;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function map_driver_job_list(Request $request)
    {
        $driverOn = DB::table('contacts')
            ->leftJoin('vehicles', 'vehicles.id', 'contacts.vehicle_id')
            ->whereRaw("contacts.is_driver = 1 and contacts.is_login = 1")
            ->selectRaw('
    contacts.id,
    contacts.name,
    contacts.lat,
    contacts.lng,
    vehicles.nopol,
    contacts.updated_at as last_update
    ')->get();
        $job = DB::table('delivery_order_drivers as dod')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin('contacts', 'contacts.id', 'dod.driver_id')
            ->leftJoin('job_statuses', 'job_statuses.id', 'dod.job_status_id')
            ->whereRaw('date(dod.updated_at) = date(now())')
            ->selectRaw('
    contacts.id as driver_id,
    contacts.name as driver,
    vehicles.nopol,
    job_statuses.name as status,
    dod.code as no_sj,
    dod.is_finish,
    dod.updated_at as last_update
    ')
            ->orderBy('last_update', 'desc')->get();
        $data = [
            'driver' => $driverOn,
            'job' => $job,
        ];
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function get_last_position_by_vendor_1(Request $request)
    {
        $params = [
            'token' => '16D90D94C567488',
            'all_vehicle' => 1,
        ];
        $url = "http://vts.easygo-gps.co.id/api/get_vts_last_position.aspx" . '?' . http_build_query($params);
        $get = Curl::to($url)
            ->asJson()
            ->post();
        // dd($get);
        $data = [];
        foreach ($get->data as $key => $value) {
            $ve = (array) DB::table('vehicles')
                ->leftJoin('delivery_order_drivers as dod', 'dod.id', 'vehicles.id')
                ->leftJoin('contacts', 'contacts.id', 'dod.driver_id')
                ->leftJoin('manifests', 'manifests.id', 'dod.manifest_id')
                ->leftJoin('routes', 'routes.id', 'manifests.route_id')
                ->where('vehicles.gps_no', $value->msisdn)
                ->selectRaw('
      contacts.name as driver,
      dod.code as code_sj,
      routes.name as trayek,
      dod.id as id,
      dod.job_status_id
      ')->first();
            if (!$ve) {
                continue;
            }
            $data[] = [
                'no_pol' => $value->no_pol,
                'latitude' => $value->latitude,
                'longitude' => $value->longitude,
                'gps_time' => $value->gps_time,
                'address' => $value->address,
                'acc' => $value->acc,
                'trayek' => $ve['trayek'],
                'driver' => $ve['driver'],
                'code_sj' => $ve['code_sj'],
                'delivery_id' => $ve['id'],
                'job_status_id' => $ve['job_status_id'],
            ];
        }
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function get_last_position_by_vendor_2(Request $request)
    {
        $params = [
            'memberCode' => 'BCS',
            'password' => 'Z170KPJpT4pPcgmX',
            'lastPositionId' => 0,
            'maxCount' => 100,
        ];
        $url = "http://api.inovatrack.com/api/data/GetPositions" . '?' . http_build_query($params);
        $get = Curl::to($url)
            ->asJson()
            ->get();
        return Response::json($get, 200, [], JSON_NUMERIC_CHECK);
    }
    public static function voyage_schedule_query(Request $request)
    {
        $wr = '1=1';
        if ($request->ships) {
            $wr .= " AND voyage_schedules.vessel_id = {$request->ships}";
        }
        if ($request->start_date_eta) {
            $wr .= " AND voyage_schedules.eta >= '" . dateDB($request->start_date_eta) . ' 00:00:00' . "'";
        }
        if ($request->end_date_eta) {
            $wr .= " AND voyage_schedules.eta <= '" . dateDB($request->end_date_eta) . ' 23:59:59' . "'";
        }
        if ($request->start_date_etd) {
            $wr .= " AND voyage_schedules.etd >= '" . dateDB($request->start_date_etd) . ' 00:00:00' . "'";
        }
        if ($request->end_date_etd) {
            $wr .= " AND voyage_schedules.etd <= '" . dateDB($request->end_date_etd) . ' 23:59:59' . "'";
        }
        return VoyageSchedule::with('vessel', 'pol', 'pod')
            ->leftJoinSub(
                DB::table('containers')
                ->leftJoin('manifests', 'manifests.container_id', '=', 'containers.id')
                ->whereNotNull('manifests.id')
                ->groupBy('voyage_schedule_id')
                ->selectRaw('voyage_schedule_id, COUNT(DISTINCT containers.id) as total'),
                'Y',
                'Y.voyage_schedule_id',
                'voyage_schedules.id'
            )
            ->whereRaw($wr)
            ->select('voyage_schedules.*', 'Y.total');
    }
    public static function container_query(Request $request)
    {
        $wr = "1=1";
        if (isset($request->is_fcl)) {
            $wr .= " AND is_fcl = $request->is_fcl";
        }
        if (isset($request->voyage_id)) {
            $wr .= " AND containers.voyage_schedule_id = $request->voyage_id";
        }
        if (isset($request->vessel_id)) {
            $wr .= " AND containers.vessel_id = $request->vessel_id";
        }
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND containers.company_id = " . auth()->user()->company_id;
        } else if ($request->company_id) {
            $wr .= " AND containers.company_id = " . $request->company_id;
        }
        $start_date = $request->start_date;
        $start_date = $start_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $start_date) : '';
        $end_date = $request->end_date;
        $end_date = $end_date != null ? preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $end_date) : '';
        if ($start_date != '' && $end_date != '') {
            $wr .= " AND booking_date BETWEEN '$start_date' AND '$end_date'";
        }
        $dt = Container::with('company:id,name', 'voyage_schedule', 'voyage_schedule.vessel', 'container_type')
        ->leftJoin('contacts as vendor','vendor.id','containers.vendor_id')
        ->leftJoin('manifests','manifests.container_id','containers.id')
        ->leftJoin('manifest_details','manifest_details.header_id','manifests.id')
        ->leftJoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
        ->leftJoin('job_orders','job_orders.id','job_order_details.header_id')
        ->leftJoin('contacts as sender','sender.id','job_orders.sender_id')
        ->leftJoin('contacts as receiver','receiver.id','job_orders.receiver_id')
        ->select('containers.*',
            'vendor.name as vendor_name',
            'job_order_details.qty as total_item2',
            'job_order_details.weight as total_tonase2',
            'job_order_details.volume as total_volume2',
            'sender.name as sender_name',
            'receiver.name as receiver_name',
            )->whereRaw($wr)->groupBy('containers.id');
        if ($request->job_order_id) {
            $job_order_id = $request->job_order_id;
            $dt = $dt->whereRaw("containers.id IN (SELECT container_id FROM job_order_containers WHERE job_order_id = $job_order_id) ");
        }
        // $dt = $dt->leftJoin(DB::raw("(select id as jo_container_id,container_id as jojo_container_id, job_order_id as jojo_id from job_order_containers) Y"),"containers.id","=","Y.jo_container_id");
        // $dt = $dt->leftJoin(DB::raw("(select id as job_id, code as code_jo from job_orders) JO"),"Y.jojo_id","=","JO.job_id");
        return $dt;
    }
    public function jo_cost_vendor_datatable(Request $request)
    {
        $data = DB::table('job_order_costs as joc');
        $data = $data->leftJoin('job_orders as jo', 'jo.id', 'joc.header_id');
        $data = $data->leftJoin('cost_types as ct', 'ct.id', 'joc.cost_type_id');
        // $data = $data->where('joc.manifest_cost_id', null);
        if ($request->type == 'all') {
            $data = $data->where('joc.type', '!=', null);
        } else {
            $data = $data->where('joc.type', 1);
        }
        $data = $data->where('ct.type', 1); // type accrual
        $data = $data->where(function ($query) {
            $query->where('joc.status', 10);// ->orWhere('joc.status', 9);
            // $query->where('joc.status', 5);
            // $query->orWhere('joc.status', 8);
        });
        $no_po_customer = $data->select('jo.no_po_customer');
        $data = $data->where('joc.is_vendor_bill', 0);
        // $co_data= DB::table('customer_orders')->where('code','686')->first();
        // $so_data= DB::table('sales_orders')->where('customer_order_id',$co_data->id);
        $data = $data->select([
            'joc.id',
            DB::raw('ifnull(jo.sales_order_code,jo.code) as code'),
            DB::raw('concat(ct.code,\' - \',ct.name) as name'),
            'joc.total_price',
            'joc.description',
            'jo.shipment_date',
        ]);
        if ($request->filled('vendor_id')) {
            $data = $data->where('joc.vendor_id', $request->vendor_id);
        }
        if ($request->filled('not_id')) {
            $data = $data->whereNotIn('joc.id', $request->not_id);
        }
        return DataTables::of($data)
            ->filterColumn('name', function ($query, $keyword) {
                $sql = "CONCAT(ct.code,'-',ct.name) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('code', function ($query, $keyword) {
                $sql = "ifnull(jo.sales_order_code,jo.code) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->toJson(true);
    }
    /*
    Date : 09-07-2021
    Description : Menampilkan daftar kategori klaim datatable
    Developer : Hendra
    Status : Create
     */
    public function claim_categories_datatable(Request $request)
    {
        $data = DB::table('claim_categories')
            ->select('id', 'name');
        return DataTables::of($data)
            ->make(true);
    }
    /*
    Date : 09-07-2021
    Description : Menampilkan daftar klaim datatable
    Developer : Hendra
    Status : Create
     */
    public function claims_datatable(Request $request)
    {
        $dt = DB::table('claims')
            ->join('claim_details', 'claim_details.header_id', 'claims.id')
            ->join('contacts AS customers', 'customers.id', 'claims.customer_id')
            ->leftJoin('contacts AS collects', 'collects.id', 'claims.collectible_id')
            ->leftJoin('job_orders', 'job_orders.id', 'claims.job_order_id')
            ->leftJoin('sales_orders', 'sales_orders.id', 'claims.sales_order_id')
            ->leftJoin('job_orders as jo_so', 'sales_orders.job_order_id', 'jo_so.id')
            ->join('companies', 'companies.id', 'claims.company_id')
            ->select(
                'claims.id',
                'claims.code',
                'claims.date_transaction',
                'customers.name AS customer_name',
                'collects.name AS collectible_name',
                'claim_type',
                'claims.status',
                DB::raw('IF(claims.status = 1, "Draft", "Disetujui") AS status_name'),
                'job_orders.code AS job_order_code',
                'job_orders.shipment_date AS job_order_date',
                'sales_orders.code AS sales_order_code',
                'jo_so.shipment_date AS sales_order_date',
                'companies.name AS company_name'
            )
            ->where('claim_details.sold_qty', '!==', 'claim_details.qty');
        if ($request->filled('start_date')) {
            $dt = $dt->where('claims.date_transaction', '>=', dateDB($request->start_date));
        }
        if ($request->filled('end_date')) {
            $dt = $dt->where('claims.date_transaction', '<=', dateDB($request->end_date));
        }
        if ($request->filled('company_id')) {
            $dt->where('claims.company_id', $request->company_id);
        }
        if ($request->filled('customer_id')) {
            $dt->where('claims.customer_id', $request->customer_id);
        }
        if ($request->filled('status')) {
            $dt->where('claims.status', $request->status);
        }
        return DataTables::of($dt->groupBy('claims.id'))
            ->addColumn('jo_so_code', function ($row) {
                return $row->job_order_code ?? $row->sales_order_code;
            })
            ->addColumn('jo_so_date', function ($row) {
                return $row->job_order_date ?? $row->sales_order_date;
            })
            ->make(true);
    }
    public function claim_details_datatable(Request $request)
    {
        // dd($request->all());
        $dt = DB::table('claim_details')
            ->select('claim_details.*', 'commodities.name as commodity_name')
            ->join('commodities', 'commodities.id', 'claim_details.commodity_id')
            ->where('sold_qty', '!==', 'qty');
        if ($request->filled('claim_id')) {
            $dt->where('header_id', $request->claim_id);
        }
        return DataTables::of($dt)
            ->make(true);
    }
    public function claim_sales_datatable(Request $request)
    {
        $dt = DB::table('claim_sales')->select(
            'claim_sales.*',
            'companies.name as company_name',
            'contacts.name as customer_name'
        )
            ->join('companies', 'companies.id', 'claim_sales.company_id')
            ->join('contacts', 'contacts.id', 'claim_sales.customer_id')
            ->get();
        return DataTables::of($dt)
            ->editColumn('term', function ($item) {
                if ($item->term == 1) {
                    return 'Cash';
                } else {
                    return 'Kredit';
                }
            })
            ->editColumn('status', function ($item) {
                if ($item->status == 1) {
                    return 'Belum Disetujui';
                } else {
                    return 'Selesai';
                }
            })
            ->make(true);
    }
    /**
     * Date : 13-01-2022
     * Description : Menambahkan filter & export excel
     * Developer : Syahrul
     * Status : Edit
     */
    /*
    Date : 30-09-2022 By : Muhammad Firyanul Rizky
    Description : [Improve] Shipment Summary
    Keterangan : Menambah field co-driver Shipment Summary
     */
    public function manifest_activity(Request $request)
    {
        $is_periode = ($request->start_date && $request->end_date);
        $costs = DB::raw("(
      SELECT SUM(IFNULL(mc.total_price,0)) as cost_total,mc.header_id as manifest_id
      FROM manifest_costs as mc
      GROUP BY mc.header_id
      ) as costs");
        $item = DB::table('manifests')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', 'dod.id', 'delivery_manifests.delivery_order_driver_id')
            ->leftJoin('contacts as co_driver', 'co_driver.id', 'dod.co_driver_id')
            ->leftJoin('manifest_details', 'manifest_details.header_id', 'manifests.id')
            ->leftJoin('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
            ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin($costs, 'costs.manifest_id', 'manifests.id')
            ->leftJoin('vehicles', 'vehicles.id', 'manifests.vehicle_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('contacts as driver', 'driver.id', 'manifests.driver_id')
            ->select(
                'companies.name as company_name',
                'manifests.id as m_id',
                'manifests.code',
                'manifests.is_full',
                'manifests.container_type_id',
                'manifests.vehicle_type_id',
                'manifests.date_manifest',
                'manifests.revenue as revenue_total',
                'routes.name as route_name',
                'costs.cost_total',
                'job_order_details.imposition',
                'job_orders.service_type_id',
                DB::RAW('if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir'),
                DB::RAW('COALESCE(if(dod.co_driver_id is not null,co_driver.name, dod.co_driver),"-") as co_driver'),
                DB::RAW('if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan'),
            )
            ->distinct()
            // Close Description : [Improve] Shipment Summary
            ->when($request->company_id, function ($sql) use ($request) {
                $sql->where('companies.id', $request->company_id);
            })
            ->when($is_periode, function ($sql) use ($request) {
                $start = Carbon::parse($request->start_date)->format('Y-m-d');
                $end = Carbon::parse($request->end_date)->format('Y-m-d');
                $sql->whereBetween('manifests.date_manifest', [$start, $end]);
            })
            ->when($request->vehicle_id, function ($sql) use ($request) {
                $sql->where('vehicles.id', $request->vehicle_id);
            })
            ->when($request->driver_id, function ($sql) use ($request) {
                $sql->where('driver.id', $request->driver_id);
            })
            ->when($request->route_id, function ($sql) use ($request) {
                $sql->where('routes.id', $request->route_id);
            });
        return DataTables::of($item)
            ->make(true);
    }
    public function get_top_route()
    {
        $item = DB::table('job_orders')
            ->where('route_id', '!=', null)
            ->where('job_orders.is_cancel', 0)
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->selectRaw('COUNT(*) as count, routes.name')
            ->groupBy('routes.name')
            ->orderBy('count', 'desc')
            ->get()->take(5);
        return $item;
    }
    public function get_chart_manifest_ftl(Request $request)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND manifests.company_id = " . auth()->user()->company_id;
        }
        if ($request->status) {
            $wr .= " and dod.job_status_id = " . $request->status;
        }
        if ($request->supplier_id) {
            $wr .= " and dod.vendor_id = " . $request->supplier_id;
        }
        if ($request->filled('generateCostId')) {
            $wr .= " and manifests.generate_cost_id = " . $request->generateCostId;
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " AND date(manifests.date_manifest) between '$start' and '$end'";
        }
        $wr .= " and manifests.is_container = 0";
        $wr .= " and manifests.vehicle_type_id is not null";
        $item = DB::table('manifests')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', function ($join) {
                $join->on('dod.id', '=', 'delivery_manifests.delivery_order_driver_id');
                $join->where('dod.status', '<', 3);
            })
            ->leftJoin('job_statuses', 'job_statuses.id', 'dod.job_status_id')
            ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin(DB::raw('(select count(*) as total_item, header_id from manifest_details group by header_id) as md'), 'md.header_id', 'manifests.id')
            ->selectRaw('COUNT(*) as count, job_statuses.name')
            ->whereIn('job_statuses.id', [2, 11, 14])
            ->groupBy('job_statuses.name')
            ->whereRaw($wr)
            ->selectRaw('
        manifests.id,
        manifests.code,
        manifests.date_manifest,
        manifests.created_at,
        dod.job_status_id,
        if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
        if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
        routes.name as trayek,
        companies.name as company,
        job_statuses.name as job_status,
        dod.code as code_sj,
        ifnull(containers.container_no,manifests.container_no) as container_no,
        voyage_schedules.voyage,
        md.total_item,
        if(manifests.is_full=1,\'FTL\',\'LTL\') as tipe_angkut
      ');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('manifest', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(manifests.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM manifests) AS additional_manifests";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_manifests.id', 'manifests.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_manifests.' . $a]);
            }
        }
        if ($request->filled('company_id')) {
            $item->where('manifests.company_id', $request->company_id);
        }
        if ($request->source) {
            $item->where('manifests.source', $request->source);
        }
        if ($request->is_crossdocking) {
            $item->where('manifests.is_crossdocking', $request->is_crossdocking);
        }
        if ($request->sales_order_id) {
            $item->leftJoin('manifest_details', 'manifest_details.header_id', 'manifests.id');
            $item->leftJoin('job_order_details as jod', 'manifest_details.job_order_detail_id', 'jod.id');
            $item->leftJoin('sales_orders as so', 'so.job_order_id', 'jod.header_id');
            $item->where('so.id', $request->sales_order_id);
            $item->groupBy(DB::raw('id, code, date_manifest, created_at, sopir, kendaraan, trayek, company, job_status, code_sj,
        container_no, voyage, total_item, tipe_angkut'));
        }
        // $item->orderBy('manifests.code');
        // $item->orderBy('manifests.date_manifest');
        // $item->orderBy('companies.name');
        return DataTables::of($item)
            ->filterColumn('tipe_angkut', function ($query, $keyword) {
                $sql = "if(manifests.is_full=1,'FTL','LTL') like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('sopir', function ($query, $keyword) {
                $sql = "if(dod.driver_id is not null,driver.name, dod.driver_name) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('job_statuses.name', function ($query, $keyword) {
                if (strtolower($keyword) == 'draft') {
                    $sql = "job_statuses.name is null";
                } else {
                    $sql = "job_statuses.name like ?";
                }
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->editColumn('job_status', function ($item) {
                if (!$item->job_status) {
                    return "Draft";
                }
                return $item->job_status;
            })
            ->make(true);
        return $item;
    }
    public function get_chart_manifest_fcl(Request $request)
    {
        $wr = "1=1";
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $wr .= " AND manifests.company_id = " . auth()->user()->company_id;
        }
        if ($request->status) {
            $wr .= " and dod.job_status_id = " . $request->status;
        }
        if ($request->start_date && $request->end_date) {
            $start = Carbon::parse($request->start_date)->format('Y-m-d');
            $end = Carbon::parse($request->end_date)->format('Y-m-d');
            $wr .= " AND date(manifests.date_manifest) between '$start' and '$end'";
        }
        if ($request->supplier_id) {
            $wr .= " and dod.vendor_id = " . $request->supplier_id;
        }
        $wr .= " and manifests.is_container = 1";
        $wr .= " and manifests.container_type_id is not null";
        $item = DB::table('manifests')
            ->leftJoin('containers', 'containers.id', 'manifests.container_id')
            ->leftJoin('voyage_schedules', 'voyage_schedules.id', 'containers.voyage_schedule_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
            ->leftJoin('delivery_order_drivers as dod', function ($join) {
                $join->on('dod.id', 'delivery_manifests.delivery_order_driver_id');
                $join->where('dod.status', '<', 3);
            })
            ->leftJoin('job_statuses', 'job_statuses.id', 'dod.job_status_id')
            ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
            ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
            ->leftJoin(DB::raw('(select count(*) as total_item, header_id from manifest_details group by header_id) as md'), 'md.header_id', 'manifests.id')
            ->selectRaw('COUNT(*) as count, job_statuses.name')
            ->whereIn('job_statuses.id', [2, 11, 14])
            ->groupBy('job_statuses.name')
            ->whereRaw($wr)
            ->selectRaw('
        manifests.id,
        manifests.code,
        manifests.date_manifest,
        manifests.created_at,
        if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
        if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan,
        routes.name as trayek,
        companies.name as company,
        job_statuses.name as job_status,
        dod.code as code_sj,
        containers.container_no,
        voyage_schedules.voyage,
        md.total_item,
        if(manifests.is_full=1,\'FCL\',\'LCL\') as tipe_angkut
        ');
        $params = [];
        $params['show_in_index'] = 1;
        $additionalFields = AdditionalField::indexKey('manifest', $params);
        if (count($additionalFields) > 0) {
            $addon = '';
            foreach ($additionalFields as $a) {
                $addon .= ', ';
                $addon .= "REPLACE(JSON_EXTRACT(manifests.additional, '$.$a'), '\"', '') AS $a";
            }
            $additionals = "(SELECT id $addon FROM manifests) AS additional_manifests";
            $item = $item->leftJoin(DB::raw($additionals), 'additional_manifests.id', 'manifests.id');
            foreach ($additionalFields as $a) {
                $item = $item->addSelect(['additional_manifests.' . $a]);
            }
        }
        $item->orderBy('manifests.code');
        $item->orderBy('manifests.date_manifest');
        $item->orderBy('companies.name');
        return DataTables::of($item)
            ->addColumn('action', function ($item) {
                $html = "<a ng-show=\"roleList.includes('operational.manifest.container.detail')\" ui-sref='operational.manifest_fcl.show({id:$item->id})' ><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
                if ($item->total_item < 1) {
                    $html .= "<a ng-show=\"roleList.includes('operational.manifest.container.delete')\" ng-click='deletes($item->id)' ><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
                }
                return $html;
            })
            ->filterColumn('tipe_angkut', function ($query, $keyword) {
                $sql = "if(manifests.is_full=1,'FCL','LCL') like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->editColumn('job_status', function ($item) {
                if (!$item->job_status) {
                    return "Manifest";
                }
                return $item->job_status;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function customer_job_order_datatable(Request $request)
    {
        $item = CustomerJobOrder::all();
        return DataTables::of($item)
            ->editColumn('status', function ($item) {
                $statuses = [
                    1 => 'Draft',
                    2 => 'Job Order',
                    3 => 'Ditolak',
                ];
                return $statuses[$item->status] ?? '-';
            })
            ->make(true);
    }
    public function get_chart_jo_status()
    {
        $finish = DB::table('job_orders')
            ->leftJoin('companies', 'companies.id', 'job_orders.company_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('contacts AS receivers', 'receivers.id', 'job_orders.receiver_id')
            ->leftJoin('contacts AS senders', 'senders.id', 'job_orders.sender_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
            ->leftJoin('quotations', 'quotations.id', '=', 'job_orders.quotation_id')
            ->leftJoin('work_orders', 'work_order_id', '=', 'work_orders.id')
            ->where('kpi_statuses.name', '=', 'Finish')
            ->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)')
            ->selectRaw('COUNT(*) as count, kpi_statuses.name')
            ->groupBy('kpi_statuses.name')
            ->get();
        $proses = DB::table('job_orders')
            ->leftJoin('companies', 'companies.id', 'job_orders.company_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('contacts AS receivers', 'receivers.id', 'job_orders.receiver_id')
            ->leftJoin('contacts AS senders', 'senders.id', 'job_orders.sender_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
            ->leftJoin('quotations', 'quotations.id', '=', 'job_orders.quotation_id')
            ->leftJoin('work_orders', 'work_order_id', '=', 'work_orders.id')
            ->where('kpi_statuses.name', '!=', 'Finish')
            ->where('kpi_statuses.name', '!=', 'Draft')
            ->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)')
            ->selectRaw('COUNT(*) as count, kpi_statuses.name')
            ->get();
        $data = [
            'finish' => $finish->pluck('count'),
            'proses' => $proses->pluck('count'),
        ];
        return $data;
    }
    public function get_jo_status(){
        $summaries = DB::table('job_orders')
        ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
        ->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)')
        ->whereYear('job_orders.created_at', '=', date('Y'))
        ->selectRaw('MONTH(job_orders.created_at) AS month,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN 1 END), 0) AS finish,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN 1 END), 0) AS process,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN job_orders.total_price END), 0) AS summaryFinish,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN job_orders.total_price END), 0) AS summaryProcess,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN 1 END), 0) + COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN 1 END), 0) AS totalJobOrder,
            COALESCE(SUM(job_orders.total_price), 0) AS totalSummary')
        ->groupBy('month')
        ->get();    
        // $response=[];
        // $months = ['x', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        // foreach ($summaries as $key => $row) {
        //     $response[] = [
        //         'month' => $months[$row->month],
        //         'data' => $row,
        //     ];
        // }
        // return $response;
        return Response::json($summaries, 200, [], JSON_NUMERIC_CHECK);
    }
    public function grafik_jo_year()
    {
        $item = DB::table('job_orders')
            ->whereYear('created_at', '=', date('Y-m-d'))
            ->where('job_orders.is_cancel', 0)
            ->selectRaw('COUNT(*) as count, MONTH(created_at) month')
            ->groupBy('month')
            ->get();
        $months = ['x', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        $response = [];
        $count = [];
        $count[] = 'Total JO';
        foreach ($item as $value) {
            $response[] = [
                'month' => $months[$value->month],
                'count' => $value->count,
            ];
        }
        foreach ($months as $key => $month) {
            $condition = true;
            if ($key !== 0) {
                foreach ($response as $item) {
                    if ($month == $item['month']) {
                        $count[] = (int) $item['count'];
                        $condition = false;
                        continue;
                    }
                }
                if ($condition === true) {
                    $count[] = 0;
                }
            }
        }
        $item2 = DB::table('job_orders')
            ->join('invoice_job_order', 'job_orders.id', 'invoice_job_order.job_order_id')
            ->join('invoices', 'invoices.id', 'invoice_job_order.invoice_id')
            ->whereYear('job_orders.created_at', '=', date('Y-m-d'))
            ->selectRaw('COUNT(*) as count, MONTH(job_orders.created_at) month')
            ->groupBy('month')
            ->where('job_orders.is_cancel', 0)
            ->get();
        $response2 = [];
        $count2 = [];
        $count2[] = 'Total JO Invoices';
        foreach ($item2 as $value) {
            $response2[] = [
                'month' => $months[$value->month],
                'count2' => $value->count,
            ];
        }
        foreach ($months as $key => $month) {
            $condition = true;
            if ($key !== 0) {
                foreach ($response2 as $item) {
                    if ($month == $item['month']) {
                        $count2[] = (int) $item['count2'];
                        $condition = false;
                        continue;
                    }
                }
                if ($condition === true) {
                    $count2[] = 0;
                }
            }
        }
        $response = [
            'months' => $months,
            'count' => $count,
            'count2' => $count2,
        ];
        return $response;
    }
    public function delivery_order_driver_dashboard()
    {
        $item = DB::table('delivery_order_drivers')
        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
        ->leftJoin('manifests', 'manifests.id', '=', 'delivery_manifests.manifest_id')
        ->leftJoin('companies', 'companies.id', '=', 'manifests.company_id')
        ->leftJoin('routes', 'routes.id', '=', 'manifests.route_id')
        ->leftJoin('job_statuses', 'job_statuses.id', '=', 'delivery_order_drivers.job_status_id')
        ->leftJoin('contacts as driver', 'driver.id', '=', 'delivery_order_drivers.driver_id')
        ->leftJoin('vehicles', 'vehicles.id', '=', 'delivery_order_drivers.vehicle_id')
        ->where('delivery_order_drivers.vehicle_id', '<>', '')
        ->selectRaw('ROUND(
            CASE
                WHEN delivery_order_drivers.journey_distance IS NOT NULL AND delivery_order_drivers.journey_distance <> 0
                    THEN delivery_order_drivers.journey_distance
                ELSE vehicles.last_km_gps
            END, 2) as sum')
        ->selectRaw('COUNT(delivery_order_drivers.id) as qty')
        ->addSelect('vehicles.nopol')
        ->groupBy('vehicles.nopol')
        ->orderByDesc('qty')
        ->limit(5)
        ->get();
        return $item;
    }
    public function get_precentage_jo()
    {
        $year = DB::table('job_orders')
        ->where('job_orders.is_cancel', 0)
        ->whereYear('created_at', '=', Carbon::parse(date('Y-m-d'))->year)
        ->count();
        // $year = DB::table('job_orders')
        //     ->whereYear('created_at', '=', date('Y-m-d'))
        //     ->count();
        $month = DB::table('job_orders')
            ->where('job_orders.is_cancel', 0)
            ->whereMonth('created_at', '=', Carbon::parse(date('Y-m-d'))->month)
            ->count();
        // $month = DB::table('job_orders')
        //     ->whereMonth('created_at', '=', date('Y-m-d'))
        //     ->count();
        $today = DB::table('job_orders')
            ->where('job_orders.is_cancel', 0)
            ->whereDate('created_at', '=', Carbon::parse(date('Y-m-d')))
            ->count();
        $month_invoices = DB::table('job_orders')
            ->where('job_orders.is_cancel', 0)
            ->join('invoice_job_order', 'job_orders.id', 'invoice_job_order.job_order_id')
            ->join('invoices', 'invoices.id', 'invoice_job_order.invoice_id')
            ->whereMonth('job_orders.created_at', '=', Carbon::parse(date('Y-m-d'))->month)
            ->count();
        $invoice_year = DB::table('job_orders')
            ->where('job_orders.is_cancel', 0)
            ->join('invoice_job_order', 'job_orders.id', 'invoice_job_order.job_order_id')
            ->join('invoices', 'invoices.id', 'invoice_job_order.invoice_id')
            ->whereYear('job_orders.created_at', '=', Carbon::parse(date('Y-m-d'))->year)
            ->count();
        $revenue = DB::table('job_orders')
            ->where('job_orders.is_cancel', 0)
            ->whereDate('job_orders.created_at', '=', Carbon::parse(date('Y-m-d')))
            ->sum('total_price');
        return [
            'year' => $year,
            'month' => $month,
            'today' => $today,
            'invoice_year' => $invoice_year,
            'month_invoices' => $month_invoices,
            'revenue' => $revenue,
        ];
    }
    public function manifest_activity_dashboard(Request $request)
    {
        $wr = "1=1";
        $wr .= Contact::filterUser(auth()->user()->company_id, $where = " AND companies.id = ");
        $costs = DB::raw("(
      SELECT SUM(IFNULL(mc.total_price,0)) as cost_total,mc.header_id as manifest_id
      FROM manifest_costs as mc
      GROUP BY mc.header_id
      ) as costs");
        $item = DB::table('manifests')
            ->leftJoin('manifest_details', 'manifest_details.header_id', 'manifests.id')
            ->leftJoin('container_types', 'container_types.id', 'manifests.container_type_id')
            ->leftJoin('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
            ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
            ->leftJoin('companies', 'companies.id', 'manifests.company_id')
            ->leftJoin($costs, 'costs.manifest_id', 'manifests.id')
            ->leftJoin('vehicles', 'vehicles.id', 'manifests.vehicle_id')
            ->leftJoin('routes', 'routes.id', 'manifests.route_id')
            ->leftJoin('contacts as driver', 'driver.id', 'manifests.driver_id')
            ->where('job_orders.is_cancel', 0)
            ->select(
                'companies.name as company_name',
                'manifests.id as m_id',
                'manifests.code',
                'manifests.is_full',
                'manifests.container_type_id',
                'manifests.vehicle_type_id',
                'manifests.date_manifest',
                'container_types.name',
                'manifests.revenue as revenue_total',
                'vehicles.nopol',
                'routes.name as route_name',
                'driver.name as driver_name',
                'costs.cost_total',
                'job_order_details.imposition',
                'job_orders.service_type_id'
            );
        return DataTables::of($item)
            ->make(true);
    }
    public function invoice_jual_dashboard()
    {
        $item = DB::table('invoices')
            ->join('contacts', 'contacts.id', 'invoices' . '.customer_id')
            ->join('companies', 'companies.id', 'invoices' . '.company_id')
            ->leftjoin(DB::raw('(select GROUP_CONCAT(distinct job_orders.aju_number SEPARATOR ", ") as aju,GROUP_CONCAT(distinct job_orders.no_bl SEPARATOR ", ") as bl,GROUP_CONCAT(distinct work_orders.code SEPARATOR  ", ") as code_wo, header_id from invoice_details left join job_orders on job_orders.id = invoice_details.job_order_id left join work_orders on work_orders.id = job_orders.work_order_id group by invoice_details.header_id) as Y'), 'Y.header_id', 'invoices.id');
        // Filter customer, wilayah, status, dan periode
        $tgl_awal = '';
        $tgl_akhir = '';
        $item = $tgl_awal != '' && $tgl_akhir != '' ? $item->whereBetween('date_invoice', [$tgl_awal, $tgl_akhir]) : $item;
        $company_id = '';
        $item = $company_id != '' ? $item->where('company_id', $company_id) : $item;
        $customer_id = '';
        $item = $customer_id != '' ? $item->where('customer_id', $customer_id) : $item;
        $status = '';
        $item = $status != '' ? $item->where('status', $status) : $item;
        $item = $item
            ->select(
                'invoices.*',
                'companies.name AS company_name',
                'code_wo',
                'contacts.name AS customer_name',
                'Y.aju',
                'Y.bl',
                DB::raw('IF(invoices.status = 1, "Diajukan", IF(invoices.status = 2, "Disetujui", IF(invoices.status = 3, "Invoice", IF(invoices.status = 4, "Terbayar Sebagian", IF(invoices.status = 5, "Lunas", "-"))))) as invoice_status'),
                DB::raw("(grand_total+grand_total_additional) as total")
            );
        // if (auth()->user()->is_admin==0) {
        //     $item = $item->where('invoices.company_id', auth()->user()->company_id);
        // }
        $wr = "1=1";
        $wr .= Contact::filterUser(auth()->user()->company_id, $where = " AND invoices.company_id = ");
        $item = $item->whereRaw($wr);
        return DataTables::of($item)
            ->editColumn('created_at', function ($item) {
                return dateView($item->created_at);
            })
            ->editColumn('total', function ($item) {
                return formatNumber($item->total);
            })
            ->filterColumn('aju', function ($query, $keyword) {
                $sql = "Y.aju like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('bl', function ($query, $keyword) {
                $sql = "Y.bl like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->filterColumn('total', function ($query, $keyword) {
                $sql = "(grand_total+grand_total_additional) like ?";
                $query->whereRaw($sql, ["%{$keyword}%"]);
            })
            ->addColumn('status_name', function ($item) {
                $stt = [
                    1 => 'Diajukan',
                    2 => 'Draft',
                    3 => 'Posting',
                    4 => 'Terbayar Sebagian',
                    5 => 'Lunas',
                    6 => 'Void',
                ];
                return $stt[$item->status];
            })
            ->rawColumns(['action', 'action_customer', 'aju'])
            ->toJson();
    }
    public function get_jo_status_year(){
        $summaries = DB::table('job_orders')
        ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
        ->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)')
        ->whereYear('job_orders.created_at', '=', date('Y'))
        ->selectRaw('YEAR(job_orders.created_at) AS year,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN 1 END), 0) AS finish,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN 1 END), 0) AS process,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN job_orders.total_price END), 0) AS summaryFinish,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN job_orders.total_price END), 0) AS summaryProcess,
            COALESCE(SUM(CASE WHEN kpi_statuses.NAME = "Finish" THEN 1 END), 0) + COALESCE(SUM(CASE WHEN kpi_statuses.NAME != "Finish" THEN 1 END), 0) AS totalJobOrder,
            COALESCE(SUM(job_orders.total_price), 0) AS totalSummary')
        ->groupBy('year')
        ->get();  
        // $response=[];
        // $months = ['x', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        // foreach ($summaries as $key => $row) {
        //     $response[] = [
        //         'month' => $months[$row->month],
        //         'data' => $row,
        //     ];
        // }
        // return $response;
        return Response::json($summaries, 200, [], JSON_NUMERIC_CHECK);
    }
    public function get_chart_jo_month()
    {
        $item = DB::table('job_orders')
            ->leftJoin('companies', 'companies.id', 'job_orders.company_id')
            ->leftJoin('contacts', 'contacts.id', 'job_orders.customer_id')
            ->leftJoin('contacts AS receivers', 'receivers.id', 'job_orders.receiver_id')
            ->leftJoin('contacts AS senders', 'senders.id', 'job_orders.sender_id')
            ->leftJoin('services', 'services.id', 'job_orders.service_id')
            ->leftJoin('routes', 'routes.id', 'job_orders.route_id')
            ->leftJoin('service_types', 'service_types.id', '=', 'services.service_type_id')
            ->leftJoin('kpi_statuses', 'kpi_statuses.id', '=', 'job_orders.kpi_id')
            ->leftJoin('quotations', 'quotations.id', '=', 'job_orders.quotation_id')
            ->leftJoin('work_orders', 'work_order_id', '=', 'work_orders.id')
            ->whereRaw('job_orders.id NOT IN (SELECT job_order_id FROM sales_orders WHERE job_order_id IS NOT NULL)')
            ->whereYear('job_orders.created_at', '=', date('Y'))
            ->selectRaw('COUNT(*) as count, MONTH(job_orders.created_at) month')
            ->groupBy('month')
            ->get();
        $months = ['x', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        $response = [];
        $count = [];
        $count[] = 'Total JO';
        foreach ($item as $value) {
            $response[] = [
                'month' => $months[$value->month],
                'count' => $value->count,
            ];
        }
        foreach ($months as $key => $month) {
            $condition = true;
            if ($key !== 0) {
                foreach ($response as $item) {
                    if ($month == $item['month']) {
                        $count[] = (int) $item['count'];
                        $condition = false;
                        continue;
                    }
                }
                if ($condition === true) {
                    $count[] = 0;
                }
            }
        }
        $response = [
            'months' => $months,
            'count' => $count,
        ];
        return $response;
    }
    // web landing page
    public function trackDeliveryOrderStatus(Request $request)
    {
        $do = DeliveryOrderDriver::with('job_status:id,name')->where('code', $request->code)->first();
        $manifest = DB::table('manifests')->where('code', $request->code)->first();
        if ($do) {
            $data['status'] = DeliveryOrderStatusLog::index($do->id);
            $data['detail_do'] = $do;
            $job_order = DB::table('delivery_manifests')
                ->where('delivery_order_driver_id', $do->id)
                ->leftJoin('manifest_details', 'manifest_details.header_id', 'delivery_manifests.manifest_id')
                ->leftJoin('manifests', 'manifests.id', 'delivery_manifests.manifest_id')
                ->leftJoin('routes', 'routes.id', 'manifests.route_id')
                ->leftJoin('job_order_details', 'job_order_details.id', 'manifest_details.job_order_detail_id')
                ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
                ->leftJoin('services', 'services.id', 'job_orders.service_id')
                ->selectRaw('
             services.name as service_name, routes.name as route_name')
                ->first();
            $data['detail_jo'] = $job_order;
            $data['from'] = DOD::showOrigin($do->id);
            $data['to'] = DOD::showReceiver($do->id);
            $data['route'] = DOD::getDeliveryRoute($do->id);
            $data['is_delivery'] = true;
        } else if ($manifest) {
            $data['detail'] = DB::table('manifest_details')
                ->leftJoin('manifests', 'manifests.id', 'manifest_details.header_id')
                ->where('header_id', $manifest->id)->select('job_order_detail_id')->get();
            $data['job_order'] = DB::table('job_order_details')
                ->leftJoin('job_orders', 'job_orders.id', 'job_order_details.header_id')
                ->leftJoin('services', 'services.id', 'job_orders.service_id')
                ->where('job_order_details.id', @$data['detail'][0]->job_order_detail_id)
                ->select('services.name as service_name')
                ->first();
            $data['detail_do'] = DB::table('delivery_order_drivers as dod')
                ->leftJoin('contacts as driver', 'driver.id', 'dod.driver_id')
                ->leftJoin('contacts as co_driver', 'co_driver.id', 'dod.co_driver_id')
                ->leftJoin('vehicles', 'vehicles.id', 'dod.vehicle_id')
                ->leftJoin('delivery_manifests', 'dod.id', 'delivery_manifests.delivery_order_driver_id')
                ->leftJoin('manifests', 'manifests.id', 'delivery_manifests.manifest_id')
                ->leftJoin('routes', 'routes.id', 'manifests.route_id')
                ->leftJoin('job_statuses', 'job_statuses.id', 'manifests.job_status_id')
                ->where('delivery_manifests.manifest_id', $manifest->id)
                ->where('dod.status', '<', 3)
                ->selectRaw('
            dod.id,
            manifests.code as code,
            dod.pick_date,
            routes.name as route_name,
            job_statuses.name as status,
            if(dod.driver_id is not null,driver.name, if(dod.driver_name is not null, dod.driver_name, dod.driver_eksternal)) as sopir,
            if(dod.co_driver_id is not null,co_driver.name, dod.co_driver) as co_driver,
            if(dod.vehicle_id is not null,vehicles.nopol, if(dod.nopol is not null, dod.nopol, dod.vehicle_eksternal)) as kendaraan')->first();
            $data['from'] = DOD::showOrigin(@$data['detail_do']->id);
            $data['to'] = DOD::showReceiver(@$data['detail_do']->id);
            $data['status'] = DB::table('manifest_status_logs')
                ->leftJoin('job_statuses', 'job_statuses.id', 'manifest_status_logs.job_status_id')
                ->leftJoin('users', 'users.id', 'manifest_status_logs.created_by')
                ->where('manifest_status_logs.manifest_id', $manifest->id)
                ->select('job_statuses.name as status', 'manifest_status_logs.created_at as dibuat', 'users.name as pembuat')
                ->orderBy('manifest_status_logs.created_at', 'desc')
                ->get();
            $data['is_delivery'] = false;
        } else {
            // throw new Exception('Data not found'); 
            return null;
        }
        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function landingPageCity()
    {
        $data = City::get();
        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function landingPageService()
    {
        $data = Service::select('id', 'name')->get();
        return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function cekTarif(Request $request)
    {
        $route = DB::table('routes')->where('city_to', $request->city_to)->where('city_from', $request->city_from)->select('id')->first();
        if ($route) {
            $data = DB::table('price_lists')->where('route_id', $route->id)->where('service_id', $request->service_id)->select('code', 'name', 'price_tonase', 'price_volume', 'price_item', 'price_full')->get();
            $count = DB::table('price_lists')->where('route_id', $route->id)->where('service_id', $request->service_id)->select('id')->count();
            if ($count != 0) {
                return response()->json($data, 200, [], JSON_NUMERIC_CHECK);
            } else {
                throw new Exception('Data not found');
                return null;
            }
        } else {
            throw new Exception('Data not found');
            return null;
        }
    }

    public function gross_profit_vs_cost_of_sales(Request $request)
    {
        $dt = DB::table('work_orders')
            ->leftJoin('work_types', 'work_types.id', '=', 'work_orders.work_type_id')
            ->leftJoin('contacts as customer', 'customer.id', '=', 'work_orders.customer_id')
            
            // Subquery untuk jasa_jo
            ->leftJoinSub(function ($query) {
                $query->from('job_orders as a')
                    ->join('services as b', 'b.id', '=', 'a.service_id')
                    ->join('invoice_details as c', 'c.job_order_id', '=', 'a.id')
                    ->join('work_orders as d', 'd.id', '=', 'a.work_order_id')
                    ->join('work_types as e', 'e.id', '=', 'd.work_type_id')
                    ->leftJoinSub(function($q) {
                        $q->from('invoice_taxes as z')
                            ->join('taxes as x', 'x.id', 'z.tax_id')
                            ->where('x.is_ppn', 1)
                            ->selectRaw('
                                z.invoice_detail_id,
                                SUM(z.amount) as amount
                            ');
                    }, 'ppn', 'ppn.invoice_detail_id', 'c.id')
                    ->where('c.is_other_cost', 0)
                    ->selectRaw('
                        a.work_order_id,
                        SUM(CASE WHEN b.service_type_id = 6 AND e.id IN (1,2,3,4,5,7) THEN c.total_price + COALESCE(ppn.amount, 0) ELSE 0 END) as total_price_trasport, 
                        SUM(CASE WHEN b.service_group_id = 5 AND e.id IN (1,2,3,4,5,7) THEN c.total_price + COALESCE(ppn.amount, 0) ELSE 0 END) as total_price_angkut
                    ')
                    ->groupBy('a.work_order_id');
            }, 'jasa_jo', 'jasa_jo.work_order_id', '=', 'work_orders.id')

            // Subquery untuk cost_jo
            ->leftJoinSub(function ($query) {
                $query->from('invoice_details as c')
                    ->join('job_orders as b', 'b.id', '=', 'c.job_order_id')
                    ->join('job_order_costs as a', 'a.header_id', '=', 'b.id')
                    // ->whereIn('a.status', [5, 10])
                    ->where('c.is_other_cost', 1)
                    ->where('a.type', 2)
                    ->selectRaw('
                        b.work_order_id,
                        SUM(DISTINCT c.total_price) as cost_reimburse
                    ')
                    ->groupBy('b.work_order_id');
            }, 'cost_jo', 'cost_jo.work_order_id', '=', 'work_orders.id')

            // Subquery untuk cost_jasa_jo
            ->leftJoinSub(function ($query) {
                $query->from('job_order_costs as a')
                    ->join('job_orders as b', 'b.id', '=', 'a.header_id')
                    ->join('services as s', 's.id', '=', 'b.service_id')
                    ->join('cost_types as c', 'c.id', '=', 'a.cost_type_id')
                    ->join('work_orders as d', 'd.id', '=', 'b.work_order_id')
                    ->join('work_types as e', 'e.id', '=', 'd.work_type_id')
                    ->leftJoin('invoice_details as f', function($join) {
                        $join->on('f.job_order_id', '=', 'b.id')
                             ->where('f.is_other_cost', 1);
                    })
                    ->whereIn('a.status', [5, 10])
                    ->selectRaw('
                        b.work_order_id,
                        SUM(DISTINCT CASE WHEN s.service_type_id = 6 AND c.parent_id IN (1,2,3,4,811) AND e.id IN (1,2,3,4,5,7) AND a.type = 1 THEN a.total_real ELSE 0 END) as cost_jasa_transport, 
                        SUM(CASE WHEN s.service_group_id = 5 AND c.parent_id IN (3,5,6,7,8) AND e.id IN (1,2,3,4,5,7) AND a.type = 1 THEN a.total_real ELSE 0 END) as cost_jasa_ongkos,
                        SUM(DISTINCT CASE WHEN e.id IN (1,2,3,4,5,7) AND a.type = 2 THEN f.total_price ELSE NULL END) as hpp_cost_reimburse
                    ')
                    ->groupBy('b.work_order_id');
            }, 'cost_jasa_jo', 'cost_jasa_jo.work_order_id', '=', 'work_orders.id');

        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $dt->where('work_orders.company_id', auth()->user()->company_id);
        }

        if ($request->company_id) {
            $dt->where('work_orders.company_id', $request->company_id);
        }

        if ($request->customer_id) {
            $dt->where('work_orders.customer_id', $request->customer_id);
        }

        if ($request->start_date && $request->end_date) {
            $start_date = date('Y-m-d', strtotime($request->start_date));
            $end_date = date('Y-m-d', strtotime($request->end_date));
            $dt->whereBetween('work_orders.date', [$start_date, $end_date]);
        }

        $dt->selectRaw('
            customer.name as customer_name,
            work_types.name as work_type,
            work_orders.date as wo_date,
            work_orders.code as wo_code,
            jasa_jo.total_price_trasport,
            jasa_jo.total_price_angkut,
            cost_jo.cost_reimburse,
            (jasa_jo.total_price_trasport + jasa_jo.total_price_angkut + COALESCE(cost_jo.cost_reimburse, 0)) as total_sales,
            cost_jasa_jo.cost_jasa_transport,
            cost_jasa_jo.cost_jasa_ongkos,
            COALESCE(cost_jasa_jo.hpp_cost_reimburse, 0) as hpp_cost_reimburse,
            (cost_jasa_jo.cost_jasa_transport + cost_jasa_jo.cost_jasa_ongkos + COALESCE(cost_jasa_jo.hpp_cost_reimburse, 0)) as total_cost_sales
        ');

        return DataTables::of($dt)
            ->addIndexColumn()
            ->addColumn('gross_profit', function ($item) {
                return $item->total_sales - $item->total_cost_sales;
            })
            ->addColumn('presentase_gross_profit', function ($item) {
                $total_price = $item->total_price_trasport + $item->total_price_angkut;
                return $total_price > 0 ? round(($item->total_sales - $item->total_cost_sales) / $total_price * 100, 2) . '%' : '0%';
            })
            ->make(true);
    }

    public function work_order_management(Request $request)
    {
        $work_order_detail = DB::table('work_order_details')
                    ->selectRaw('
                        work_order_details.header_id as work_order_id,
                        CASE WHEN COUNT(DISTINCT work_order_details.id) > 0 THEN 1 ELSE 0 END AS has_wod
                    ')
                    ->groupBy('header_id');

        $container = DB::table('containers')
                    ->selectRaw('
                        containers.work_order_id,
                        CASE WHEN COUNT(DISTINCT containers.id) > 0 THEN 1 ELSE 0 END AS has_container
                    ')
                    ->groupBy('work_order_id');

        $job_order = DB::table('job_orders')
                    ->leftJoin('manifests', 'manifests.job_order_id', 'job_orders.id')
                    ->leftJoin('delivery_manifests', 'delivery_manifests.manifest_id', 'manifests.id')
                    ->leftJoin('delivery_order_drivers', 'delivery_order_drivers.id', 'delivery_manifests.delivery_order_driver_id')
                    ->selectRaw('
                        job_orders.work_order_id,
                        CASE WHEN COUNT(DISTINCT job_orders.id) > 0 THEN 1 ELSE 0 END AS has_job_order,
                        CASE 
                            WHEN COUNT(DISTINCT manifests.id) = COUNT(DISTINCT job_orders.id) THEN 1
                            ELSE 0
                        END AS has_all_manifest,
                        CASE 
                            WHEN COUNT(DISTINCT delivery_order_drivers.id) = COUNT(DISTINCT job_orders.id) THEN 1
                            ELSE 0
                        END AS has_all_delivery_order
                    ')
                    ->groupBy('job_orders.work_order_id');
        
        $cash_advance = DB::table('cash_advances')
                        ->selectRaw('
                            cash_advances.work_order_id,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (2,3,4,5) OR status >= 2 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_ajukan,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (3,4,5) OR status >= 2 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_manager,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (4,5) OR status >= 2 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_manager_opr,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id = 5 OR status >= 2 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_direktur,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN (status = 3 OR status = 6 OR status = 8) THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_uang_keluar,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (2,3,4,5) OR status >= 3 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_ajukan_realisasi,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (3,4,5) OR status >= 3 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_manager_realisasi,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id IN (4,5) OR status >= 3 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_manager_opr_realisasi,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN status_approval_id = 5 OR status >= 3 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_approve_direktur_realisasi,
                            CASE 
                                WHEN COUNT(*) = SUM(CASE WHEN (status = 3 OR status = 6 OR status = 8) AND status_approval_id = 5 THEN 1 ELSE 0 END)
                                THEN 1 ELSE 0 
                            END AS all_realisasi
                        ')
                        ->groupBy('cash_advances.work_order_id');

        $sub = DB::table('invoice_details')
            ->join('invoices', 'invoices.id', 'invoice_details.header_id')
            ->selectRaw('
                invoice_details.job_order_id,
                invoices.type,
                invoices.status,
                invoices.due_date
            ')
            ->groupBy('invoice_details.job_order_id', 'invoices.type', 'invoices.status', 'invoices.due_date');

        $invoice = DB::table(DB::raw("({$sub->toSql()}) as sub"))
            ->mergeBindings($sub)
            ->join('job_orders', 'job_orders.id', 'sub.job_order_id')
            ->selectRaw('
                job_orders.work_order_id,

                -- Semua JO dalam WO sudah punya invoice (type = 1)
                CASE 
                    WHEN COUNT(DISTINCT job_orders.id) = 
                        COUNT(DISTINCT CASE WHEN sub.type = 1 THEN job_orders.id END)
                    THEN 1 ELSE 0
                END AS all_invoiced,

                -- Semua JO dalam WO sudah punya reimburse (type = 2)
                CASE 
                    WHEN COUNT(DISTINCT job_orders.id) = 
                        COUNT(DISTINCT CASE WHEN sub.type = 2 THEN job_orders.id END)
                    THEN 1 ELSE 0
                END AS all_reimbursed,

                -- Semua invoice dari semua JO sudah di-approve (status = 2)
                CASE 
                    WHEN COUNT(*) = 
                        SUM(CASE WHEN sub.status >= 2 THEN 1 ELSE 0 END)
                    THEN 1 ELSE 0
                END AS all_approved,

                -- Semua invoice dari semua JO sudah di-posting (status = 3)
                CASE 
                    WHEN COUNT(*) = 
                        SUM(CASE WHEN sub.status >= 3 THEN 1 ELSE 0 END)
                    THEN 1 ELSE 0
                END AS all_posted,

                -- Ada aging invoice (type = 1 lewat due date)
                CASE 
                    WHEN COUNT(CASE WHEN sub.type = 1 AND sub.due_date < CURRENT_DATE THEN 1 END) > 0
                    THEN 1 ELSE 0
                END AS is_invoice_aging,

                -- Ada aging reimburse (type = 2 lewat due date)
                CASE 
                    WHEN COUNT(CASE WHEN sub.type = 2 AND sub.due_date < CURRENT_DATE THEN 1 END) > 0
                    THEN 1 ELSE 0
                END AS is_reimburse_aging
            ')
            ->groupBy('job_orders.work_order_id');

        $dt = DB::table('work_orders')
            ->leftJoinSub($work_order_detail, 'work_order_detail', 'work_order_detail.work_order_id', 'work_orders.id')
            ->leftJoinSub($container, 'container', 'container.work_order_id', 'work_orders.id')
            ->leftJoinSub($job_order, 'job_order', 'job_order.work_order_id', 'work_orders.id')
            ->leftJoinSub($cash_advance, 'cash_advance', 'cash_advance.work_order_id', 'work_orders.id')
            ->leftJoinSub($invoice, 'invoice', 'invoice.work_order_id', 'work_orders.id')
            ->selectRaw('
                work_orders.id,
                work_orders.code,
                work_orders.date,
                work_order_detail.has_wod,
                container.has_container,
                job_order.has_job_order,
                job_order.has_all_manifest,
                job_order.has_all_delivery_order,
                cash_advance.all_ajukan,
                cash_advance.all_approve_manager,
                cash_advance.all_approve_manager_opr,
                cash_advance.all_approve_direktur,
                cash_advance.all_uang_keluar,
                cash_advance.all_ajukan_realisasi,
                cash_advance.all_approve_manager_realisasi,
                cash_advance.all_approve_manager_opr_realisasi,
                cash_advance.all_approve_direktur_realisasi,
                cash_advance.all_realisasi,
                invoice.all_invoiced,
                invoice.all_reimbursed,
                invoice.all_approved,
                invoice.all_posted,
                invoice.is_invoice_aging,
                invoice.is_reimburse_aging
            ');

        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0) {
            $dt->where('work_orders.company_id', auth()->user()->company_id);
        }

        return DataTables::of($dt)
            ->addIndexColumn()
            ->make(true);
    }
}
