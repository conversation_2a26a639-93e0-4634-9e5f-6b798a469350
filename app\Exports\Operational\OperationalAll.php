<?php
namespace App\Exports\Operational;
/*
    Update : 8 September 2022
    Keterangan :  Membuat File Export Excel untuk Menu Operational All [Finance Accounting]
    PIC : <PERSON>
*/

/*  Date : 10-09-2022 s/d 13-09-2022 By : <PERSON> Rizky
    Description : [Improve] Report Operational : Operational All [untuk dept Finance Accounting]
    Keterangan : Menambah kolom baru pada export excel Operational All[Finance Accounting]
*/

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OperationalAll implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    /**
    * @return \Illuminate\Support\Collection
    */

    public function collection()
    {
        $report_controller = new \App\Http\Controllers\Operational\ReportController;
        return $report_controller->getAllReport($this->request);
    }
    public function headings(): array
    {
        return [
            'Branch',
            'No. WO',
            'Tgl. WO',
            'Customer',
            // 'Operational',
            // 'Reimburshment',
            'Invoice WO',
            'Presentase',
            'No. Invoice',
            'Tgl Invoice',
            'No JO',
            'Tgl JO',
            'Service',
            'Origin',
            'Destination',
            'Revenue',
            'Cost',
            // 'Base Price',
            'Profit',
            'Opr. JO',
            'Code Surat Jalan',
            'No. Manifest',
            'Tgl. Manifest',
            'Vehicle',
            'Rute',
            'Driver',
            'Cost Driver'
        ];
    }
    public function map($data): array
    {
        if($data->opr_jo == 1) {
            $opr_jo_final = 'Finish';
        } elseif($data->opr_jo == 0) {
            $opr_jo_final = 'Job Order';
        } else {
            $opr_jo_final = '-';
        }

        return [
            $data->branch ?? '-',
            $data->code ?? '-',
            $data->date_wo ?? '-',
            $data->customer_name ?? '-',
            // number_format($data->operational_cost,2,",",".") ?? '-',
            // number_format($data->talangan_price,2,",",".") ?? '-',
            number_format($data->profit,2,",",".") ?? '-',
            $data->presentase.".00%" ?? '-',
            $data->code_invoice ?? '-',
            $data->datin ?? '-',
            $data->no_jo ?? '-',
            $data->date_jo ?? '-',
            $data->service_name ?? '-',
            $data->oad ?? '-',
            $data->dad ?? '-',
            number_format($data->ma_rev,2,",",".") ?? '-',
            number_format($data->mcop,2,",",".") ?? '-',
            // number_format($data->base_price,2,",",".") ?? '-',
            number_format($data->invoice_price,2,",",".") ?? '-',
            $opr_jo_final,
            $data->code_surat_jalan ?? '-',
            $data->manifest ?? '-',
            $data->manifest_date ?? '-',
            $data->nop ?? '-',
            $data->ron ?? '-',
            $data->drv ?? '-',
            number_format($data->cost_driver,2,",",".") ?? '-',
        ];
    }
}
// Close [Improve] Report Operational : Operational All [untuk dept Finance Accounting]
?>