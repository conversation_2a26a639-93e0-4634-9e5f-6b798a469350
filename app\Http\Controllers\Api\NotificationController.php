<?php
namespace App\Http\Controllers\Api;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\Notification;
use App\Model\NotificationUser;
use DB;
use Response;
use Auth;
class NotificationController extends Controller
{
  private function sql_notif_union()
  {
    return "
    
    select id,title,des,url,params,type,date,company_id from notif_type_21
    union
    select id,title,des,url,params,type,date,company_id from notif_type_20
    union
    select id,title,des,url,params,type,date,company_id from notif_type_19
    union
    select id,title,des,url,params,type,date,company_id from notif_type_18
    union
    select id,title,des,url,params,type,date,company_id from notif_type_17
    union
    select id,title,des,url,params,type,date,company_id from notif_type_5
    union
    select id,title,des,url,params,type,date,company_id from notif_type_6
    union
    select id,title,des,url,params,type,date,company_id from notif_type_7
    union
    select id,title,des,url,params,type,date,company_id from notif_type_8
    union
    select id,title,des,url,params,type,date,company_id from notif_type_11
    union
    select id,title,des,url,params,type,date,company_id from notif_type_12
    union
    select id,title,des,url,params,type,date,company_id from notif_type_13
    union
    select id,title,des,url,params,type,date,company_id from notif_type_14
    union
    select id,title,des,url,params,type,date,company_id from notif_type_16
    union
    select id,title,des,url,params,type,date,company_id from notif_type_10
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_29
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_32
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_30
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_31
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_33
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_34
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_35
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_36
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_37
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_38
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_39
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_40
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_41
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_42
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_43
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_44
    union 
    select id,title,des,url,params,type,date,company_id from notif_type_45
    ";
  }

  public function get_notif(Request $request)
  {
    
    $user=DB::table('notification_type_users')->where('user_id', auth()->id())->pluck('notification_type_id')->toArray();
    $typ="0";
    foreach ($user as $value) {
      $typ.=",$value";
    }
    $wr="1=1";
    // $user=DB::table('users')->where('id', auth()->id())->first();
    if (!auth()->user()->is_admin) {
        $wr.=" and y.type in ($typ)";
        $wr.=" and y.company_id = ".auth()->user()->company_id;
    }
    $user_id = Auth::user()->id;
    // DB::statement("SET lc_time_names = 'id_ID'; 
    // SET character_set_client = 'utf8mb4'; 
    // SET character_set_connection = 'utf8mb4'; 
    // SET collation_connection = 'utf8mb4_0900_ai_ci'; 
    // ");

    $limit = '';
    if ($request->limit) {
      $limit = 'limit ' . $request->limit;
    }
    $query_union = $this->sql_notif_union();
    $sql="
    select * from
    ($query_union)
    as y
    where $wr AND `id` NOT IN (SELECT notification_id FROM noticed_notifications WHERE user_id = $user_id AND `type` = y.type)
    order by y.date desc
    $limit
    ";
    // echo($sql);die;
    $item=DB::select($sql);

    $sql2="
    select count(*) as total from
    ($query_union)
    as y
    where $wr AND `id` NOT IN (SELECT notification_id FROM noticed_notifications WHERE user_id = $user_id AND `type` = y.type)
    order by y.date desc
    ";

    $total=DB::select($sql2);
    // $items = DB::table('notification_users')
    //     ->leftJoin('notifications','notifications.id','=','notification_users.notification_id')
    //     ->whereRaw('notification_users.is_read = 0')
    //     ->whereRaw('notification_users.user_id = '.auth()->id())
    //     ->selectRaw('notifications.name as `title`, notifications.route as `url`, '
    //         . 'notifications.description as `desc`, '
    //         . 'notifications.parameter as `params`, '
    //         . 'notifications.created_at as `date`,'
    //         . 'notification_users.id')
    //     ->get();
    //
    // foreach($items as $index => $item){
    //     $items[$index]->params = json_decode($item->params);
    // }
    return Response::json(['item' => $item, 'total' => $total],200,[],JSON_NUMERIC_CHECK);
  }
  public function detail_notification()
  {
    $whereNotifJO = "((notifications.notification_type_id = 6 or notifications.notification_type_id = 7 or notifications.notification_type_id = 8) and notifications.is_done = 0)";
    $whereNotifNonJO = "((notifications.notification_type_id <> 6 and notifications.notification_type_id <> 7 and notifications.notification_type_id <> 8) and notification_users.is_read = 0)";
    $cWhere = " {$whereNotifJO} or {$whereNotifNonJO} ";
    $data['notification']=DB::table('notification_users')
        ->leftJoin('notifications','notifications.id','=','notification_users.notification_id')
        ->where('notification_users.user_id', auth()->id())
        ->whereRaw($cWhere)
        ->select('notifications.*','notification_users.id as user_notif_id')
        ->groupBy('notification_users.notification_id')
        ->orderBy('notifications.created_at','desc')
        ->limit(30)
        ->get();
    return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
  }
  public function get_notif_vendor()
  {
    DB::statement("SET lc_time_names = 'id_ID'; 
    SET character_set_client = 'utf8mb4'; 
    SET character_set_connection = 'utf8mb4'; 
    SET collation_connection = 'utf8mb4_0900_ai_ci'; 
    ");
    // $user=DB::table('notification_type_users')->where('user_id', auth()->id())->pluck('notification_type_id')->toArray();
    // $typ="0";
    // foreach ($user as $value) {
    //   $typ.=",$value";
    // }
    $wr="1=1";
    $user=DB::table('contacts')->where('id', auth()->id())->first();
    if ($user->is_vendor == 1) {
        $wr.=" and y.type in (22,23,24)";
        $wr.=" and y.company_id = ".$user->company_id;
        $wr.=" and y.vendor_id = ".auth()->id();
    }
    // return response()->json($typ);
    
    $user_id = auth()->id();
    $sql="
    select y.* from
    (
    select id,title,des,url,params,type,date,company_id,vendor_id from notif_type_22
    union
    select id,title,des,url,params,type,date,company_id,vendor_id from notif_type_23
    union
    select id,title,des,url,params,type,date,company_id,vendor_id from notif_type_24
    ) as y
    where $wr AND `id` NOT IN (SELECT notification_id FROM noticed_notifications WHERE user_id = $user_id AND `type` = y.type)
    order by y.date desc
    ";
    $item=DB::select($sql);
    // $items = DB::table('notification_users')
    //     ->leftJoin('notifications','notifications.id','=','notification_users.notification_id')
    //     ->whereRaw('notification_users.is_read = 0')
    //     ->whereRaw('notification_users.user_id = '.auth()->id())
    //     ->selectRaw('notifications.name as `title`, notifications.route as `url`, '
    //         . 'notifications.description as `desc`, '
    //         . 'notifications.parameter as `params`, '
    //         . 'notifications.created_at as `date`,'
    //         . 'notification_users.id')
    //     ->get();
    //
    // foreach($items as $index => $item){
    //     $items[$index]->params = json_decode($item->params);
    // }
    return Response::json($item,200,[],JSON_NUMERIC_CHECK);
  }
  public function get_notif_customer()
  {
    DB::statement("SET lc_time_names = 'id_ID'; 
    SET character_set_client = 'utf8mb4'; 
    SET character_set_connection = 'utf8mb4'; 
    SET collation_connection = 'utf8mb4_0900_ai_ci'; 
    ");
    // $user=DB::table('notification_type_users')->where('user_id', auth()->id())->pluck('notification_type_id')->toArray();
    // $typ="0";
    // foreach ($user as $value) {
    //   $typ.=",$value";
    // }
    $wr="1=1";
    $user=DB::table('users')->where('id', auth()->id())->first();
    $wr.=" and y.type in (25,26,27,28)";
    $wr.=" and y.company_id = ".$user->company_id;
    $wr.=" and y.customer_id = ".auth()->id();
    // return response()->json(auth()->id());
    DB::statement("SET lc_time_names = 'id_ID';");
    $user_id = auth()->id();
    $sql="
    select * from
    (
    select id,title,des,url,params,type,date,company_id,customer_id from notif_type_25
    union
    select id,title,des,url,params,type,date,company_id,customer_id from notif_type_26
    union
    select id,title,des,url,params,type,date,company_id,customer_id from notif_type_27
    union
    select id,title,des,url,params,type,date,company_id,customer_id from notif_type_28
    ) as y
    where $wr AND `id` NOT IN (SELECT notification_id FROM noticed_notifications WHERE user_id = $user_id AND `type` = y.type)
    order by y.date desc
    ";
    //echo($sql);die;
    $item=DB::select($sql);
    // $items = DB::table('notification_users')
    //     ->leftJoin('notifications','notifications.id','=','notification_users.notification_id')
    //     ->whereRaw('notification_users.is_read = 0')
    //     ->whereRaw('notification_users.user_id = '.auth()->id())
    //     ->selectRaw('notifications.name as `title`, notifications.route as `url`, '
    //         . 'notifications.description as `desc`, '
    //         . 'notifications.parameter as `params`, '
    //         . 'notifications.created_at as `date`,'
    //         . 'notification_users.id')
    //     ->get();
    //
    // foreach($items as $index => $item){
    //     $items[$index]->params = json_decode($item->params);
    // }
    return Response::json($item,200,[],JSON_NUMERIC_CHECK);
  }
  public function view_notif(Request $request)
  {
    // dd($request);
    // DB::beginTransaction();
    // NotificationUser::find($request->user_notif_id)->update([
    //   'is_read' => 1
    // ]);
    // DB::commit();
    // $dt=DB::table('notification_users')
    // ->leftJoin('notifications','notifications.id','=','notification_users.notification_id')
    // ->where('notification_users.id', $request->user_notif_id)
    // ->whereRaw('notifications.is_done = 0 and notification_users.is_read = 0')
    // ->select('notifications.*','notification_users.id as user_notif_id')
    // ->first();
    DB::table('noticed_notifications')->insert([
        'notification_id' => $request->id,
        'type' => $request->type,
        'user_id' => Auth::user()->id
    ]);
    return Response::json($request, 200, [], JSON_NUMERIC_CHECK);
  }
}
