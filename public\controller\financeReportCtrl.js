app.controller('financeReport', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="<PERSON><PERSON><PERSON>";
  $scope.baseUrl=baseUrl;

  $scope.exportAccount=function() {
    // console.log($scope);
    window.open($scope.baseUrl+'/finance/report/export/account','_blank');
    // $http.get($scope.baseUrl+'/finance/report/export/account').then(function() {
    //   toastr.success('File Downloaded!');
    // })
  }
  $scope.exportUnpaidCost=function() {
    window.open($scope.baseUrl+'/pdf/unpaid_cost','_blank');
  }
  $scope.exportCostBalance=function() {
    window.open($scope.baseUrl+'/pdf/cost_balance','_blank');
  }
  /*
    Date : 05-10-2022 By : Muhammad <PERSON>ryanul Rizky
    Description : 13. Report - Finance Accounting
    Keterangan : routing export excel
  */
  $scope.exportAccountExcel=function() {
    window.open($scope.baseUrl+'/finance/report/export/account_excel','_blank');
  }
  $scope.exportUnpaidCostExcel=function() {
    window.open($scope.baseUrl+'/pdf/unpaid_cost_excel','_blank');
  }
  $scope.exportCostBalanceExcel=function() {
    window.open($scope.baseUrl+'/pdf/cost_balance_excel','_blank');
  }
  // Close 13. Report - Finance Accounting
});

app.controller('financeReportBiayaBelumTransaksi', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Biaya Belum Transaksi";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'Pdf'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/posisi_keuangan').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 2 || $scope.formData.tipe_dokumen == null) {
      $http({method: 'GET', url: baseUrl+'/pdf/unpaid_cost_excel', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    } else {
      $http({method: 'GET', url: baseUrl+'/pdf/unpaid_cost', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
  }
});

app.controller('financeReportBiayaSelisih', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Biaya Selisih";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'Pdf'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/posisi_keuangan').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 2 || $scope.formData.tipe_dokumen == null) {
      $http({method: 'GET', url: baseUrl+'/pdf/cost_balance_excel', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    } else {
      $http({method: 'GET', url: baseUrl+'/pdf/cost_balance', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
  }

});

app.controller('financeReportJournal', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Jurnal Umum";
  $scope.baseUrl=baseUrl;

  $scope.status=[
    {id: 1, name:'Draft'},
    {id: 2, name:'Disetujui'},
    {id: 3, name:'Posting'},
  ];
  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/journal').then(function(data) {
    $scope.data=data.data;
  });

  $scope.exportJournal=function() {    
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/journal_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    } 
    if($scope.formData.tipe_dokumen == 1) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/journal', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(response, status, headers, config) {
        var blob = response.data;
        var contentType = response.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        window.open(fileURL,'_blank');
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText,"Error!");
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportLedger', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile,$window) {
  $rootScope.pageTitle="Laporan Buku Besar";
  $scope.baseUrl=baseUrl;
  $scope.formData = {};

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ledger').then(function(data) {
    $scope.data=data.data;
  });

  $scope.showWorkOrder = function() {
    $('#workOrderModal').modal()
  }
  
  var work_order_datatable = $('#work_order_datatable').DataTable({
    processing: true,
    serverSide: true,
    order:[[3,'desc']],
    scrollX:false,
    // dom: 'frtp',
    ajax: {
        headers : {'Authorization' : 'Bearer '+authUser.api_token},
        url : baseUrl+'/api/marketing/work_order_datatable',
        data: {
          company_id: $scope.formData.company_id
        },
        dataSrc: function(e) {
          $scope.list_wo = e.data;
          return e.data;
        }
    },
    columns:[
      {
          data:null,
          name:"code",
          className : 'font-bold',
          render : function(a, b, v, z) {
            return `<button type="button" class="btn btn-primary btn-xs" ng-click="selectWo(${v.id})"><i class="fa fa-check"></i> Pilih</button>`;
          }
      },
      {data:"company.name",name:"company.name"},
      {data:"code",name:"code"},
      {data:"date",name:"date"},
      {data:"work_type.name",name:"work_type.name"},
      {data:"customer.name",name:"customer.name",className:"font-bold"},
      {data:"name",name:"name"},
    ],
    createdRow: function(row, data, dataIndex) {
      $compile(angular.element(row).contents())($scope);
    }
  });
  
  $scope.selectWo = function(id) {
    let selected = $scope.list_wo.find((e) => e.id == id);
    $scope.formData.work_order_id = id;
    $scope.formData.work_order_code = selected.code;
    $('#workOrderModal').modal('hide');
  }

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 2 ) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/ledger_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if($scope.formData.tipe_dokumen == 1){

      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/ledger'+query,'_blank');
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportLedgerReceivable', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Buku Besar Piutang";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ledger_receivable').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
    if($scope.formData.tipe_dokumen == 2){
      $http({method: 'GET', url: baseUrl+'/finance/report/export/ledger_receivable_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if($scope.formData.tipe_dokumen == 1){
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/ledger_receivable'+query,'_blank');
    }
    }
    
});
app.controller('financeReportLedgerPayable', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Buku Besar Hutang";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ledger_payable').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
    if($scope.formData.tipe_dokumen == 2){
      $http({method: 'GET', url: baseUrl+'/finance/report/export/ledger_payable_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if($scope.formData.tipe_dokumen == 1){
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/ledger_payable'+query,'_blank');
    }
    }
    
});
app.controller('financeReportLedgerUmSupplier', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Buku Besar Uang Muka Supplier";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ledger_um_supplier').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $scope.formData._token = csrfToken;
    var query = $.param($scope.formData);

    if(query != "")
      query = '?' + query;

    window.open($scope.baseUrl+'/finance/report/export/ledger_um_supplier'+query,'_blank');
  }
});
app.controller('financeReportLedgerUmCustomer', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Buku Besar Uang Muka Customer";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ledger_um_customer').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $scope.formData._token = csrfToken;
    var query = $.param($scope.formData);

    if(query != "")
      query = '?' + query;

    window.open($scope.baseUrl+'/finance/report/export/ledger_um_customer'+query,'_blank');
  }
});
app.controller('financeReportNeracaSaldo', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Neraca Saldo";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/neraca_saldo').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 1) {
    
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/neraca_saldo'+query,'_blank');
    } if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/neraca_saldo_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportNeracaLajur', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Neraca Lajur";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/posisi_keuangan').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/neraca_lajur', params: $scope.formData, responseType: 'arraybuffer'})
    .then(function successCallback(data, status, headers, config) {

      headers = data.headers()
      var filename = headers['content-disposition'].split('"')[1];
      var contentType = headers['content-type'];
      var linkElement = document.createElement('a');
      try {
        var blob = new Blob([data.data], { type: contentType });
        var url = window.URL.createObjectURL(blob);
        linkElement.setAttribute('href', url);
        linkElement.setAttribute("download", filename);
        var clickEvent = new MouseEvent("click", {
          "view": window,
          "bubbles": true,
          "cancelable": false
        });
        linkElement.dispatchEvent(clickEvent);
      } catch (e) {

      }
    },
    function errorCallback(data, status, headers, config) {

      toastr.error(data.statusText);
    });
  }

});
app.controller('financeReportNeracaSaldoBanding', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Neraca Saldo Perbandingan";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/neraca_saldo').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/neraca_saldo_banding', params: $scope.formData, responseType: 'blob'})
    .then(function successCallback(response, status, headers, config) {
      var blob = response.data;
      var contentType = response.headers("content-type");
      var fileURL = URL.createObjectURL(blob);
      window.open(fileURL,'_blank');
    },
    function errorCallback(data, status, headers, config) {
      toastr.error(data.statusText,"Error!");
    });
    // window.location.href=baseUrl+'/finance/report/export/journal?company_id='+$scope.formData.company_id+'&start_date='+$scope.formData.start_date+'&end_date='+$scope.formData.end_date+'&status='+$scope.formData.status+'&type_transaction_id='+$scope.formData.type_transaction_id;
  }
});
app.controller('financeReportLabaRugi', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Laba Rugi";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
    {id: 3, name:'Excel Horizontal'},
  ];

  const date = new Date();

  $scope.formData={
    start_month: date,
    end_month: date
  };
  $http.get(baseUrl+'/finance/report/laba_rugi').then(function(data) {
    $scope.data=data.data;
  });

  var lastday = function(y,m){
    console.log(m);
    return  new Date(y, m, 0).getDate();
  }

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 1) {
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/laba_rugi'+query,'_blank');
    }
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }

    if($scope.formData.tipe_dokumen == 3 ) {
      
      // START DATE TIME MANIPULATION
      // SEND TO SERVER WHERE FULL DATE TYPE NOT ONLY MONTH
      // THIS WILL BE THE ONE THAT PROCESS FOR EASIER CARBON LARAVEL
      $scope.formData.start_date = '01-'+$scope.formData.start_month;
      const split_end = $scope.formData.end_month.split("-")
      const lastDay = lastday(split_end[1], split_end[0]);
      $scope.formData.end_date = lastDay+'-'+$scope.formData.end_month;
      // END DATE TIME MANIPULATION

      $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_excel_horizontal', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText);
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportEkuitas', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Ekuitas";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ekuitas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/ekuitas_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if($scope.formData.tipe_dokumen == 1) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/ekuitas', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(response, status, headers, config) {
        var blob = response.data;
        var contentType = response.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        window.open(fileURL,'_blank');
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText,"Error!");
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
   
  }
});
app.controller('financeReportEkuitasBanding', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Ekuitas Perbandingan";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/ekuitas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/ekuitas_banding', params: $scope.formData, responseType: 'blob'})
    .then(function successCallback(response, status, headers, config) {
      var blob = response.data;
      var contentType = response.headers("content-type");
      var fileURL = URL.createObjectURL(blob);
      window.open(fileURL,'_blank');
    },
    function errorCallback(data, status, headers, config) {
      toastr.error(data.statusText,"Error!");
    });
  }
});
app.controller('financeReportPosisiKeuangan', function($scope, $http, $rootScope,$state,$stateParams,$filter, $timeout,$compile) {
  $rootScope.pageTitle="Laporan Posisi Keuangan";
  $scope.baseUrl=baseUrl;
  
  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  $scope.formData={};
  $http.get(baseUrl+'/finance/report/posisi_keuangan').then(function(data) {
    $scope.data=data.data;
    $scope.formData.start_date = $filter('minDate')(data.data.initial_date_accounting);
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/posisi_keuangan', params: $scope.formData, responseType: 'arraybuffer'})
    .then(function successCallback(data, status, headers, config) {

      headers = data.headers()
      var filename = headers['content-disposition'].split('"')[1];
      var contentType = headers['content-type'];
      var linkElement = document.createElement('a');
      try {
        var blob = new Blob([data.data], { type: contentType });
        var url = window.URL.createObjectURL(blob);
        linkElement.setAttribute('href', url);
        linkElement.setAttribute("download", filename);
        var clickEvent = new MouseEvent("click", {
          "view": window,
          "bubbles": true,
          "cancelable": false
        });
        linkElement.dispatchEvent(clickEvent);
      } catch (e) {

      }
    },
    function errorCallback(data, status, headers, config) {

      toastr.error(data.statusText);
    });
  }

});
app.controller('financeReportPosisiKeuanganBanding', function($scope, $http, $rootScope,$state,$stateParams, $filter,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Posisi Keuangan Perbandingan";
  $scope.baseUrl=baseUrl;
  

  $scope.formData={};
  $http.get(baseUrl+'/finance/report/posisi_keuangan').then(function(data) {
    $scope.data=data.data;
    $scope.formData.start_date = $filter('minDate')(data.data.initial_date_accounting);
    $scope.formData.start_date2 = $filter('minDate')(data.data.initial_date_accounting);
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/posisi_keuangan_perbandingan', params: $scope.formData, responseType: 'arraybuffer'})
    .then(function successCallback(data, status, headers, config) {

      headers = data.headers()
      var filename = headers['content-disposition'].split('"')[1];
      var contentType = headers['content-type'];
      var linkElement = document.createElement('a');
      try {
        var blob = new Blob([data.data], { type: contentType });
        var url = window.URL.createObjectURL(blob);
        linkElement.setAttribute('href', url);
        linkElement.setAttribute("download", filename);
        var clickEvent = new MouseEvent("click", {
          "view": window,
          "bubbles": true,
          "cancelable": false
        });
        linkElement.dispatchEvent(clickEvent);
      } catch (e) {

      }
    },
    function errorCallback(data, status, headers, config) {

      toastr.error(data.statusText);
    });
  }

});
app.controller('financeReportOutstandingDebt', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Outstanding Piutang";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/outstanding_debt').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 1){
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);

      if(query != "")
        query = '?' + query;

      window.open($scope.baseUrl+'/finance/report/export/outstanding_debt'+query,'_blank');
    }
    if($scope.formData.tipe_dokumen == 2){
      $http({method: 'GET', url: baseUrl+'/finance/report/export/outstanding_debt_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportOutstandingCredit', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Outstanding Hutang";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/outstanding_credit').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if($scope.formData.tipe_dokumen == 1){
      $scope.formData._token = csrfToken;
      var query = $.param($scope.formData);
  
      if(query != "")
        query = '?' + query;
  
      window.open($scope.baseUrl+'/finance/report/export/outstanding_credit'+query,'_blank');
    }
    if($scope.formData.tipe_dokumen == 2){
      $http({method: 'GET', url: baseUrl+'/finance/report/export/outstanding_credit_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
   
  }
});
app.controller('financeReportProfitComparison', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Laba Rugi Perbandingan";
  $scope.baseUrl=baseUrl;
  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/laba_rugi_perbandingan').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    /*
    $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_perbandingan', params: $scope.formData, responseType: 'blob'})
    .then(function successCallback(response, status, headers, config) {
      var blob = response.data;
      var contentType = response.headers("content-type");
      var fileURL = URL.createObjectURL(blob);
      window.open(fileURL,'_blank');
    },
    function errorCallback(data, status, headers, config) {
      toastr.error(data.statusText,"Error!");
    });
    */
    $scope.formData._token = csrfToken;
    var query = $.param($scope.formData);

    if(query != "")
      query = '?' + query;

    window.open($scope.baseUrl+'/finance/report/export/laba_rugi_perbandingan'+query,'_blank');

    // window.location.href=baseUrl+'/finance/report/export/journal?company_id='+$scope.formData.company_id+'&start_date='+$scope.formData.start_date+'&end_date='+$scope.formData.end_date+'&status='+$scope.formData.status+'&type_transaction_id='+$scope.formData.type_transaction_id;
  }
});
app.controller('financeReportArusKas', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Arus Kas";
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/arus_kas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {

    if($scope.formData.tipe_dokumen == 1 ) {

      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(response, status, headers, config) {
        var blob = response.data;
        var contentType = response.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        window.open(fileURL,'_blank');
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText,"Error!");
      });
    
    }
    if($scope.formData.tipe_dokumen == 2 ) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
        } catch (e) {

        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }

    // window.location.href=baseUrl+'/finance/report/export/journal?company_id='+$scope.formData.company_id+'&start_date='+$scope.formData.start_date+'&end_date='+$scope.formData.end_date+'&status='+$scope.formData.status+'&type_transaction_id='+$scope.formData.type_transaction_id;
  }

});
app.controller('financeReportArusKasBaru', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Arus Kas Baru";
  $scope.baseUrl=baseUrl;

  $scope.isExporting = false;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  $http.get(baseUrl+'/finance/report/arus_kas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $scope.isExporting = true;

    if($scope.formData.tipe_dokumen == 1 ) {

      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_baru', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(response, status, headers, config) {
        var blob = response.data;
        var contentType = response.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        window.open(fileURL,'_blank');
        $scope.isExporting = false;
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText,"Error!");
        $scope.isExporting = false;
      });

    }
    if($scope.formData.tipe_dokumen == 2 ) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_baru_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
          $scope.isExporting = false;
        } catch (e) {
          toastr.error("Gagal mengunduh file Excel","Error!");
          $scope.isExporting = false;
        }
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText || "Gagal export Excel","Error!");
        $scope.isExporting = false;
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
      $scope.isExporting = false;
    }
  }

});
app.controller('financeReportArusKasBanding', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Arus Kas Perbandingan";
  $scope.baseUrl=baseUrl;

  // $scope.formData={};
  $http.get(baseUrl+'/finance/report/arus_kas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_perbandingan', params: $scope.formData, responseType: 'blob'})
    .then(function successCallback(response, status, headers, config) {
      var blob = response.data;
      var contentType = response.headers("content-type");
      var fileURL = URL.createObjectURL(blob);
      window.open(fileURL,'_blank');
    },
    function errorCallback(data, status, headers, config) {
      toastr.error(data.statusText,"Error!");
    });
    // window.location.href=baseUrl+'/finance/report/export/journal?company_id='+$scope.formData.company_id+'&start_date='+$scope.formData.start_date+'&end_date='+$scope.formData.end_date+'&status='+$scope.formData.status+'&type_transaction_id='+$scope.formData.type_transaction_id;
  }

});
app.controller('financeReportArusKasBandingBaru', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Arus Kas Perbandingan Baru";
  $scope.baseUrl=baseUrl;

  $scope.isExporting = false;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
  ];

  $http.get(baseUrl+'/finance/report/arus_kas').then(function(data) {
    $scope.data=data.data;
  });

  $scope.export=function() {
    if(!$scope.formData.start_date || !$scope.formData.end_date || !$scope.formData.start_date2 || !$scope.formData.end_date2) {
      toastr.error("Harap isi semua periode","Error!");
      return;
    }

    $scope.isExporting = true;

    if($scope.formData.tipe_dokumen == 1 ) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_perbandingan_baru', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(response, status, headers, config) {
        var blob = response.data;
        var contentType = response.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        window.open(fileURL,'_blank');
        $scope.isExporting = false;
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText || "Gagal export PDF","Error!");
        $scope.isExporting = false;
      });
    }
    if($scope.formData.tipe_dokumen == 2 ) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/arus_kas_perbandingan_baru_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {
        var blob = data.data;
        var contentType = data.headers("content-type");
        var fileURL = URL.createObjectURL(blob);
        var link = document.createElement('a');
        link.href = fileURL;
        link.download = 'Laporan_Arus_Kas_Perbandingan_Baru_' + new Date().toISOString().slice(0,10) + '.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        $scope.isExporting = false;
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText || "Gagal export Excel","Error!");
        $scope.isExporting = false;
      });
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
      $scope.isExporting = false;
    }
  }

});
app.controller('financeReportNotaKoreksi', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Nota Koreksi Hutang / Piutang";
  $scope.baseUrl=baseUrl;
  $scope.isPreview = false;

  $scope.tipe_dokumen=[
    {id: 1, name:'PDF'},
    {id: 2, name:'Excel'}
  ];

  $scope.type=[
    {id:1,name:"Hutang"},
    {id:2,name:"Piutang"},
  ];

  $http.get(baseUrl+'/finance/report/arus_kas').then(function(data) {
    $scope.data=data.data;
  });
  
  $scope.export=function() {
    // TIPE HUTANG
    if($scope.formData.type == 1 ) {    
      if($scope.formData.tipe_dokumen == 1) {
        $scope.formData._token = csrfToken;
        var query = $.param($scope.formData);
    
        if(query != "")
          query = '?' + query;
    
        window.open($scope.baseUrl+'/finance/report/export/nota_potong_pembelian_export'+query,'_blank');
      }
      if($scope.formData.tipe_dokumen == 2 ) {
        $http({method: 'GET', url: baseUrl+'/excel/nota_potong_pembelian_export', params: $scope.formData, responseType: 'blob'})
        .then(function successCallback(data, status, headers, config) {
  
          headers = data.headers()
          var filename = headers['content-disposition'].split('"')[1];
          var contentType = headers['content-type'];
          var linkElement = document.createElement('a');
          try {
            var blob = new Blob([data.data], { type: contentType });
            var url = window.URL.createObjectURL(blob);
            linkElement.setAttribute('href', url);
            linkElement.setAttribute("download", filename);
            var clickEvent = new MouseEvent("click", {
              "view": window,
              "bubbles": true,
              "cancelable": false
            });
            linkElement.dispatchEvent(clickEvent);
          } catch (e) {
  
          }
        },
        function errorCallback(data, status, headers, config) {
  
          toastr.error(data.statusText);
        });
      }
    }
    // TIPE PIUTANG
    if($scope.formData.type == 2 ) {    
      if($scope.formData.tipe_dokumen == 1) {
        $scope.formData._token = csrfToken;
        var query = $.param($scope.formData);
    
        if(query != "")
          query = '?' + query;
    
        window.open($scope.baseUrl+'/finance/report/export/nota_potong_penjualan_export'+query,'_blank');
      }
      if($scope.formData.tipe_dokumen == 2 ) {
        $http({method: 'GET', url: baseUrl+'/excel/nota_potong_penjualan_export', params: $scope.formData, responseType: 'blob'})
        .then(function successCallback(data, status, headers, config) {
  
          headers = data.headers()
          var filename = headers['content-disposition'].split('"')[1];
          var contentType = headers['content-type'];
          var linkElement = document.createElement('a');
          try {
            var blob = new Blob([data.data], { type: contentType });
            var url = window.URL.createObjectURL(blob);
            linkElement.setAttribute('href', url);
            linkElement.setAttribute("download", filename);
            var clickEvent = new MouseEvent("click", {
              "view": window,
              "bubbles": true,
              "cancelable": false
            });
            linkElement.dispatchEvent(clickEvent);
          } catch (e) {
  
          }
        },
        function errorCallback(data, status, headers, config) {
  
          toastr.error(data.statusText);
        });
      }
    }
    if(!$scope.formData.type){
      toastr.error("Harap pilih tipe transaksi","Error!");
    }
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportPengeluaranJo', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile,$filter) {
  $rootScope.pageTitle="Report Operasional";
  $('.ibox-content').addClass('sk-loading');
  $scope.formData={}

  $scope.resetFilter = function() {
      $scope.formData = {};
      $scope.isPreview = false;
  }

  $scope.showPreview = function() {
    $scope.isPreview = false;
    var esc=encodeURIComponent;
    var params=Object.keys($scope.formData).map(function(k) {
        if (!$scope.formData[k]) {
            return esc(k)+'=';
        }
        return esc(k) + '=' + esc($scope.formData[k]);
    }).join('&')

    $http.get(baseUrl+'/finance/report/export/preview_register_pengeluaran_jo?' + params).then(function(resp) {
        var outp = resp.data;
        $('#preview_box').html($compile(outp)($scope));
        $scope.isPreview = true;
    });
  }
  $scope.get_jo = function(){
    $scope.job_order= [];
    $http.get(baseUrl+'/operational/manifest_ftl/get_data_job_order').then(function(resp) {
      resp.data.data.forEach(element => {
        if(element.company_id == $scope.formData.company_id){
          $scope.job_order.push(element)
        }
      });
    });
  }

  $http.get(baseUrl+'/setting/company').then(function(data) {
    $scope.company=data.data;
    $('.ibox-content').removeClass('sk-loading');
  });

  $scope.export=function() {
      $('.ibox-content').addClass('sk-loading');
      $http({method: 'GET', url: baseUrl+'/finance/report/export/excel_register_pengeluaran_jo', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

          headers = data.headers()
          var filename = headers['content-disposition'].split('"')[1];
          var contentType = headers['content-type'];
          var linkElement = document.createElement('a');
          try {
              var blob = new Blob([data.data], { type: contentType });
              var url = window.URL.createObjectURL(blob);
              linkElement.setAttribute('href', url);
              linkElement.setAttribute("download", filename);
              var clickEvent = new MouseEvent("click", {
                  "view": window,
                  "bubbles": true,
                  "cancelable": false
              });
              linkElement.dispatchEvent(clickEvent);
          } catch (e) {

          }
          $('.ibox-content').removeClass('sk-loading');
      },
      function errorCallback(data, status, headers, config) {
          toastr.error(data.statusText);
          $('.ibox-content').removeClass('sk-loading');
      });
  }


  $scope.export_pdf=function() {
      $('.ibox-content').addClass('sk-loading');
      $http({method: 'GET', url: baseUrl+'/finance/report/export/export_register_pengeluaran_jo', params: $scope.formData, responseType: 'arraybuffer'})
      .then(function successCallback(data, status, headers, config) {

          headers = data.headers()
          var filename = headers['content-disposition'].split('"')[1];
          var contentType = headers['content-type'];
          var linkElement = document.createElement('a');
          try {
              var blob = new Blob([data.data], { type: contentType });
              var url = window.URL.createObjectURL(blob);
              linkElement.setAttribute('href', url);
              linkElement.setAttribute("download", filename);
              var clickEvent = new MouseEvent("click", {
                  "view": window,
                  "bubbles": true,
                  "cancelable": false
              });
              linkElement.dispatchEvent(clickEvent);
          } catch (e) {
          }
          $('.ibox-content').removeClass('sk-loading');
      },
      function errorCallback(data, status, headers, config) {
          toastr.error(data.statusText);
          $('.ibox-content').removeClass('sk-loading');
      });
  }


});
app.controller('financeReportLabaRugiJO', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Laba Rugi Job Order";
  $scope.baseUrl=baseUrl;
  $scope.isLoading = false;
  $scope.tipe_dokumen=[
    // {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
    // {id: 3, name:'Excel Horizontal'},
  ];

  const date = new Date();

  $scope.formData={
    start_month: date,
    end_month: date,
    tipe_dokumen: 2,
    is_invoice: 1
  };
  $http.get(baseUrl+'/finance/report/laba_rugi').then(function(data) {
    $scope.data=data.data;
  });

  var lastday = function(y,m){
    console.log(m);
    return  new Date(y, m, 0).getDate();
  }

  $scope.export=function() {
    $scope.isLoading = true;
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_jo_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
          $scope.isLoading = false;
        } catch (e) {
          $scope.isLoading = false;
        }
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText);
      });
    }
    
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportLabaRugiNopol', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Laba Rugi Nopol";
  $scope.isLoading = false;
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    // {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
    // {id: 3, name:'Excel Horizontal'},
  ];

  const date = new Date();

  $scope.formData={
    start_month: date,
    end_month: date,
    tipe_dokumen: 2,
    is_invoice: 1
  };
  $http.get(baseUrl+'/finance/report/laba_rugi').then(function(data) {
    $scope.data=data.data;
  });

  var lastday = function(y,m){
    console.log(m);
    return  new Date(y, m, 0).getDate();
  }

  $scope.export=function() {
    $scope.isLoading = true;
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_nopol_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
          $scope.isLoading = false;
        } catch (e) {
          $scope.isLoading = false;
        }
      },
      function errorCallback(data, status, headers, config) {
        toastr.error(data.statusText);
      });
    }
    
    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
app.controller('financeReportLabaRugiDriver', function($scope, $http, $rootScope,$state,$stateParams,$timeout,$compile) {
  $rootScope.pageTitle="Laporan Laba Rugi Driver";
  $scope.isLoading = false;
  $scope.baseUrl=baseUrl;

  $scope.tipe_dokumen=[
    // {id: 1, name:'PDF'},
    {id: 2, name:'Excel'},
    // {id: 3, name:'Excel Horizontal'},
  ];

  const date = new Date();

  $scope.formData={
    start_month: date,
    end_month: date,
    tipe_dokumen: 2,
    is_invoice: 1
  };
  $http.get(baseUrl+'/finance/report/laba_rugi').then(function(data) {
    $scope.data=data.data;
  });

  var lastday = function(y,m){
    console.log(m);
    return  new Date(y, m, 0).getDate();
  }

  $scope.export=function() {
    $scope.isLoading = true;
    
    if($scope.formData.tipe_dokumen == 2) {
      $http({method: 'GET', url: baseUrl+'/finance/report/export/laba_rugi_driver_excel', params: $scope.formData, responseType: 'blob'})
      .then(function successCallback(data, status, headers, config) {

        headers = data.headers()
        var filename = headers['content-disposition'].split('"')[1];
        var contentType = headers['content-type'];
        var linkElement = document.createElement('a');
        try {
          var blob = new Blob([data.data], { type: contentType });
          var url = window.URL.createObjectURL(blob);
          linkElement.setAttribute('href', url);
          linkElement.setAttribute("download", filename);
          var clickEvent = new MouseEvent("click", {
            "view": window,
            "bubbles": true,
            "cancelable": false
          });
          linkElement.dispatchEvent(clickEvent);
          $scope.isLoading = false;
        } catch (e) {
          $scope.isLoading = false;
        }
      },
      function errorCallback(data, status, headers, config) {

        toastr.error(data.statusText);
      });
    }

    if(!$scope.formData.tipe_dokumen){
      toastr.error("Harap pilih tipe dokumen","Error!");
    }
  }
});
