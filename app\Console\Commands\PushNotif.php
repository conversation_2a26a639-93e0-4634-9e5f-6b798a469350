<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Model\Quotation;
use App\Model\Notification;
use App\Model\NotificationUser;
use App\Model\Invoice;
use App\Model\InvoiceVendor;
use Carbon\Carbon;
use DB;

class PushNotif extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'push:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cek dan Trigger Notifikasi';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
      //invoices
      $invoices=DB::table('invoices')
      ->leftJoin('contacts','contacts.id','=','invoices.customer_id')
      ->where('invoices.due_date','>',Carbon::now()->format('Y-m-d'))
      ->where('invoices.status','<',5)
      ->select([
        'invoices.id',
        'contacts.name as customer',
        'invoices.code',
        'invoices.due_date',
        'invoices.slug',
        'invoices.company_id'
      ])->get();
      $reminder_invoice=DB::table('reminder_types')->where('id', 1)->first();
      foreach ($invoices as $value) {
        $due=Carbon::parse($value->due_date);
        $limit=Carbon::now()->addDays($reminder_invoice->interval);
        $now=Carbon::now();
        $diff=Carbon::parse($value->due_date)->diffInDays(Carbon::now());
        if ($now<$due && $due<$limit) {
          DB::beginTransaction();

          $slug=str_random(6);
          if (!$value->slug) {
            Invoice::find($value->id)->update([
              'slug' => $slug
            ]);
          } else {
            $slug=$value->slug;
          }
          $userList=DB::table('notification_type_users')
          ->leftJoin('users','users.id','=','notification_type_users.user_id')
          ->whereRaw("notification_type_users.notification_type_id = 10")
          ->select('users.id','users.is_admin','users.company_id')->get();
          $n=Notification::create([
            'notification_type_id' => 10,
            'name' => 'Ada Invoice yang akan mendekati jatuh tempo!',
            'description' => 'No. Invoice '.$value->code.', '.$value->customer.' akan jatuh tempo dalam '.$diff.' hari',
            'slug' => $slug,
            'route' => 'operational.invoice_jual.show',
            'parameter' => json_encode(['id' => $value->id])
          ]);
          foreach ($userList as $un) {
            if ($un->is_admin) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            } else {
              if ($un->company_id==$value->company_id) {
                NotificationUser::create([
                  'notification_id' => $n->id,
                  'user_id' => $un->id
                ]);
              }
              //abaikan
            }
          }
          $this->info("Invoice $value->code telah dikirim notifikasi");
          DB::commit();
        }
      }

      // invoice vendor --------------------------------------------
      $invoice_vendors=DB::table('invoice_vendors')
      ->leftJoin('contacts','contacts.id','=','invoice_vendors.vendor_id')
      ->where('invoice_vendors.date_invoice','>',Carbon::now()->format('Y-m-d'))
      ->where('invoice_vendors.status',1)
      ->select([
        'invoice_vendors.id',
        'contacts.name as customer',
        'invoice_vendors.code',
        'invoice_vendors.date_invoice as due_date',
        'invoice_vendors.slug',
        'invoice_vendors.company_id'
      ])->get();
      $reminder_invoice=DB::table('reminder_types')->where('id', 2)->first();
      foreach ($invoice_vendors as $value) {
        $due=Carbon::parse($value->due_date);
        $limit=Carbon::now()->addDays($reminder_invoice->interval);
        $now=Carbon::now();
        $diff=Carbon::parse($value->due_date)->diffInDays(Carbon::now());
        if ($now<$due && $due<$limit) {
          DB::beginTransaction();

          $slug=str_random(6);
          if (!$value->slug) {
            InvoiceVendor::find($value->id)->update([
              'slug' => $slug
            ]);
          } else {
            $slug=$value->slug;
          }
          $userList=DB::table('notification_type_users')
          ->leftJoin('users','users.id','=','notification_type_users.user_id')
          ->whereRaw("notification_type_users.notification_type_id = 9")
          ->select('users.id','users.is_admin','users.company_id')->get();
          $n=Notification::create([
            'notification_type_id' => 9,
            'name' => 'Ada Invoice Vendor yang akan mendekati jatuh tempo!',
            'description' => 'No. Invoice '.$value->code.', '.$value->customer.' akan jatuh tempo dalam '.$diff.' hari',
            'slug' => $slug,
            'route' => 'operational.invoice_vendor.show',
            'parameter' => json_encode(['id' => $value->id])
          ]);
          foreach ($userList as $un) {
            if ($un->is_admin) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            } else {
              if ($un->company_id==$value->company_id) {
                NotificationUser::create([
                  'notification_id' => $n->id,
                  'user_id' => $un->id
                ]);
              }
              //abaikan
            }
          }
          $this->info("Invoice Vendor $value->code telah dikirim notifikasi");
          DB::commit();
        }
      }
      // manifest
      $manifests = DB::table('manifests')
                  ->select(
                      'manifests.id',
                      'manifests.etd_time',
                      'manifests.code',
                      'contacts.name as customer',
                      'manifests.company_id',
                      'manifests.vehicle_type_id',
                      'manifests.container_type_id'
                  )
                  ->leftJoin('delivery_order_drivers','delivery_order_drivers.manifest_id','manifests.id')
                  ->leftJoin('manifest_details','manifest_details.header_id','manifests.id')
                  ->leftJoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
                  ->leftJoin('job_orders','job_orders.id','job_order_details.header_id')
                  ->leftJoin('contacts','contacts.id','job_orders.customer_id')
                  ->where('manifests.date_manifest','<>',null)
                  ->where('manifests.date_manifest','>',Carbon::now()->format('Y-m-d'))
                  ->where('delivery_order_drivers.job_status_id','<',6)
                  ->get();
      $reminder_manifest=DB::table('reminder_types')->where('slug', 'rencanaPengiriman')->first();
      $notif_type = DB::table('notification_types')->where('name','Rencana Pengiriman')->first();
      foreach ($manifests as $manifest) {
        $etd = Carbon::parse($manifest->etd_time);
        $limit=Carbon::now()->addDays($reminder_manifest->interval);
        $now=Carbon::now();
        $diff=Carbon::parse($manifest->etd_time)->diffInDays(Carbon::now());
        if($now<$etd && $etd<$limit){
          DB::beginTransaction();
          $userList=DB::table('notification_type_users')
          ->leftJoin('users','users.id','=','notification_type_users.user_id')
          ->where('notification_type_users.notification_type_id',$notif_type->id)
          ->select('users.id','users.is_admin','users.company_id')->get();
          $route = '';
          if($manifests->vehicle_type_id!=null){              
            //FTL & LTL
            $route = 'operational.manifest_ftl.show';
          }
          if($manifests->container_type_id!=null){
              //FCL & LCL
              $route = 'operational.manifest_fcl.show';
          }
          $n=Notification::create([
            'notification_type_id' => $notif_type->id,
            'name' => 'Ada Rencana Pengiriman yang akan mendekati jatuh tempo!',
            'description' => 'No. Manifest '.$manifest->code.', '.$manifest->customer.' akan masuk tanggal pengiriman dalam tempo'.$diff.' hari',
            'slug' => str_random(6),
            'route' => $route,
            'parameter' => json_encode(['id' => $manifest->id])
          ]);
        }
        foreach ($userList as $un) {
          if ($un->is_admin) {
            NotificationUser::create([
              'notification_id' => $n->id,
              'user_id' => $un->id
            ]);
          } else {
            if ($un->company_id==$manifest->company_id) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            }
            //abaikan
          }
        }
        $this->info("Manifest $manifest->code telah dikirim notifikasi");
        DB::commit();
      }
      // vehicle maintenance
      $maintenances = DB::table('vehicle_maintenances')
                      ->select(
                        'vehicle_maintenances.id',
                        'vehicle_maintenances.date_rencana',
                        'vehicle_maintenances.code',
                        'vehicles.nopol',
                        'vehicles.id as vehicle_id',
                        'vehicle_maintenances.company_id'
                      )
                      ->leftJoin('vehicles','vehicles.id','vehicle_maintenances.vehicle_id')
                      ->where('vehicle_maintenances.date_pengajuan','>',Carbon::now()->format('Y-m-d'))
                      ->where('vehicle_maintenances.status',3)
                      ->get();
      $reminder_maintenance=DB::table('reminder_types')->where('slug', 'rencanaPerawatan')->first();
      $notif_type = DB::table('notification_types')->where('name','Rencana Perawatan')->first();
      foreach ($maintenances as $maintenance) {
        $due   = Carbon::parse($maintenance->date_rencana);
        $limit = Carbon::now()->addDays($reminder_maintenance->interval);
        $now   = Carbon::now();
        $diff  = Carbon::parse($maintenance->date_rencana)->diffInDays(Carbon::now());
        if($now<$due && $due<$limit){
          DB::beginTransaction();
          $userList=DB::table('notification_type_users')
          ->leftJoin('users','users.id','=','notification_type_users.user_id')
          ->where('notification_type_users.notification_type_id',$notif_type->id)
          ->select('users.id','users.is_admin','users.company_id')->get();

          $n=Notification::create([
            'notification_type_id' => $notif_type->id,
            'name' => 'Ada Rencana Perawatan yang akan mendekati tanggal rencana!',
            'description' => 'No. Maintenance '.$maintenance->code.', '.$maintenance->nopol.' akan masuk tanggal perencanaan dalam tempo'.$diff.' hari',
            'slug' => str_random(6),
            'route' => 'vehicle.vehicle.show.maintenance.show',
            'parameter' => json_encode(['id' => $maintenance->vehicle_id,'vm_id'=>$maintenance->id])
          ]);
        }
        foreach ($userList as $un) {
          if ($un->is_admin) {
            NotificationUser::create([
              'notification_id' => $n->id,
              'user_id' => $un->id
            ]);
          } else {
            if ($un->company_id==$maintenance->company_id) {
              NotificationUser::create([
                'notification_id' => $n->id,
                'user_id' => $un->id
              ]);
            }
            //abaikan
          }
        }
        $this->info("Vehicle Maintenance $maintenance->code telah dikirim notifikasi");
        DB::commit();
      }
    }
}
