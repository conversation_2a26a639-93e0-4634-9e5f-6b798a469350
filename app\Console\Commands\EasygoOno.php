<?php
namespace App\Console\Commands;
use App\Model\Vehicle;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class EasygoOno extends Command
{
    protected $signature = 'gps:fetch-gps-easygo-ono';
    protected $description = 'Log GPS easygo pt Ono Loistik';
    public function __construct()
    {
        parent::__construct();
    }
    public function handle()
    {
        DB::beginTransaction();
        $this->info('GET API from EASYGO');
        $curl = curl_init();
        $payload = [
            "list_nopol" => [],
            "status_vehicle" => NULL,
            "geo_code" => [],
            "page" => 0,
            "encrypted" => 0
        ];
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://vtsapi.easygo-gps.co.id/api/Report/lastposition',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER     => array(
                "accept: application/json",
                "token: ".env('EASYGO_TOKEN'), 
                "Content-Type: application/json"
            ),
            CURLOPT_POSTFIELDS => json_encode($payload),
        ));
        $response = curl_exec($curl);
        curl_close($curl);

        $data = json_decode($response, true);        
        if ($data['ResponseCode'] == 1) {
            $row = $data['Data'];
            foreach ($row as $unit){
                DB::table('vehicle_km_gps_logs')->insert([
                    'imei'=>$unit['gps_sn'],
                    'gsm_no'=>$unit['gsm_no'],
                    'odometer'=>$unit['odometer'],
                    'log_date'=>Carbon::now(),
                    'updated_at'=>Carbon::now(),
                    'lattitude'=>(string)$unit['lat'],
                    'longitude'=>(string)$unit['lon']
                ]);
                $v = Vehicle::where('no_imei',$unit['gps_sn'])->where('gps_no',$unit['gsm_no'])->where('is_trailer',0)->first();
                if ($v != null) {
                    $v->last_latitude=(string)$unit['lat'];
                    $v->last_longitude=(string)$unit['lon'];
                    $v->last_km_gps_date=Carbon::now();
                    $v->last_update_latlng=Carbon::now();
                    $v->last_km_date=Carbon::now();
                    $v->gps_no=(string)$unit['gsm_no'];
                    $v->no_imei=(string)$unit['gps_sn'];
                    $v->save();
                }
                // sleep(2);
            }
        } else {
            $this->error('Get Data from EASYGO Failed!');
        }

        DB::commit();
        return $this->info('Success update on :'.date('Y-m-d H:i:s'));
    }
}
