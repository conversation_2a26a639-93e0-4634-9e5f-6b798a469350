<?php
namespace App\Abstracts\Operational;
use Carbon\Carbon;
use Exception;
use App\Model\Manifest AS M;
use App\Model\ManifestStatusLog;
use App\Abstracts\Operational\DeliveryOrderDriver;
use App\Abstracts\Operational\ManifestDetail;
use App\Abstracts\AdditionalField;
use App\Abstracts\JobOrderDetail;
use App\Abstracts\Contact;
use Illuminate\Support\Facades\DB;
class Manifest
{
    protected static $table = 'manifests';
    public static function query($request = []) {
        $request = self::fetchFilter($request);
        $dt = DB::table(self::$table);
        $dt = $dt->leftJoin('routes', 'routes.id', self::$table . '.route_id');
        if($request['job_order_id']) {
            $details = ManifestDetail::query(['job_order_id' => $request['job_order_id']
            ]);
            $manifests = $details->pluck('manifest_details.header_id');
            $dt = $dt->whereIn(self::$table . '.id', $manifests);
        }
        return $dt;
    }
    public static function fetchFilter($args = []) {
        $params = [];
        $params['job_order_id'] = $args['job_order_id'] ?? null;
        return $params;
    }
    public static function indexRequestedMail($source) {
        $dt = DB::table(self::$table);
        $sentManifest = DB::table('manifest_email_logs')->select('manifest_id');
        $sentManifest = $sentManifest->toSql();
        $dt = $dt->whereRaw("id NOT IN ($sentManifest)");
        if($source) {
            $dt = $dt->where(self::$table . '.source', $source);
        }
        $dt = $dt->get();
        return $dt;
    }
    /*
      Date : 17-02-2021
      Description : Menangkap parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($params = []) {
        $args = [];
        $args['company_id'] = $params['company_id'] ?? null;
        $args['vehicle_type_id'] = $params['vehicle_type_id'] ?? null;
        $args['route_id'] = $params['route_id'] ?? null;
        $args['vehicle_id'] = $params['vehicle_id'] ?? null;
        $args['reff_no'] = $params['reff_no'] ?? null;
        $args['driver_id'] = $params['driver_id'] ?? null;
        $args['date_manifest'] = $params['date_manifest'] ?? null;
        $args['date_manifest'] = Carbon::parse($args['date_manifest']);
        $args['description'] = $params['description'] ?? null;
        $etd_date = $params['etd_date'] ?? null;
        $etd_time = $params['etd_time'] ?? null;
        $args['etd_time'] = createTimestamp($etd_date, $etd_time);
        $eta_date = $params['eta_date'] ?? null;
        $eta_time = $params['eta_time'] ?? null;
        $args['eta_time'] = createTimestamp($eta_date, $eta_time);
        return $args;
    }
    /*
      Date : 12-02-2021
      Description : Memvalidasi keberaraan data manifest
      Developer : Didin
      Status : Create
    */
    public static function validate($id, $source = null, $is_crossdocking = 0) {
        $dt = DB::table('manifests')
        ->whereId($id);
        if($source) {
            switch($source) {
                case 'job_order' :
                    $dt->where('manifests.source', $source);
                    break;
                case 'picking_order' :
                    $dt->where('manifests.source', $source);
                    break;
            }
        }
        if($is_crossdocking) {
            $dt = $dt->where('manifests.is_crossdocking', 1);
        }
        $dt = $dt->first();
        if(!$dt) {
            throw new Exception('Data not found');
        }
    }
    public static function show($id, $source = null, $is_crossdocking = null) {
        self::validate($id, $source, $is_crossdocking);
        $dt = M::with('vehicle_type','delivery','container','container_type','company:id,name','trayek:id,name,distance', 'commodity', 'job_status:id,name')->where('manifests.id', $id);
        if($source) {
            switch($source) {
                case 'job_order' :
                    $dt->where('manifests.source', $source);
                    break;
                case 'picking_order' :
                    $dt->where('manifests.source', $source);
                    break;
            }
        }
        if($is_crossdocking) {
            $dt = $dt->where('manifests.is_crossdocking', 1);
        }
        $dt = $dt->first();
        $dt->additional = json_decode($dt->additional);
        $inManifest = AdditionalField::indexKey('jobOrder', ['show_in_manifest' => 1]);
        if(count($inManifest) > 0) {
            $jo = JobOrderDetail::query();
            $jo->where('manifest_details.header_id', $id);
            foreach($inManifest as $m) {
                $jo = $jo->addSelect([DB::raw("GROUP_CONCAT(REPLACE(JSON_EXTRACT(job_orders.additional, '$.$m'), '\"', '') SEPARATOR ',') AS $m")]);
            }
            $dt->job_order = $jo->first();
        }
        $dt->delivery_order_driver_id = DeliveryOrderDriver::getIdByManifest($id);
        return $dt;
    }
    /*
      Date : 12-02-2021
      Description : Menghapus manifest
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validate($id);
        DB::beginTransaction();
        $deliveryOrder = DeliveryOrderDriver::showByManifest($id);
        if($deliveryOrder) {
            DB::table('vehicles')
            ->whereDeliveryId($deliveryOrder->id)
            ->update(['delivery_id' => null]);
            DeliveryOrderDriver::destroy($deliveryOrder->id);
        }
        DB::table('manifests')
        ->whereId($id)
        ->delete();
        DB::commit();
    }
    /*
      Date : 12-02-2021
      Description : Meng-update manifest
      Developer : Didin
      Status : Create
    */
    public static function update($args = [], $id) {
        self::validate($id);
        DB::beginTransaction();
        $params = self::fetch($args);
        DB::table('manifests')
        ->whereId($id)
        ->update($params);
        DB::commit();
    }
    /*
      Date : 12-02-2021
      Description : Menyimpan data additional
      Developer : Didin
      Status : Create
    */
    public static function storeAdditional($params = [], $id) {
        $manifest = self::show($id);
        $origin = $manifest->additional;
        $keys = collect(array_keys($params));
        $manifestKeys = AdditionalField::indexKey('manifest');
        $keys = $keys->intersect($manifestKeys);
        $data = [];
        foreach ($keys as $k) {
            $data[$k] = $params[$k];
        }
        $params = $data;
        $params = collect($params)->union($origin);
        $params = $params->all();
        $json = json_encode($params);
        $update['additional'] = $json;
        DB::table('manifests')
        ->whereId($id)
        ->update($update);
    }
    public static function setNullableAdditional($id) {
        $dt = self::show($id);
        if(!$dt->additional) {
            $data['additional'] = '{}';
            DB::table('manifests')
            ->whereId($id)
            ->update($data);
        }
    }
    public static function getCustomers($id) {
        $dt = self::show($id);
        $customers = [];
        $details = ManifestDetail::query(['header_id' => $id]);
        $details = $details->select('job_orders.customer_id')->groupBy('job_orders.customer_id');
        $customers = $details->pluck('job_orders.customer_id')->toArray();
        return $customers;
    }
    public static function getEmails($id) {
        $customers = self::getCustomers($id);
        $emails = Contact::getEmails($customers);
        return $emails;
    }
    public static function getDeliveryOrder($id){
        $deliveryOrder=DB::table('delivery_order_drivers as dod')
        ->leftJoin('contacts as driver','driver.id','dod.driver_id')
        ->leftJoin('contacts as vendor','vendor.id','driver.parent_id')
        ->leftJoin('vehicles','vehicles.id','dod.vehicle_id')
        ->leftJoin('job_statuses','job_statuses.id','dod.job_status_id')
        ->leftJoin('delivery_manifests', 'dod.id', 'delivery_manifests.delivery_order_driver_id')
        ->where('delivery_manifests.manifest_id', $id)
        ->where('dod.status', '<', 3)
        ->selectRaw('
        dod.id,
        dod.code as code_sj,
        dod.code_external as code_sj_external,
        dod.is_internal,
        dod.vehicle_id,
        dod.status,
        dod.driver_id,
        dod.vendor_id,
        job_statuses.name as status,
        vendor.name as vendor_name,
        if(dod.driver_id is not null,driver.name, dod.driver_name) as sopir,
        if(dod.vehicle_id is not null,vehicles.nopol, dod.nopol) as kendaraan')->get();
        return $deliveryOrder;
    }
    /*
      Date : 20-01-2022
      Description : Mengupdate nilai total revenue pada manifests
      Developer : Lyo
      Status : Create
    */
    public static function updateRevenue($manifest_id){
      $m = DB::table('manifests')
      ->leftJoin('manifest_details','manifest_details.header_id','manifests.id')
      ->leftJoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
      ->leftJoin('job_orders','job_orders.id','job_order_details.header_id')
      ->where('manifests.id',$manifest_id)
      ->select(
        'manifests.id',
        'manifests.is_full',
        'job_order_details.imposition',
        'job_orders.service_type_id'
      )->first();
      if(!isset($m->id)){
        throw new Exception("Manifest tidak ditemukan !!", 422);
      }
      if($m->is_full == 1){
        $data = DB::table('manifests')
        ->select(DB::raw('SUM(job_order_details.total_price) as total'))
        ->leftJoin('manifest_details','manifest_details.header_id','manifests.id')
        ->leftjoin('job_order_details','job_order_details.id','manifest_details.job_order_detail_id')
        ->where('manifests.id',$m->id)->first();
      }else if($m->is_full == 0){
        if($m->imposition == 1){
          $data = DB::table('manifests')
          ->select(DB::raw('(
            SUM(jod.price * (md.transported * (jod.long * jod.wide * jod.high) / 1000000))
          ) as total'))
          ->leftJoin('manifest_details as md','md.header_id','manifests.id')
          ->leftjoin('job_order_details as jod','jod.id','md.job_order_detail_id')
          ->where('manifests.id',$m->id)->first();
        }else if($m->imposition == 2){
          $data = DB::table('manifests')
          ->select(DB::raw('(
            CASE
              WHEN jod.volume > (jod.long * jod.wide * jod.high * md.transported) / 6000 THEN SUM(jod.price * jod.volume)
              WHEN jod.volume < (jod.long * jod.wide * jod.high * md.transported) / 6000 THEN SUM(jod.price * (jod.long * jod.wide * jod.high * md.transported) / 6000)
            END
          ) as total'))
          ->leftJoin('manifest_details as md','md.header_id','manifests.id')
          ->leftjoin('job_order_details as jod','jod.id','md.job_order_detail_id')
          ->where('manifests.id',$m->id)->first();
        }else if($m->imposition == 3){
          $data = DB::table('manifests')
          ->select(DB::raw('(
            SUM(jod.price * md.transported)
          ) as total'))
          ->leftJoin('manifest_details as md','md.header_id','manifests.id')
          ->leftjoin('job_order_details as jod','jod.id','md.job_order_detail_id')
          ->where('manifests.id',$m->id)->first();
        }
      }else if($m->service_type_id == 4){
        $data = DB::table('manifests')
        ->select(DB::raw('(
          SUM(jod.price_total / md.transported)
        ) as total'))
        ->leftJoin('manifest_details as md','md.header_id','manifests.id')
        ->leftjoin('job_order_details as jod','jod.id','md.job_order_detail_id')
        ->where('manifests.id',$m->id)->first();
      }
      // dd($data->total);
      DB::table('manifests')->whereId($m->id)->update([
        'revenue' => $data->total ?? 0
      ]);
    }

    public static function forceFinish($id)
    {
      $manifest = M::find($id);
      $status = [];

      $jo = DB::table('manifests')
      ->leftJoin('manifest_details', 'manifest_details.header_id','manifests.id')
      ->leftJoin('job_order_details', 'job_order_details.id','manifest_details.job_order_detail_id')
      ->leftJoin('job_orders', 'job_orders.id','job_order_details.header_id')
      ->where('manifests.id' , $id)
      ->select('job_orders.service_type_id')
      ->first();

      if($jo){
        if(in_array($jo->service_type_id, [1,2,3,4])){
          $status = [
            0 => 2,     //Assigned To Driver
            1 => 6,     //Muat Barang
            2 => 9,     //Bongkar Muatan
            3 => 10,    //Selesai Bongkar
            4 => 0,     //Status tidak bisa terupdate
        ];
        }
        elseif(in_array($jo->service_type_id, [20,21,24])){
            $status = [
              1 => 2,     //Assigned To Driver
              2 => 17,    //Muat Container
              3 => 19,    //Bongkar Container
              4 => 10,    //Selesai Bongkar
              5 => 0,     //Status tidak bisa terupdate
        ];
        }
        elseif(in_array($jo->service_type_id, [22,27])){
            $status = [
              1 => 2,     //Assigned To Driver
              2 => 17,    //Muat Container
              3 => 9,     //Bongkar Muatan
              4 => 10,    //Selesai Bongkar
              5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [23])){
            $status = [
              0 => 2,     //Assigned To Driver
              1 => 17,    //Muat Container
              2 => 9,     //Bongkar Muatan
              3 => 19,    //Bongkar Container
              4 => 10,    //Selesai Bongkar
              5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [28])){
            $status = [
              0 => 2,     //Assigned To Driver
              1 => 17,    //Muat Container
              2 => 6,     //Muat Barang
              3 => 19,    //Bongkar Container
              4 => 10,    //Selesai Bongkar
              5 => 0,     //Status tidak bisa terupdate
            ];
        }
        elseif(in_array($jo->service_type_id, [25,26])){
            $status = [
              0 => 2,     //Assigned To Driver
              1 => 6,     //Muat Barang
              2 => 19,    //Bongkar Container
              3 => 10,    //Selesai Bongkar
              4 => 0,     //Status tidak bisa terupdate
            ];
        }else{
            $status = [
              0 => 2,     //Assigned To Driver
              1 => 6,     //Muat Barang
              2 => 9,     //Bongkar Muatan
              3 => 10,    //Selesai Bongkar
              4 => 17,    //Muat Container
              5 => 19,    //Bongkar Container
              6 => 0,     //Status tidak bisa terupdate
            ];
        }
      }

      DB::beginTransaction();
      
      if($manifest->job_status_id !== "11" && $manifest->job_status_id !== "14"){
        // Melakukan update status manifest log sampai selesai
        $status_no = array_search($manifest->job_status_id, $status);
        $next_status = $status_no + 1;
        if(count($status) > 0){
          while($status[$next_status] !== 0) {
            ManifestStatusLog::create([
              'manifest_id' => $id,
              'job_status_id' => $status[$next_status], //Selesai
              'created_by' => auth()->id(),
            ]);
            $next_status = $next_status + 1;
          }
        }
  
        ManifestStatusLog::create([
          'manifest_id' => $id,
          'job_status_id' => 11, //Selesai
          'created_by' => auth()->id(),
        ]);

        $dod = DB::table('delivery_order_drivers')
        ->select('delivery_order_drivers.*')
        ->join('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', 'delivery_order_drivers.id')
        ->where('manifest_id', $id)
        ->where('status', '<', 3)
        ->first();

        DB::table('delivery_order_drivers')->where('id',$dod->id)->update(['job_status_id'=>11, 'is_finish' => 0]);
  
      }

      $manifest->update([
        'job_status_id' => 11 //Selesai
      ]);

      DB::commit();
    }
}
