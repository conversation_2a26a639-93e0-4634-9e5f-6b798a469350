<?php
namespace App\Http\Controllers\Api;
use App\Abstracts\Operational\V2\DeliveryOrderDriver;
use App\Jobs\HitungJoCostManifestJob;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\{RegisterBbm,RegisterBbmDetail,Contact,CostType,Manifest, ManifestCost};
use App\Abstracts\ManifestCost AS MC;
use Response;
use DB;
use Yajra\DataTables\DataTables;
use Carbon\Carbon;
use App\Http\Controllers\Operational\ManifestFTLController as ManifestFTLController;
class RegisterbbmController extends Controller{
    public function store(Request $request){
        $request->validate([
            'date_request' => 'required',
            'requested_by' => 'required',
        ],[
            'date_request.required' => 'Tanggal pengajuan tidak boleh kosong',
            'requested_by.required' => 'Pengaju tidak boleh kosong',
        ]);
        $status_code = 200;
        $msg = 'OK';
        DB::beginTransaction();
        try {
            RegisterBbm::store($request->all());
            DB::commit();
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return response()->json($data, $status_code);
    }
    public function update(Request $request, $id)
    {
        $request->validate([
            'date_request' => 'required',
        ],[
            'date_request.required' => 'Tanggal pengajuan tidak boleh kosong',
        ]);
        $status_code = 200;
        $msg = 'OK';
        DB::beginTransaction();
        try {
            RegisterBbm::edit($request->all(), $id);
            DB::commit();
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return response()->json($data, $status_code);
    }
    public function show($id){
        $data['item'] = RegisterBbm::show($id);
        $data['detail'] = RegisterBbmDetail::index_spbu($id);
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }
    public function datatable(Request $request){
        $item = RegisterBbm::with('company','vehicle','deliveryorder','trayek','contact','driver');
        $item = $item->join('using_item_statuses', 'register_bbm.status', 'using_item_statuses.id');
        $item = $item->leftJoin('register_bbm_details', 'register_bbm_details.header_id', 'register_bbm.id');
        $item = $item->leftJoin('manifest_costs', 'manifest_costs.from_bbm', 'register_bbm_details.id');
        $start_date = $request->start_date;
        $start_date = $start_date != null ? Carbon::parse($start_date)->format("Y-m-d") : null;
        $end_date = $request->end_date;
        $end_date = $end_date != null ? Carbon::parse($end_date)->format("Y-m-d") : null;
        if($start_date) {
            $item = $item->where("date_request", ">=", $start_date);
        }
        if($end_date) {
            $item = $item->where("date_request", "<=", $end_date);
        }
        $start_date_penggunaan = $request->start_date_penggunaan;
        $start_date_penggunaan = $start_date_penggunaan != null ? new DateTime($start_date_penggunaan) : '';
        $end_date_penggunaan = $request->end_date_penggunaan;
        $end_date_penggunaan = $end_date_penggunaan != null ? new DateTime($end_date_penggunaan) : '';
        $item = $start_date_penggunaan != '' && $end_date_penggunaan != '' ? $item->whereBetween('date_pemakaian', [$start_date_penggunaan->format('Y-m-d'), $end_date_penggunaan->format('Y-m-d')]) : $item;
        $company_id = $request->company_id;
        $company_id = $company_id != null ? $company_id : '';
        $item = $company_id != '' ? $item->where('company_id', $company_id) : $item;
        $status = $request->status;
        $status = $status != null ? $status : '';
        $item = $status != '' ? $item->where('status', $status) : $item;
        $item->select('register_bbm.*', 'using_item_statuses.name AS status_name', 'manifest_costs.id AS mc_id',DB::raw('IFNULL(manifest_costs.status, "Draft") AS manifest_status'))
            ->orderByRaw('register_bbm.id DESC');
        if($request->is_pallet == 1) {
            $pallet = DB::table('items')
            ->join('categories', 'categories.id', 'items.category_id')
            ->join('categories AS parents', 'categories.parent_id', 'parents.id')
            ->whereRaw('categories.is_pallet = 1 OR parents.is_pallet = 1')
            ->select('items.id');
            $pallet = $pallet->toSql();
            $details = DB::table('register_bbm_details')
            ->select('header_id')
            ->whereRaw("item_id IN ($pallet)");
            $details = $details->toSql();
            $item = $item->whereRaw("register_bbm.id IN ($details)");
        }
        return DataTables::of($item)->make(true);
    }
    public function dod()
    {
        $request=new Request();
        $request['delivery_order_status']=1;
        $request['is_active']=1;
        $item = DeliveryOrderDriver::query($request->all());
        $item = $item->selectRaw('
        delivery_order_drivers.*,
        manifests.id as manifests_id,
        manifests.code as code_pl,
        driver.name as driver,
        routes.id as trayek_id,
        routes.name as trayek,
        job_statuses.name as status_name,
        if(delivery_order_drivers.driver_id is not null,driver.name, delivery_order_drivers.driver_name) as sopir,
        if(delivery_order_drivers.vehicle_id is not null,vehicles.nopol, delivery_order_drivers.nopol) as kendaraan
        ')
        ->groupBy('delivery_order_drivers.code')->get();
        return Response::json($item, 200, [], JSON_NUMERIC_CHECK);
    }
    public function reject($id){
        $status_code = 200;
        $msg = 'Data successfully updated';
        DB::beginTransaction();
        try {
            RegisterBbm::reject($id);
            DB::commit();
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return Response::json($data, $status_code);
    }
    public function approve($id){
        $status_code = 200;
        $msg = 'Data successfully updated';
        DB::beginTransaction();
        try {
            // RegisterBbm::itemOut($id);
            $rb = RegisterBbm::find($id);
            $jo = Manifest::find($rb->manifests_id);
            $dt = DB::table('register_bbm_details')->where('header_id', $id)->first();
            $m = ManifestCost::create([
                'header_id' => $rb->manifests_id,
                'company_id' => $jo->company_id,
                'cost_type_id' => $dt->cost_id,
                'transaction_type_id' => 21,
                'vendor_id' => $rb->spbu_id,
                'qty' => $dt->qty,
                'price' => $dt->cost,
                'total_price' => $dt->total,
                'description' => $dt->description,
                'is_internal' => $dt->is_internal,
                'is_generated' => 1,
                'type' => $dt->type,
                'create_by' => auth()->id(),
                'is_edit' => 1,
                'is_kasbon' => $dt->is_kasbon,
                'from_bbm' => $dt->id,
            ]);
            $rb->status = 2;
            $rb->approve_by = auth()->id();
            $rb->save();
            MC::storeVendorJob($m->id);
            DB::commit();
            HitungJoCostManifestJob::dispatch($rb->manifests_id);
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return Response::json($data, $status_code);
    }
    public function destroy($id){
        $status_code = 200;
        $msg = 'Data successfully removed';
        DB::beginTransaction();
        try {
            RegisterBbm::reject($id);
            // RegisterBbm::destroy($id);
            DB::commit();
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return Response::json($data, $status_code);
    }
    public function create()
    {
        $item['contact'] = Contact::where('is_vendor', 1)
        ->where('is_spbu', 1)
        ->select('id', 'name')
        ->get();
        $item['cost_type'] = CostType::where('is_bbm', 1)->get();
        return Response::json($item, 200, [], JSON_NUMERIC_CHECK);
    }
}