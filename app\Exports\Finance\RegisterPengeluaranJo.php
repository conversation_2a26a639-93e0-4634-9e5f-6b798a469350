<?php

namespace App\Exports\Finance;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class RegisterPengeluaranJo implements FromCollection, WithHeadings, WithMapping
{
    protected $request;
    function __construct(Request $request) {
      $this->request = $request;
    }
    public function collection()
    {
        $report = new \App\Http\Controllers\Finance\ReportController;
        return collect($report->get_register_pengeluaran_jo($this->request));
    }

    public function headings(): array
    {
        return [
            'Tanggal',
            'No BKK/BBK',
            'No Ref',
            'Kode Akun',
            'Kode Biaya & Nama Biaya',
            'Uraian',
            'No Job Order',
            'No Manifest',
            'Driver',
            'Nopol',
            'Type',
            'Amount',
        ];
    }
    public function map($data): array
    {
        return [
            $data['date_transaction'],
            $data['code'],
            $data['reff'],
            $data['kode_akun'] .' - '. $data['nama_akun'],
            $data['kode_biaya'] .' - '. $data['nama_biaya'],
            $data['description'],
            $data['kode_jo'],
            $data['kode_manifest'],
            $data['nama_sopir'],
            $data['nopol'],
            $data['type'],
            $data['amount'],
        ];
    }
}
