<?php
// <!-- Date : 04-08-2022 By : Felix
// Description : [Bug] menu edit trailer -->
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Vehicle\VehicleController;
use App\Model\Vehicle;
use App\Model\VehicleDistance;
use App\Model\VehicleChecklistItem;
use App\Model\VehicleMaintenance;
use App\Model\VehicleMaintenanceDetail;
use App\Model\TargetRate;
use App\Model\VehicleTire;
use App\Model\VehicleMutationRequest;
use App\Model\VehicleMutationIn;
use App\Model\VehicleMutationOut;
use App\Model\VehicleDocument;
use App\Model\VehicleTrailer;
use Response;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Mockery\Undefined;
use Yajra\DataTables\Facades\DataTables;

class VehicleApiController extends Controller
{
    /**
     * Date : 20-01-2022
     * Description : Create function vehicle_head_datatable() (khusus untuk vehicle is_trailer = 0)
     * Status : Create
     */
    public function vehicle_head_datatable(Request $request)
    {
        $wr = "1=1";
        if($request->company_id)
            $wr .= " AND vehicles.company_id = {$request->company_id}";

        /* if($request->filled('is_trailer') && $request->is_trailer == 1){
          $wr.=" AND vehicles.is_trailer = 1";
        }else{
          $wr.=" AND vehicles.is_trailer = 0"; //Default
        } */

        $wr.=" AND vehicles.is_trailer = 0"; //Default

        if ($request->filled('is_internal')) {
            $wr.=" AND vehicles.is_internal = $request->is_internal";
        }

        $item = Vehicle::with('company','company.area','vehicle_variant','vehicle_variant.vehicle_type','supplier')
        ->whereRaw($wr)
        ->when($request->vehicle_type_id, function($query) use($request) {
            $query->whereHas('vehicle_variant', function($query) use($request) {
                $query->where('vehicle_type_id', $request->vehicle_type_id);
            });
        })
        ->select('vehicles.*');

        return DataTables::of($item)
        ->addColumn('action', function($item){
          $html = "";
          if($item->is_trailer){
            $html = "";
            $html.="<a ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            if(!$item->nopol){
              $html.="<a ui-sref=\"vehicle.vehicle_trailer.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            }
            $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
          }else{
            $html = "";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.detail')\" ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.edit')\" ui-sref=\"vehicle.vehicle_head.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.delete')\" ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
          }
            return $html;
        })
        // ->filter(function($query) use ($request ) {
        //     if(isset($request->is_internal)) {
        //         $query->whereIsInternal($request->is_internal ?? 0);
        //     }

        //     if(isset($request->search['value'])){
        //         $query->where(DB::raw('LOWER(vehicles.nopol)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        //         $query->orWhere(DB::raw('LOWER(vehicles.code)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        //     }

        //     if(isset($request->vehicle_type_id)) {
        //         $vehicle_type_id = $request->vehicle_type_id;
        //         $query->whereHas('vehicle_variant', function (Builder $q) use ($vehicle_type_id) {
        //             $q->where('vehicle_type_id', $vehicle_type_id);
        //         });
        //     }
        // })
        ->editColumn('last_km', function($item){
            return number_format($item->last_km).' km';
        })
        ->rawColumns(['action'])
        ->make(true);
    }

    

    /**
     * Date : 24-01-2022
     * Description : Memperbaiki query vehicle_datatable()
     * Developer : Syahrul
     * Status : Edit
     */

    public function vehicle_datatable(Request $request)
    {
        // dd($request->is_internal);
        $wr = "1=1";
        if($request->company_id){
          $wr .= " AND vehicles.company_id =". (int)$request->company_id;
        }
        if($request->vehicle_type_id){
          $wr .= " AND vehicle_variants.vehicle_type_id =". (int)$request->vehicle_type_id;
        }
        if($request->is_internal != null){
          $wr .= " AND vehicles.is_internal =". (int)$request->is_internal;
        }
        // if($request->filled('is_trailer') && $request->is_trailer == 1){
        //   $wr.=" AND vehicles.is_trailer = 1";
        // }else{
        //   $wr.=" AND vehicles.is_trailer = 0";
        // }
        $item = Vehicle::with('company','company.area','vehicle_variant','vehicle_variant.vehicle_type','supplier')
        ->whereRaw($wr)
        ->when($request->filled('is_trailer'), function($query) use($request) {
            if ($request->is_trailer == 1) {
                $query->where('vehicles.is_trailer', 1);
            } else {
                $query->where('vehicles.is_trailer', 0);
            }
        })
        ->select('vehicles.*', 'vehicle_variants.vehicle_type_id')
        ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id');

        return DataTables::of($item)
        ->addColumn('action', function($item){
          $html = "";
          if($item->is_trailer){
            $html = "";
            $html.="<a ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            // [Bug] menu edit trailer
            // if(!$item->nopol){
              $html.="<a ui-sref=\"vehicle.vehicle_trailer.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            // }
            // end [Bug] menu edit trailer
            $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
          }else{
            $html = "";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.detail')\" ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.edit')\" ui-sref=\"vehicle.vehicle.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.delete')\" ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
          }
            return $html;
        })
        ->addColumn('head_trailer', function($item) {
            if($item->is_trailer) {
                return 'Trailer';
            } else {
                return 'Head';
            }
        })
        // ->filter(function($query) use ($request ) {
        //     if(isset($request->is_internal)) {
        //         $query->whereIsInternal($request->is_internal ?? 0);
        //     }

        //     if(isset($request->search['value'])){
        //         $query->where(DB::raw('LOWER(vehicles.nopol)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        //         $query->orWhere(DB::raw('LOWER(vehicles.code)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        //     }

        //     if(isset($request->vehicle_type_id)) {
        //         $vehicle_type_id = $request->vehicle_type_id;
        //         $query->whereHas('vehicle_variant', function (Builder $q) use ($vehicle_type_id) {
        //             $q->where('vehicle_type_id', $vehicle_type_id);
        //         });
        //     }
        // })
        ->editColumn('last_km', function($item){
            return number_format($item->last_km).' km';
        })
        ->rawColumns(['action'])
        ->make(true);
    }
    public function vehicle_distance_datatable(Request $request)
    {
        $wr = '1=1';

        if($request->vehicle_id) {
            $wr .= " AND vehicle_distances.vehicle_id = {$request->vehicle_id}";
        } else {
            if($request->company_id)
                $wr .= " AND vehicles.company_id = {$request->company_id}";
        }

        if($request->start_date)
            $wr .= ' AND vehicle_distances.date_distance >= "'. dateDB($request->start_date) .'"';

        if($request->end_date)
            $wr .= ' AND vehicle_distances.date_distance <= "'. dateDB($request->end_date) .'"';

        $item = VehicleDistance::with('vehicle')
        ->leftJoin('vehicles', 'vehicles.id', 'vehicle_distances.vehicle_id')
        ->whereRaw($wr)
        ->select('vehicle_distances.*');

        return DataTables::of($item)
        ->editColumn('distance', function($item){
            return number_format($item->distance);
        })
        ->make(true);
    }
    public function vehicle_check_datatable(Request $request)
    {
        $wr = '1=1';

        if($request->company_id)
            $wr .= " AND vehicle_checklist_items.company_id = {$request->company_id}";

        if($request->vehicle_id)
            $wr .= " AND vehicle_checklist_items.vehicle_id = {$request->vehicle_id}";

        if($request->start_date)
            $wr .= ' AND vehicle_checklist_items.date_transaction >= "'. dateDB($request->start_date) .'"';

        if($request->end_date)
            $wr .= ' AND vehicle_checklist_items.date_transaction <= "'. dateDB($request->end_date) .'"';

        $item = VehicleChecklistItem::with('vehicle','company')
        ->whereRaw($wr)
        ->select('vehicle_checklist_items.*');

        return DataTables::of($item)
        ->addColumn('action', function($item){
            // $html = "<a ng-show=\"roleList.includes('vehicle.checklist.create')\" ui-sref=\"vehicle.vehicle_check.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
            $html = "<a ng-show=\"roleList.includes('vehicle.checklist.create')\" ng-click=\"deletes($item->id)\"><span class='fa fa-trash' data-toggle='tooltip' title='Delete Data'></span></a>";
            return $html;
        })
        ->rawColumns(['action'])
        ->make(true);
    }
    public function target_rate_datatable(Request $request)
    {
        $item = DB::select("select id,DATE_FORMAT(period,'%M') as months, plan, realisasi, DATE_FORMAT(updated_at,'%d-%m-%Y %H:%i') as up_at from target_rates where vehicle_id = '$request->vehicle_id' AND year(period) = '$request->year' group by months order by period asc");
        // dd($item);
        return DataTables::of($item)
        ->addColumn('action', function($item){
            $html="<a ng-show=\"roleList.includes('vehicle.vehicle.detail.detail.ritase.edit')\" ng-click=\"edits($item->id,$item->plan)\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>";
            return $html;
        })
        ->rawColumns(['action'])
        ->make(true);
    }

    public function get_vehicle(Request $request)
    {
        $wr="1=1";
        if (isset($request->company_id)) {
            $wr.=" AND company_id = $request->company_id";
        }
        $c=Vehicle::whereRaw($wr)->select('id','code','nopol')->get();
        return response()->json($c, 200);
    }

    public function maintenance_datatable(Request $request)
    {
        $wr="1=1";
        if (isset($request->vehicle_id)) {
            $wr.=" AND vehicle_maintenances.vehicle_id = ".$request->vehicle_id;
        }
        if (isset($request->status)) {
          if($request->status == 2){
            $wr.=" AND vehicle_maintenances.status IN (2, 3, 4, 5, 6, 8)";
          } else if($request->status == 9) {
            $wr.=" AND vehicle_maintenances.status IN (7, 9, 10, 11)";
          } else {
            $wr.=" AND vehicle_maintenances.status = ".$request->status;
          }
        }
        // $item = VehicleMaintenance::with('vendor')->whereRaw($wr)->select('vehicle_maintenances.*');
        $item=DB::table('vehicle_maintenances')
        ->leftJoin('contacts','contacts.id','vehicle_maintenances.vendor_id')
        ->leftJoin('maintenance_statuses','maintenance_statuses.id','vehicle_maintenances.status')
        ->leftJoin('vehicle_maintenance_items','vehicle_maintenance_items.vehicle_maintenance_id','vehicle_maintenances.id')
        ->whereRaw($wr)
        ->selectRaw('
            vehicle_maintenances.*,
            contacts.name as vendor,
            maintenance_statuses.name as status_name,
            sum(vehicle_maintenance_items.total_price) as total_biaya_item,
            sum(vehicle_maintenance_items.price) as biaya_rencana ,
            sum(vehicle_maintenance_items.price * vehicle_maintenance_items.qty) as total_rencana,
            sum(vehicle_maintenance_items.price_realisasi  * vehicle_maintenance_items.qty) as total_realisasi
            ')->groupBy('vehicle_maintenances.id');

        return DataTables::of($item)
        ->addColumn('action', function($item){
            $html="<a ng-show=\"roleList.includes('vehicle.vehicle.detail.maintenance.submission.detail')\" ui-sref=\"vehicle.vehicle.show.maintenance.show({id:$item->vehicle_id,vm_id:$item->id})\"><span class='fa fa-truck'></span></a>&nbsp;&nbsp;";
            // jika status maintenance = pengajuan
            // if ($item->status==3) {
            //     $html.="<a ui-sref=\"vehicle.vehicle.show.maintenance.show.edit_pengajuan({id:$item->vehicle_id,vm_id:$item->id})\"><span class='fa fa-pencil'></span></a>&nbsp;&nbsp;";
            // }
            // end jika status maintenance = pengajuan
            // if ($item->status == 1) {
            //     $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.detail.maintenance.submission.delete')\" ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
            // }
            return $html;
        })
        ->editColumn('name', function($item){
            return "<a ui-sref='vehicle.vehicle.show.maintenance.show({id:$item->vehicle_id,vm_id:$item->id})'>$item->name</a>";
        })
        // edit form km_pengajuan & total_pengajuan jika status maintenance = pengajuan
        ->editColumn('km_pengajuan', function($item){
          return number_format($item->km_rencana);
        })
        ->editColumn('total_pengajuan', function($item){
            return number_format($item->total_rencana);
        })
        // end edit form km_pengajuan & total_pengajuan jika status maintenance = pengajuan
        ->editColumn('km_rencana', function($item){
            return number_format($item->km_rencana);
        })
        ->editColumn('total_rencana', function($item){
            return number_format($item->total_rencana);
        })
        ->filterColumn('total_biaya_item', function($query, $keyword) {
        })
        ->filterColumn('total_rencana', function($query, $keyword) {
        })
        ->rawColumns(['action','status_name_html','name'])
        ->make(true);
    }

    public function maintenance_detail_datatable(Request $request, $vm_id)
    {
        $wr="1=1";
        $item = VehicleMaintenanceDetail::with('item','vehicle_maintenance_type')->where('header_id', $vm_id)->whereRaw($wr)->select('vehicle_maintenance_details.*');

        return DataTables::of($item)
        ->addColumn('action', function($item){
            if ($item->qty_realisasi<1) {
                $html="<a ng-click='editItem($item->id,$item->qty_rencana,$item->cost_rencana)'><span class='fa fa-pencil-square-o'></span></a>&nbsp;&nbsp;";
            } else {
                $html="<a ng-click='editItem($item->id,$item->qty_realisasi,$item->cost_realisasi)'><span class='fa fa-pencil-square-o'></span></a>&nbsp;&nbsp;";
            }
            if ($item->header->status<4) {
                $html.="<a ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
            }
            return $html;
        })
        ->editColumn('qty_rencana', function($item){
            return number_format($item->qty_rencana);
        })
        ->editColumn('cost_rencana', function($item){
            return number_format($item->cost_rencana);
        })
        ->rawColumns(['action'])
        ->make(true);
    }

    public function vehicle_maintenance_bill_datatable(Request $request){
      $total_realisasi_sub = DB::raw("(
        SELECT vm.id, SUM(vmi.total_price) AS total_realisasi
        FROM vehicle_maintenances AS vm INNER JOIN vehicle_maintenance_items AS vmi ON vm.id = vmi.vehicle_maintenance_id
        GROUP BY vm.id
      ) as total_realisasi_sub");
      $item=DB::table('vehicle_maintenances')
      ->leftJoin($total_realisasi_sub,'total_realisasi_sub.id','vehicle_maintenances.id')
      ->leftJoin('invoice_vendor_details','invoice_vendor_details.reff_no', DB::raw('CONCAT(vehicle_maintenances.code, " ", vehicle_maintenances.name)'))
      ->where('invoice_vendor_details.reff_no', null)
      ->where('status',12)->where('is_internal',0);
      if($request->filled('vendor_id')){
        $item->where('vehicle_maintenances.bengkel_id',$request->vendor_id);
        $item->orWhere('vehicle_maintenances.vendor_id',$request->vendor_id);
      }

      $item->select(
        'vehicle_maintenances.id as vm_id',
        'name',
        'code',
        'date_realisasi',
        'vehicle_maintenances.description',
        'cost_realisasi',
        DB::raw("(
          select IFNULL(total_realisasi_sub.total_realisasi,0) + IFNULL(cost_realisasi,0)
        ) as total")
      );
      return DataTables::of($item)
      ->make(true);
    }

    public function hitung_perawatan($vehicle_id)
    {
        $data['pengajuan']=VehicleMaintenance::where('vehicle_id', $vehicle_id)->where('status', 1)->count();
        $data['rencana']=VehicleMaintenance::where('vehicle_id', $vehicle_id)->whereIn('status', [ 2, 3, 4, 5, 6,  8])->count();
        $data['perawatan']=VehicleMaintenance::where('vehicle_id', $vehicle_id)->whereIn('status', [ 7, 9, 10, 11])->count();
        $data['selesai']=VehicleMaintenance::where('vehicle_id', $vehicle_id)->whereIn('status', [12])->count();
        return response()->json($data, 200);
    }

    public function manajemen_ban($vehicle_id){
      // dd("$vehicle_id, ini id vehicle dari fungsi manajemen ban di VehicleApiController");
      
      /**
       * Date : 12-11-2022
       * Description : Mengecek apakah membawa vehicle_id head dan trailer atau hanya salah satu vehicle_id
       * Developer : Sendy
       * Status : Update
      */

      if( strpos(',', $vehicle_id) !== false ) {
        $id[0] = $vehicle_id;
        $id[1] = '';
      }else {
        $id = explode(',', $vehicle_id);
      }
      
      $item = VehicleTire::select(
        'vehicle_joint_details.name as posisi',
        DB::raw('COALESCE(warehouse_stock_details.tire_batch_no, "-") as emboss'),
        DB::raw('IF(categories.is_radial = 1,"is_radial",IF(categories.is_bias = 1, "is_bias", "-")) as jenis'),
        'km_tires.bias_std_km as km_standart_bias',
        'km_tires.radial_std_km as km_standart_radial',
        'km_tires.vulkanisir_std_km as km_standart_vulkanisir',
        DB::raw('round(warehouse_stock_details.tire_last_km,0) as km_tempuh'),
        DB::raw('round(warehouse_stock_details.tire_last_km_gps,0) as km_tempuh_gps'),
        // 'initial_km as km_sisa', //data dummy
        DB::raw('0 as ketebalan') //data dummy
      )
      ->join('vehicle_joint_details','vehicle_tires.vehicle_joint_detail_id','=','vehicle_joint_details.id')
      ->join('vehicle_joints','vehicle_joints.id','=','vehicle_joint_details.vehicle_joint_id')
      ->leftJoin('warehouse_stock_details','warehouse_stock_details.id','=','vehicle_tires.wh_stock_ban_luar_id')
      ->leftJoin('racks','racks.id','=','warehouse_stock_details.rack_id')
      ->leftJoin('warehouses','warehouses.id','=','racks.warehouse_id')
      ->leftJoin('km_tires','km_tires.company_id','=','warehouses.company_id')
      ->leftJoin('items','items.id','=','warehouse_stock_details.item_id')
      ->leftJoin('categories','categories.id','=','items.category_id')
      ->whereIn('vehicle_tires.vehicle_id', [$id[0],$id[1]])->get();
        // dd($item);
      return DataTables::of($item)
      ->addColumn('km_standart', function($item){
        if($item->jenis == 'is_bias'){
            $kmStd = (int) $item->km_standart_bias;
        } else if($item->jenis == 'is_radial'){
          $kmStd = (int) $item->km_standart_radial;
        } else {
          $kmStd = (int) $item->km_standart_vulkanisir;
        }
        return $kmStd;
      })
      ->addColumn('km_sisa', function($item){
        if($item->jenis == 'is_bias'){
          $kmSisa = 0;
          if($item->km_standart_bias){
            $kmSisa = (int) $item->km_standart_bias - $item->km_tempuh;
          }
        } else if($item->jenis == 'is_radial'){
          $kmSisa = 0;
          if($item->km_standart_radial){
            $kmSisa = (int) $item->km_standart_radial - $item->km_tempuh;
          }
        } else {
          $kmSisa = 0;
        }
        return $kmSisa;
      })
      ->addColumn('km_sisa_gps', function($item){
        if($item->jenis == 'is_bias'){
          $kmSisa = 0;
          if($item->km_standart_bias){
            $kmSisa = (int) $item->km_standart_bias - $item->km_tempuh_gps;
          }
        } else if($item->jenis == 'is_radial'){
          $kmSisa = 0;
          if($item->km_standart_radial){
            $kmSisa = (int) $item->km_standart_radial - $item->km_tempuh_gps;
          }
        } else {
          $kmSisa = 0;
        }
        return $kmSisa;
      })
      ->make(true);
    }

    public function riwayat_pemasangan($vehicle_id){
      // dd("$vehicle_id, ini id vehicle dari fungsi riwayat pemasangan di VehicleApiController");

      $item = VehicleTire::select(
        'vehicle_tires.description',
        'vehicle_joint_details.name as posisi',
        DB::raw('COALESCE(warehouse_stock_details.tire_batch_no, "-") as emboss'),
        DB::raw('COALESCE(warehouse_stock_details.serial_no, "-") as no_seri'),
        'warehouse_stock_details.tire_last_km as km_tempuh',
        'warehouse_stock_details.tire_last_km_gps as km_tempuh_gps',
        'tire_categories.name as category_name',
        'tire_manufacturers.name as manufacture_name',
        'tire_sizes.name as size_name',
        'tire_types.name as type_name',
        'using_tires.date_of_use',
        'using_tires.wh_stock_ban_luar_id',
        DB::raw('IF(using_tires.wh_stock_ban_luar_id, IF(using_tires.wh_stock_ban_luar_id = vehicle_tires.wh_stock_ban_luar_id, "Aktif", "Nonaktif"), "-") as status')
      )
      ->join('vehicles', 'vehicles.id', 'vehicle_tires.vehicle_id')
      ->leftJoin('vehicle_joint_details', 'vehicle_joint_details.id', 'vehicle_tires.vehicle_joint_detail_id')
      ->leftJoin('using_tires', function($join){
        $join->on('using_tires.vehicle_tire_id','=','vehicle_tires.id');
        $join->orderBy('using_tires.id');
        $join->limit('1');
      })
      ->leftJoin('warehouse_stock_details','warehouse_stock_details.id','=','using_tires.wh_stock_ban_luar_id')
      ->leftJoin('items','items.id','=','warehouse_stock_details.item_id')
      ->leftJoin('tire_manufacturers','tire_manufacturers.id','=','items.tire_manufacture_id')
      ->leftJoin('tire_sizes','tire_sizes.id','=','items.tire_size_id')
      ->leftJoin('tire_categories','tire_categories.id','=','items.tire_category_id')
      ->leftJoin('tire_types','tire_types.id','=','items.tire_type_id')
      ->where('vehicle_tires.vehicle_id', $vehicle_id);

      return DataTables::of($item)
      ->addColumn('kendaraan', function($item){
        return 'head/trailer';
      })
      ->make(true);
    }

    public function general_maintenance_datatable(Request $request){
      $wr="1=1";
      if (isset($request->status)) {
        if($request->status == 2){
          $wr.=" AND vehicle_maintenances.status IN (2, 3, 4, 5, 6, 8)";
        } else if($request->status == 9) {
          $wr.=" AND vehicle_maintenances.status IN (7, 9, 10, 11)";
        } else {
          $wr.=" AND vehicle_maintenances.status = ".$request->status;
        }
      }
      $item = VehicleMaintenance::select(
        'vehicle_maintenances.*', 
        DB::raw('FORMAT(SUM(vehicle_maintenance_details.cost_rencana * vehicle_maintenance_details.qty_rencana), 2) as biaya_rencana'), 
        DB::raw('FORMAT(SUM(vehicle_maintenance_details.cost_realisasi * vehicle_maintenance_details.qty_realisasi), 2) as biaya_realisasi'),
        'contacts.name as vendor',
        'companies.name as company_name',
        'maintenance_statuses.name as status_name',
        DB::raw('if(vehicles.nopol = "", vehicles.chassis_no, vehicles.nopol) as nopol'),
        'trailer.code as trailer_code'
        )
      ->leftJoin('maintenance_statuses','maintenance_statuses.id','vehicle_maintenances.status')
      ->leftJoin('vehicle_maintenance_details','vehicle_maintenance_details.header_id','vehicle_maintenances.id')
      ->leftJoin('vehicle_maintenance_items','vehicle_maintenance_items.vehicle_maintenance_id','vehicle_maintenances.id')
      ->leftJoin('contacts','contacts.id','vehicle_maintenances.vendor_id')
      ->leftJoin('vehicles','vehicles.id','vehicle_maintenances.vehicle_id')
      ->leftJoin('vehicles as trailer','trailer.id','vehicles.trailer_id')
      ->leftJoin('companies','companies.id','vehicle_maintenances.company_id')
      ->whereRaw($wr)
      ->groupBy('vehicle_maintenances.id');
      // if($request->status){
      //   $item->where('status',$request->status);
      // }
      return DataTables::of($item)
      ->addColumn('action', function($item){
        // $html="<a ng-show=\"roleList.includes('vehicle.vehicle.detail.maintenance.submission.detail')\" ui-sref=\"vehicle.vehicle.show.maintenance.show({id:$item->vehicle_id,vm_id:$item->id})\"><span class='fa fa-truck'></span></a>&nbsp;&nbsp;";
        $html="<a ui-sref=\"vehicle.vehicle.show.maintenance.show({id:$item->vehicle_id,vm_id:$item->id})\"><span class='fa fa-truck'></span></a>&nbsp;&nbsp;";
        // if ($item->status<4) {
        //     // $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.detail.maintenance.submission.delete')\" ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
        //     $html.="<a ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
        // }
        return $html;
      })
      ->rawColumns(['action'])
      ->make(true);
    }

    public function report_maintenance_datatable(Request $request){
      $item = VehicleMaintenance::select(
        'vehicle_maintenances.*', 
        DB::raw('FORMAT(SUM(vehicle_maintenance_items.price), 2) as biaya_rencana'), 
        DB::raw('FORMAT(SUM(vehicle_maintenance_items.price_realisasi), 2) as biaya_realisasi'),
        'contacts.name as vendor',
        'companies.name as company_name',
        DB::raw('if(vehicles.nopol = "", vehicles.chassis_no, vehicles.nopol) as nopol'),
        'trailer.code as trailer_code',
        'items.name as item_name',
        'bengkel.name as bengkel_name'       
        )
      ->leftJoin('vehicle_maintenance_details','vehicle_maintenance_details.header_id','vehicle_maintenances.id')
      ->leftJoin('vehicle_maintenance_items','vehicle_maintenance_items.vehicle_maintenance_id','vehicle_maintenances.id')
      ->leftJoin('items','items.id','vehicle_maintenance_items.item_id')
      ->leftJoin('contacts','contacts.id','vehicle_maintenances.vendor_id')
      ->leftJoin('contacts as bengkel','bengkel.id','vehicle_maintenances.bengkel_id')
      ->join('vehicles','vehicles.id','vehicle_maintenances.vehicle_id')
      ->leftJoin('vehicles as trailer','trailer.id','vehicles.trailer_id')
      ->join('companies','companies.id','vehicle_maintenances.company_id')
      ->groupBy('vehicle_maintenances.id');
      if($request->status){
        if($request->status == 2){
          $item->whereIn('status',[2, 3, 4, 5, 6, 8]);
        } else if($request->status == 9) {
          $item->whereIn('status',[7, 9, 10, 11]);
        } else {
          $item->where('status', $request->status);
        }
      }
      if($request->company_id){
        $item->where('vehicle_maintenances.company_id',$request->company_id);
      }
      if($request->vehicle_id){
        $item->where('vehicle_maintenances.vehicle_id',$request->vehicle_id);
      }
      if($request->start_request_date){
        $start = date("Y-m-d", strtotime($request->start_request_date));
        $item->where('date_rencana', '>=',$start);
      }
      if($request->end_request_date){
        $end = date("Y-m-d", strtotime($request->end_request_date));
        $item->where('date_rencana', '<=', $end);
      }
      return DataTables::of($item)
      ->addColumn('action', function($item){
        $html="<a ui-sref=\"vehicle.vehicle.show.maintenance.show({id:$item->vehicle_id,vm_id:$item->id})\"><span class='fa fa-truck'></span></a>&nbsp;&nbsp;";
        return $html;
      })
      ->rawColumns(['action'])
      ->make(true);
    }

    public function vehicle_trailer_datatable(Request $request){
      // dd('hai');
      // dd($request->all());
      $item = DB::table('vehicle_trailer_connections as vtc')
              ->select(
                'vtc.*','companies.name as company_name','vehicles.nopol as vehicle_nopol','trailer.code as trailer_code'
              )
              ->leftJoin('companies','companies.id','vtc.company_id')
              ->leftJoin('vehicles','vehicles.id','vtc.vehicle_id')
              ->leftJoin('vehicles as trailer','trailer.id','vtc.trailer_id');
      if($request->filled('vehicle_id')){
        $item->where('vtc.vehicle_id',$request->vehicle_id);
      }
      if($request->filled('trailer_id')){
        $item->where('vtc.trailer_id',$request->trailer_id);
      }
      return DataTables::of($item)
      // ->addColumn('action', function($item){
      //     $html="<a ng-show=\"roleList.includes('vehicle.vehicle_trailer_connection.show')\" ui-sref=\"vehicle.vehicle_trailer_connection.show({id:$item->id})\"><span class='fa fa-folder-o'></span></a>&nbsp;&nbsp;";
      //     if ($item->status==1) {
      //         $html.= "<a ng-show=\"roleList.includes('vehicle.vehicle_trailer_connection.edit')\" ui-sref=\"vehicle.vehicle_trailer_connection.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
      //         $html.="<a ng-show=\"roleList.includes('vehicle.vehicle_trailer_connection.delete')\" ng-click='deletes($item->id)'><span class='fa fa-trash'  data-toggle='tooltip' title='Hapus Data'></span></a>";
      //     }
      //     return $html;
      // })
      ->editColumn('requested_date', function($item){
        return fullDate($item->requested_date);
      })
      ->editColumn('approved_date', function($item){
        return fullDate($item->approved_date);
      })
      ->editColumn('status', function($item){
        $status = [
          '1' => 'Diajukan','2' => 'Disetujui'
        ];
        return $status[$item->status];
      })
      ->editColumn('type', function($item){
        $type = [
          '1' => 'Lepas','2' => 'Pasang'
        ];
        return $type[$item->type];
      })
      ->rawColumns(['action'])
      ->make(true);
    }

    public function get_vehicle_tires(Request $request, $vehicleId)
    {
      $vehicleTire = VehicleTire::join('vehicles', 'vehicles.id', 'vehicle_tires.vehicle_id')
                  ->leftJoin('vehicle_joint_details', 'vehicle_joint_details.id', 'vehicle_tires.vehicle_joint_detail_id')
                  ->leftJoin('warehouse_stock_details', 'warehouse_stock_details.id', 'vehicle_tires.wh_stock_ban_luar_id')
                  ->leftJoin('items', 'items.id', 'warehouse_stock_details.item_id');

      // Cek apakah telah terkoneksi trailer
      $cek_trailer = Vehicle::where('id', $vehicleId)->first();
      if (@$cek_trailer->trailer_id != null && $cek_trailer->is_trailer != 1) {
        $vehicleTire = $vehicleTire->whereRaw('(vehicle_id = '.$vehicleId.' or vehicle_id = '.@$cek_trailer->trailer_id.')');
      } else {
        $vehicleTire = $vehicleTire->where('vehicle_id', $vehicleId);
      }
      // $has_selected = preg_replace ( '/[^,"A-Za-z0-9\-]/', "", $request->get('has_selected'));
      // if ($request->get('has_selected') != '[]' && $vehicleId != null) {
      //   $vehicleTire = $vehicleTire->whereRaw('vehicle_tires.description not in ('.$has_selected.')');
      // } else {
      //   $vehicleTire = $vehicleTire;
      // }

      if($request->has_tire == 1){
        $vehicleTire = $vehicleTire->whereNotNull('wh_stock_ban_luar_id');
      } else if($request->has_tire == 0){
        $vehicleTire = $vehicleTire->whereNull('wh_stock_ban_luar_id');
      }

      $vehicleTire = $vehicleTire->select('vehicle_tires.*', 'vehicle_joint_details.name as position', 
      'warehouse_stock_details.tire_batch_no as no_emboss', 
      'warehouse_stock_details.tire_batch_no', 
      'warehouse_stock_details.serial_no as serial_no',
      'items.code as item_code', 'items.name as item_name');
      $vehicleTire = $vehicleTire->get();

      return response()->json($vehicleTire);
    }

    public function vehicle_mutation_in_datatable(Request $request)
    {
      $wr = "1=1";
      if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0){
          $wr.=" AND vehicle_mutation_ins.receiver_company_id = ".auth()->user()->company_id;
      }
      if($request->receiver_company_id) {
        $wr.=" AND vehicle_mutation_ins.receiver_company_id = $request->receiver_company_id";
      }
  
      if($request->sender_company_id) {
        $wr.=" AND vehicle_mutation_ins.sender_company_id = $request->sender_company_id";
      }
     
      if($request->date_start) {
        $wr .= ' AND vehicle_mutation_ins.date_receive >= "' . dateDB($request->date_start) . '"';
      }
      if($request->date_start) {
        $wr .= ' AND vehicle_mutation_ins.date_receive <= "' . dateDB($request->date_start) . '"';
      }
      // dd($request->date_start);
      $item = VehicleMutationIn::with(
        'sender_company:id,name',
        'receiver_company:id,name',
      )->whereRaw($wr)->select('vehicle_mutation_ins.*')->orderBy('vehicle_mutation_ins.id');
      return DataTables::of($item)
      ->addColumn('action', function ($item) {
        $html = "<a ng-click=\"show($item->id)\"><span class='fa fa-folder-o' data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        if($item->status == 1){
          $html .= "<a ng-click=\"edit($item->id)\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
          $html .= "<a ng-click=\"delete($item->id)\"><span class='fa fa-trash'></span></a>";
        }
        return $html;
      })
      ->editColumn('status', function ($item) {
        return VehicleMutationIn::getStatuses()[$item->status] ?? '-';
      })
      ->rawColumns(['action'])
      ->make(true);
    }
    public function vehicle_mutation_request_datatable(Request $request)
    {
      $wr = "1=1";
      if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0){
        {
          // $wr.=" AND vehicle_mutation_requests.receiver_company_id = ".auth()->user()->company_id;
          $wr.=" AND vehicle_mutation_requests.sender_company_id = ".auth()->user()->company_id;
      }
      }
      if($request->date_request) {
        $req=Carbon::parse($request->date_request)->format('Y-m-d');
        $wr.=" AND vehicle_mutation_requests.date_request = '$req'";
      }
  
      if($request->date_needed) {
        $need=Carbon::parse($request->date_needed)->format('Y-m-d');
        $wr.=" AND vehicle_mutation_requests.date_needed = '$need'";
      }
  
      if($request->receiver_company_id) {
        $wr.=" AND vehicle_mutation_requests.receiver_company_id = $request->receiver_company_id";
      }
  
      if($request->sender_company_id) {
        $wr.=" AND vehicle_mutation_requests.sender_company_id = $request->sender_company_id";
      }
  
      if($request->status) {
        $wr.=" AND vehicle_mutation_requests.status = $request->status";
      }

     
      $item = VehicleMutationRequest::with(
        'sender_company:id,name',
        'receiver_company:id,name',
        'requested_by:id,name',
        'approve_ho_by:id,name',
        'approve_hq_by:id,name'
      )
      ->whereRaw($wr)
      ->select('vehicle_mutation_requests.*')
      ->orderBy('vehicle_mutation_requests.id');
      return DataTables::of($item)
      ->addColumn('action', function ($item) {
        $html = "<a ng-click=\"show($item->id)\"><span class='fa fa-folder-o' data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        if($item->status == 1){
          $html .= "<a ng-click=\"edit($item->id)\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
          $html .= "<a ng-click=\"delete($item->id)\"><span class='fa fa-trash'></span></a>";
        }
        return $html;
      })
      ->editColumn('status', function ($item) {
        return VehicleMutationRequest::getStatuses()[$item->status] ?? '-';
      })
      ->rawColumns(['action'])
      ->make(true);
    }
  
    public function vehicle_mutation_out_datatable_modal()
    {
      // $wr = "1=1";
      // if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0)
      //   {
      //     $wr.=" AND vehicle_mutation_outs.sender_company_id = ".auth()->user()->company_id;
      // }
  
      // $wr.=" AND vehicle_mutation_outs.status = 3";

      
      // $r = DB::table("vehicle_mutation_outs")->select('*')->whereNotIn('id',function($query) {

      //   $query->select('vehicle_mutation_out_id')->from('vehicle_mutation_ins');
     
      // })->leftJoin('companies as sc', 'vehicle_mutation_outs.sender_company_id', 'sc.id')
      // ->leftJoin('companies as rc', 'vehicle_mutation_outs.receiver_company_id', 'rc.id')
      // ->leftJoin('users as ahb', 'vehicle_mutation_outs.approve_ho_by', 'ahb.id')
      // ->whereRaw($wr)
      // ->select('sc.*', 'rc.*', 'ahb.*')
      // ->orderBy('vehicle_mutation_outs.id');
      // return DataTables::of($r)->make(true);
      $r = DB::table("vehicle_mutation_outs AS vmo")->whereNotIn('vmo.id',function($query) {

        $query->select('vehicle_mutation_out_id')->from('vehicle_mutation_ins');
     
      })->leftJoin('companies AS sc', 'vmo.sender_company_id','=', 'sc.id')
      ->leftJoin('companies AS rc', 'vmo.receiver_company_id','=', 'rc.id')
      ->leftJoin('users AS ahb', 'vmo.approve_ho_by','=', 'ahb.id')
      // ->whereRaw($wr)
      ->select('vmo.*', 'sc.name as sender_company', 'rc.name as receiver_company', 'ahb.name as approve_ho_by_name')
      ->orderBy('vmo.id')->get();
      return DataTables::of($r)
      ->make(true);
  
      // $item = VehicleMutationOut::with(
      //   'sender_company:id,name',
      //   'receiver_company:id,name',
      //   'approve_ho_by:id,name',
      //   // 'approve_hq_by:id,name'
      // )
      // ->whereRaw($wr)
      // ->select('vehicle_mutation_outs.*')
      // ->orderBy('vehicle_mutation_outs.id');
      // return DataTables::of($item)
      // ->make(true);
    }
  
    public function vehicle_mutation_request_datatable_modal()
    {
      $query = DB::table('vehicle_mutation_outs as vmo')
      ->leftJoin('vehicle_mutation_requests as vmr', 'vmo.vehicle_mutation_request_id', 'vmr.id')
      ->select('vmr.id', 'vmo.vehicle_mutation_request_id')
      ->get();
  
      // dd($query);
      $wr = "1=1";
      if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0)
        {
          $wr.=" AND vehicle_mutation_requests.sender_company_id = ".auth()->user()->company_id;
      }
  
      $wr.=" AND vehicle_mutation_requests.status = 3";
      $wr.=" AND vehicle_mutation_requests.mutation_out_code = '0'";
  
  
      $item = VehicleMutationRequest::with(
        'sender_company:id,name',
        'receiver_company:id,name',
        'requested_by:id,name',
        'approve_ho_by:id,name',
        'approve_hq_by:id,name'
      )
      ->whereRaw($wr)
      ->select('vehicle_mutation_requests.*')
      ->orderBy('vehicle_mutation_requests.id');
      return DataTables::of($item)
      ->make(true);
    }
  
    public function vehicle_mutation_out_datatable(Request $request)
    {
  
  
      $wr = "1=1";
      if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0){
        {
          $wr.=" AND vehicle_mutation_outs.receiver_company_id = ".auth()->user()->company_id;
          $wr.=" AND vehicle_mutation_outs.sender_company_id = ".auth()->user()->company_id;
        }
      }
  
  
      if($request->receiver_company_id) {
        $wr.=" AND vehicle_mutation_outs.receiver_company_id = $request->receiver_company_id";
      }
  
      if($request->sender_company_id) {
        $wr.=" AND vehicle_mutation_outs.sender_company_id = $request->sender_company_id";
      }
  
      if($request->date_send) {
        $req=Carbon::parse($request->date_send)->format('Y-m-d');
        $wr.=" AND vehicle_mutation_outs.date_send = '$req'";
      }
  
      if($request->status) {
        $wr.=" AND vehicle_mutation_outs.status = $request->status";
      }
  
      // dd($wr);
  
      $item = VehicleMutationOut::with(
        'sender_company:id,name',
        'receiver_company:id,name',
        'approve_ho_by:id,name',
      )->select('vehicle_mutation_outs.*')->whereRaw($wr)->orderBy('vehicle_mutation_outs.id');
      return DataTables::of($item)
      ->addColumn('action', function ($item) {
        $html = "<a ng-click=\"show($item->id)\"><span class='fa fa-folder-o' data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        if($item->status == 1){
          $html .= "<a ng-click=\"edit($item->id)\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
          $html .= "<a ng-click=\"delete($item->id)\"><span class='fa fa-trash'></span></a>";
        }
        return $html;
      })
      ->editColumn('status', function ($item) {
        return VehicleMutationOut::getStatuses()[$item->status] ?? '-';
      })
      ->rawColumns(['action'])
      ->make(true);
    }

    public function vehicle_document_control_datatable(Request $request)
    {
      $item = VehicleDocument::with('vehicle:id,nopol,company_id', 'vehicle.company:id,name')
        ->select(
          'vehicle_documents.*',
          DB::raw(
            '(DATEDIFF(expired_date_dokumen,NOW())) as diff_days'
          )
        );
      if ($request->filled('company_id')) {
        $item->whereHas('vehicle.company', function (Builder $query) use ($request) {
          $query->where('id', $request->company_id);
        });
      }
      if ($request->filled('vehicle_id')) {
        $item->whereHas('vehicle', function (Builder $query) use ($request) {
          $query->where('id', $request->vehicle_id);
        });
      }
      if ($request->is_alert == 0) {
        $item = $item->whereRaw("(DATEDIFF(expired_date_dokumen,NOW())) < IFNULL(alert_interval,0)");
      }
      if ($request->is_alert == 1) {
        $item = $item->whereRaw("(DATEDIFF(expired_date_dokumen,NOW())) > IFNULL(alert_interval,0)");
      }
      if ($request->filled('type')) {
        $item->where('type', $request->type);
      }
      if ($request->start_date) {
        $item->whereDate('expired_date_dokumen', '>=', dateDB($request->start_date));
      }
      if ($request->end_date) {
        $item->whereDate('expired_date_dokumen', '<=', dateDB($request->end_date));
      }
      if ($request->filled('status')) {
        if ($request->status == 1) {
          $item->whereDate('expired_date_dokumen', '>=', dateDB(now()));
        } else if ($request->status == 2) {
          $item->whereDate('expired_date_dokumen', '<=', dateDB(now()));
        }
      }
      if ($request->filled('alert_status')) {
        if ($request->alert_status == 1) {
          $item->where(DB::raw('(select DATEDIFF(expired_date_dokumen,NOW()))'), '<', 'alert_interval');
        } else if ($request->alert_status == 2) {
          $item->where(DB::raw('(select DATEDIFF(expired_date_dokumen,NOW()))'), '>', 'alert_interval');
        }
      }
      return DataTables::of($item)
        ->addColumn('date_now', function ($item) {
          return date('Y-m-d');
        })
        ->addColumn('status', function ($item) {
          return $item->diff_days > 0 ? 'Aktif' : 'Non Aktif';
        })
        ->addColumn('alert_status', function ($item) {
          return $item->diff_days <= ($item->alert_interval ?? 0) ? 'Alerted' : 'Not Alerted';
        })
        ->make(true);
    }

    public function vehicle_document_control_stnk_datatable(Request $request)
    {
      $item = VehicleDocument::with('vehicle:id,nopol,company_id', 'vehicle.company:id,name')
        ->select(
          'vehicle_documents.*',
          DB::raw(
            '(DATEDIFF(expired_date_dokumen,NOW())) as diff_days'
          )
        );
      if ($request->filled('company_id')) {
        $item->whereHas('vehicle.company', function (Builder $query) use ($request) {
          $query->where('id', $request->company_id);
        });
      }
      if ($request->filled('vehicle_id')) {
        $item->whereHas('vehicle', function (Builder $query) use ($request) {
          $query->where('id', $request->vehicle_id);
        });
      }
      // if ($request->is_alert == 0) {
      //   $item = $item->whereRaw("(DATEDIFF(expired_date_dokumen,NOW())) < IFNULL(alert_interval,0)");
      // }
      // if ($request->is_alert == 1) {
      //   $item = $item->whereRaw("(DATEDIFF(expired_date_dokumen,NOW())) > IFNULL(alert_interval,0)");
      // }
      // if ($request->filled('type')) {
        if($request->type != 0){
          $item->where('type', $request->type);
        } else {
          $item->whereNotIn('type', [1,3]);
        }
      // }
      if ($request->start_date) {
        $item->whereDate('expired_date_dokumen', '>=', dateDB($request->start_date));
      }
      if ($request->end_date) {
        $item->whereDate('expired_date_dokumen', '<=', dateDB($request->end_date));
      }
      if ($request->filled('status')) {
        if ($request->status == 1) {
          $item->whereDate('expired_date_dokumen', '>=', dateDB(now()));
        } else if ($request->status == 2) {
          $item->whereDate('expired_date_dokumen', '<=', dateDB(now()));
        }
      }
      if ($request->filled('alert_status')) {
        if ($request->alert_status == 1) {
          $item->where(DB::raw('(select DATEDIFF(expired_date_dokumen,NOW()))'), '<', 'alert_interval');
        } else if ($request->alert_status == 2) {
          $item->where(DB::raw('(select DATEDIFF(expired_date_dokumen,NOW()))'), '>', 'alert_interval');
        }
      }
      return DataTables::of($item)
        ->addColumn('date_now', function ($item) {
          return date('Y-m-d');
        })
        ->addColumn('status', function ($item) {
          return $item->diff_days > 0 ? 'Aktif' : 'Non Aktif';
        })
        ->addColumn('alert_status', function ($item) {
          return $item->diff_days <= ($item->alert_interval ?? 0) ? 'Alerted' : 'Not Alerted';
        })
        ->make(true);
    }

    public function document($vid)
  {
    $data['detail'] = VehicleDocument::where('vehicle_id', $vid)->get();
    return response()->json($data, 200);
  }

  public function getCurrentDocument($id)
  {
    $data = VehicleDocument::where('id', $id)->first();
    return response()->json($data, 200);
  }

  public function store_document(Request $request, $vid)
  {
    // dd($request->type);
    $request->validate([
      'file' => 'required',
      'type' => 'required',
      'nomor_dokumen' => 'required',
      'expired_date_dokumen' => 'required',
    ]);
    $stt = [
      1 => 'STNK',
      2 => 'SIUP',
      3 => 'KEUR',
      4 => 'KIM-IMK',
      5 => 'PERBAIKAN',
      6 => 'BPKB',
      7 => 'FOTO',
      8 => 'LAIN',
      9 => 'B3 NO',
      10 => 'B3',
      11 => 'B3 Limbah',
      12 => 'B3 BBM',
      13 => 'HANDAK',
    ];
    $file = $request->file('file');
    $filename = "VEHICLE_" . $vid . "_" . date('Ymd_His') . '.' . $file->getClientOriginalExtension();
    $file->move(public_path('files'), $filename);

    DB::beginTransaction();
    VehicleDocument::create([
      'vehicle_id' => $vid,
      'type' => str_replace("number:", "", $request->type),
      'file_name' => 'files/' . $filename,
      'date_upload' => Carbon::now(),
      'extension' => $file->getClientOriginalExtension(),
      'alert_interval' => $request->alert_interval,
      'description' => $request->description,
      'nomor_dokumen' => $request->nomor_dokumen ?? null,
      'expired_date_dokumen' => ($request->expired_date_dokumen) ? date('Y-m-d', strtotime($request->expired_date_dokumen)) : null,
    ]);

    /**
   * Date : 04-04-2022
   * Description : Update No STNK dan Tanggal STNK di berkas dan detail vehicle, Update tanggal KIR di berkas dan detail vehicle  
   * Developer : Ardiyan
   * Status : Improve
   */
    if (str_replace("number:", "", $request->type) == 1) {

      DB::table('vehicles')->where('id', $vid)->update([
        'stnk_no' => $request->nomor_dokumen,
        'stnk_date' => ($request->expired_date_dokumen) ? date('Y-m-d', strtotime($request->expired_date_dokumen)) : null,
      ]);
    }
    if (str_replace("number:", "", $request->type) == 3) {

      DB::table('vehicles')->where('id', $vid)->update([
        'kir_date' => ($request->expired_date_dokumen) ? date('Y-m-d', strtotime($request->expired_date_dokumen)) : null,
      ]);
    }
    DB::commit();
    return response()->json(null);
  }
  public function register_maintenance_datatable (Request $request)
  {
    $wr="1=1";
    if (isset($request->vehicle_id)) {
        $wr.=" AND vehicle_maintenances.vehicle_id = ".$request->vehicle_id;
    }

    $item = DB::table('vehicle_maintenances')
    ->whereRaw($wr)
    ->selectRaw('vehicle_maintenances.*')
    ->groupBy('vehicle_maintenances.id');

    return DataTables::of($item)
    ->make(true);
  }

  public function vehicle_position_dashboard() {
    $dashboard = new VehicleController();
    $is_api = true;
    return $dashboard->vehicle_position_dashboard($is_api);
  }
}
