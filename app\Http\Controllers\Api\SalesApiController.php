<?php

namespace App\Http\Controllers\Api;

use App\Abstracts\Finance\Receivable;
use App\Abstracts\Sales\CustomerOrder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Abstracts\Sales\SalesOrder;
use App\Abstracts\Sales\SalesOrderDetail;
use App\Model\CustomerOrderStatus;
use App\Model\CustomerOrderDetail;
use App\Model\PurchaseRequestDetail;
use Illuminate\Support\Facades\DB;
use Response;
use Yajra\DataTables\Facades\DataTables;

class SalesApiController extends Controller
{
    /*
      Date : 16-03-2020
      Description : Menampilkan sales order
      Developer : Didin
      Status : Edit
    */
    public function sales_order_datatable(Request $request)
    {
      // dd($request->all());
        $dt = SalesOrder::query($request->all());

        if($request->filled('customer_id')) {
            $dt->where('job_orders.customer_id', $request->customer_id);
        }

        if($request->filled('start_date') && $request->filled('end_date')){
            if(dateDB($request->start_date) > dateDB($request->end_date)){
                $temp = $request->start_date;
                $request->start_date = $request->end_date;
                $request->end_date = $temp;
            }
        }

        if($request->filled('start_date')){
            $dt->whereRaw('DATE(job_orders.shipment_date) >= "' . dateDB($request->start_date) .'"');
        }

        if($request->filled('end_date')){
            $dt->whereRaw('DATE(job_orders.shipment_date) <= "' . dateDB($request->end_date) .'"');
        }

        if($request->filled('is_invoiced')){
            $dt->where('job_orders.is_invoiced', $request->is_invoiced);
        }
        if($request->filled('for_invoicing')){
            $dt->whereIn('sales_order_statuses.slug', ['waiting_for_payment', 'approved']);
            $dt->where('job_orders.is_invoiced', 0);
        }

        $dt = $dt->selectRaw('sales_orders.id,
                    sales_orders.code,
                    customer_orders.payment_type,
                    contacts.name AS customer_name,
                    job_orders.shipment_date,
                    sales_order_statuses.name as status,
                    kpi_statuses.name AS kpi_status_name,
                    (SELECT sum(coalesce(jod.total_price,0)+ coalesce(joc.total_price,0)) as total_price FROM job_orders as jo
                    join job_order_details as jod on jo.id = jod.header_id
                    left join job_order_costs as joc on jo.id = joc.header_id
                    where jo.id = sales_orders.job_order_id
                    GROUP BY jo.id) as total_price,
                    sales_orders.is_deposit,
                    sales_orders.jumlah_deposit');

        return DataTables::of($dt)
            ->addColumn('kpi_status_name_so', function ($row) {
                // example because using service statuses
                if ($row->kpi_status_name == 'Job Order') {
                    return 'Sales Order';
                } else {
                    return $row->kpi_status_name;
                }
            })
        ->addColumn('payment_type_name',function($row){
            switch ($row->payment_type) {
                case 1:
                    return 'Due On Receipt';
                    break;
                case 2:
                    return 'Jatuh Tempo';
                    break;
                default:
                    return '-';
            }
        })
        ->addColumn('sisa_total_price', function($row){
            return $row->total_price - $row->jumlah_deposit;
        })
        ->make(true);
    }

    /*
      Date : 12-07-2021
      Description : Menampilkan list detail sales order
      Developer : Hendra
      Status : Create
    */
    public function sales_order_detail_datatable(Request $request)
    {
        $dt = SalesOrderDetail::query($request->all());

        if($request->filled('sales_order_id')){
            $dt = $dt->where('sales_orders.id', $request->sales_order_id);
        }

        return DataTables::of($dt)
                        ->make(true);
    }

    /*
      Date : 06-07-2021
      Description : Menampilkan list customer order
      Developer : Hendra
      Status : Create
    */
    public function customer_order_datatable(Request $request)
    {
        
        $dt = CustomerOrder::query($request->all());

        if($request->filled('customer_id')) {
            $dt->where('customer_orders.customer_id', $request->customer_id);
        }
        if($request->filled('status')) {
          $dt->where('customer_orders.customer_order_status_id', $request->status);
        }
        if($request->filled('start_date') && $request->filled('end_date')){
            if(dateDB($request->start_date) > dateDB($request->end_date)){
                $temp = $request->start_date;
                $request->start_date = $request->end_date;
                $request->end_date = $temp;
            }
        }

        if($request->filled('start_date')){
            $dt->whereRaw('DATE(customer_orders.date) >= "' . dateDB($request->start_date) .'"');
        }

        if($request->filled('end_date')){
            $dt->whereRaw('DATE(customer_orders.date) <= "' . dateDB($request->end_date) .'"');
        }
        if($request->filled('is_waiting') && $request->is_waiting == 1){
            $status= CustomerOrderStatus::where('slug','waiting')->first();
            $dt->where('customer_order_status_id',$status->id);

        }
        if($request->filled('is_reject') && $request->is_reject == 0 ){
            $status_reject= CustomerOrderStatus::where('slug','rejected')->first();
            $dt->where('customer_order_status_id','!=',$status_reject->id);

        }
        if($request->filled('is_approve') && $request->is_approve == 0 ){
            $status_approve= CustomerOrderStatus::where('slug','approved')->first();
            $dt->where('customer_order_status_id','!=',$status_approve->id);

        }
        if($request->filled('is_draft') && $request->is_draft == 1 ){
            $status_draft= CustomerOrderStatus::where('slug','draft')->first();
            $dt->where('customer_order_status_id','!=',$status_draft->id);
        }

        if($request->filled('is_item_master') && $request->is_item_master == 1 ){
            $co_detail=CustomerOrderDetail::where('warehouse_receipt_detail_id',null)->where('rack_id',null)->get();
            $idHeader= $co_detail->pluck('header_id')->toArray();
            $dt->whereIn('customer_orders.id',$idHeader);
        }

        if($request->filled('is_purchase_relation') && $request->is_purchase_relation == 1 ){
          $dt->where('all_qty','>',0);

        }

        $dt = $dt->select('customer_orders.id', 'customer_orders.code', 'contacts.name AS customer_name', DB::raw('DATE(customer_orders.date) as date'), 'quotations.no_contract', 'customer_order_statuses.name as status', 'customer_order_statuses.slug as status_slug', 'customer_orders.created_at', 'customer_orders.customer_id');

        return DataTables::of($dt)
        ->editColumn('date', function($row){
            return fullDate($row->date);
        })
        ->make(true);
    }
}
