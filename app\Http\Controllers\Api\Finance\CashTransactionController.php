<?php

namespace App\Http\Controllers\Api\Finance;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use app\Abstracts\Journal as J;
use App\Model\Journal;
use App\Model\JournalDetail;
use App\Model\Company;
use App\Model\TypeTransaction;
use App\Model\Account;
use App\Model\AccountDefault;
use App\Model\CashCategory;
use App\Model\JournalFavorite;
use App\Model\Contact;
use App\Model\CashAdvance;
use App\Model\CashTransaction;
use App\Model\CashTransactionDetail;
use App\Model\SubmissionCost;
use App\Model\PurchaseOrderDetail;
use App\Model\PurchaseOrder;
use App\Model\PurchaseRequest;
use App\Model\PurchaseRequestDetail;
use App\Model\CustomerOrder;
use App\Utils\TransactionCode;
use App\Abstracts\Finance\Closing;
use App\Abstracts\Finance\CashTransaction AS CT;
use App\Abstracts\WarehouseReceipt AS WR;
use App\Abstracts\Sales\CustomerOrder AS CO;
use App\Exports\Finance\CardCashBank;
use App\Model\CustomerOrderDetail;
use App\Model\SalesOrder;
use App\Model\JobOrderCost;
use App\Model\ManifestCost;
use Response;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use App\Model\CostType;
use App\Model\Manifest;
use App\Model\CashAdvanceStatus;
use App\Model\CashAdvanceTransaction;
use Barryvdh\DomPDF\Facade AS PDF;
use DateTime;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;

class CashTransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
      $data['accountDefaultPiutang']=AccountDefault::first()->getPiutang;
      $data['account']=Account::with('parent')->where('is_base',0)->orderBy('code')->get();
      // $data['cash_bank']=Account::with('parent')->where('is_base',0)->whereIn('no_cash_bank',[1,2])->orderBy('code')->get();
      $data['type_transaction']=TypeTransaction::all();
      $data['cash_category']=CashCategory::with('category')->where('is_base', 0)->get();
      $data['company_id']=auth()->user()->company_id;
      $data['employee'] = DB::table('contacts')
      ->where('is_vendor', 1)
      ->orWhere('is_pegawai', 1)
      ->orWhere('is_driver',1)
      ->select('contacts.id','contacts.name', 
      DB::raw('
          CASE
              WHEN is_vendor = 1 THEN "Vendor"
              WHEN is_pegawai = 1 THEN "Employee"
              WHEN is_driver = 1 THEN "Driver"
              ELSE "unknown" -- Default value if none of the conditions are met
          END AS group_by
      '))
      ->get();

      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    /*
      Date : 21-03-2020
      Description : Membatalkan persetujuan transaksi kas
      Developer : Didin
      Status : Create
    */
    public function reject($id) {
        if(DB::table('cash_transactions')->whereId($id)->count('id') < 1) {
            return Response::json(['message' => 'Transaksi kas tidak ditemukan'], 404);
        }
        DB::beginTransaction();
        try {
            CT::validateWasRequested($id);
        } catch (Exception $e) {
            throw new Exception('Data sudah dibatalkan');
        }
        $ct =DB::table('cash_transactions')
        ->whereId($id)
        ->first();
        Closing::preventByDate($ct->date_transaction);
        $journal_id = $ct->journal_id;

        $ct =DB::table('cash_transactions')
        ->whereId($id);

        $ct->update([
            'status_cost' => 1,
            'status' => 1,
            'is_cut' => 0,
            'journal_id' => null
        ]);
        DB::commit();

        DB::table('journal_details')
        ->whereHeaderId($journal_id)
        ->delete();
        DB::table('journals')
        ->whereId($journal_id)
        ->delete();


        return Response::json(['message' => 'Transaksi kas berhasil dibatalkan']);
    }

    /*
      Date : 17-03-2020
      Description : Meng-update biaya manifest / biaya job order pada detail transaksi kas
      Developer : Didin
      Status : Edit
    */
    public function update_manifest(Request $request, $cash_transaction_detail_id)
    {
        DB::table('cash_transaction_details')
        ->whereId($cash_transaction_detail_id)
        ->update([
            'job_order_cost_id' => $request->job_order_cost_id ?? null,
            'manifest_cost_id' => $request->manifest_cost_id ?? null,
            'description' => $request->description ?? null,
        ]);

        Response::json(['message' => 'Data berhasil di-update']);
    }
    /*
        Date : 06-10-2022 By : Muhammad Firyanul Rizky
        Description : [Improve] Kas Transaksi Bank, menu modal untuk setting/update tanggal transaksi ke journal
        Keterangan : Membuat function untuk update field date transaksi ke tabel cash_transactions
    */
    public function update_date_transaction($cash_transaction_id, $date_transaction)
    {
        //cek exist tobe
        $cash=DB::table('cash_transactions')->whereId($cash_transaction_id)->get();
        DB::beginTransaction();
        try{
            if(!empty($cash)){
                $cb=DB::table('cash_transactions')->whereId($cash_transaction_id);
                $c=$cb->update([
                    'cash_transactions.date_transaction'=>dateDB($date_transaction)??NULL
                ]);
            }
            DB::commit();
            Response::json(['message' => 'Tanggal berhasil di-update']);
        } catch (Exception $e){
            dd($e->getMessage());
            DB::rollBack();
            Response::json(['message' => 'Tanggal gagal di-update']);
        } catch (ValidationException $e){
            dd($e->getMessage());
            DB::rollBack();
            Response::json(['message' => 'Tanggal gagal di-update']);
        }
    }
    // Close Description : [Improve] Kas Transaksi Bank, menu modal untuk setting/update tanggal transaksi ke journal

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    /*
      Date : 04-03-2020
      Description : Menambah transaksi kas
      Developer : Didin
      Status : Edit
    */
    public function store(Request $request)
    {
        $request->validate([
            'company_id' => 'required',
            'date_transaction' => 'required',
            'type' => 'required',
            'jenis' => 'required',
            // 'reff' => 'required',
            // 'cash_bank' => 'required',
        ]);
        // dd($request->all());
        if ($request->type==1 && $request->jenis==1) {
            $tyname="CashIn";
            $in=1;
        } elseif ($request->type==1 && $request->jenis==2) {
            $tyname="CashOut";
            $in=0;
        } elseif ($request->type==2 && $request->jenis==1) {
            $tyname="BankIn";
            $in=1;
        } else {
            $tyname="BankOut";
            $in=0;
        }

        //keterangan cash transaksi jika dari kasbon
        $new_title = "";
        if ($request->kasbon_id != null && $request->kasbon_id != 0) {
            $getkasbon = DB::table("cash_advances")->where("id", $request->kasbon_id)->first();
            if ($tyname == "CashIn") {
                $new_title = "Realisasi Kas Masuk atas Cash Advance : ".$getkasbon->code." - ".$request->description;
            }
            if ($tyname == "BankIn") {
                $new_title = "Realisasi Bank Masuk atas Cash Advance : ".$getkasbon->code." - ".$request->description;
            }
            if ($tyname == "CashOut") {
                $new_title = "Realisasi Kas Keluar atas Cash Advance : ".$getkasbon->code." - ".$request->description;
            }
            if ($tyname == "BankOut") {
                $new_title = "Realisasi Bank Keluar atas Cash Advance : ".$getkasbon->code." - ".$request->description;
            }

            // Untuk title dengan no realisasi
            if(isset($request->no_realization)){
                if($request->no_realization){
                    $new_title = "Pengembalian atas Cash Advance : ".$getkasbon->code." - ".$request->description;
                }
            }
            $request->reff = $getkasbon->code;
        } else {
            $new_title = $request->description;
        }

        DB::beginTransaction();
        $tp = TypeTransaction::where('slug', $tyname)->first();
        $code = new TransactionCode($request->company_id, $tyname);
        $code->setCode();
        $trx_code = $code->getCode();
        $statusCost = 1;

        if($request->kasbon_id > 0)
            $statusCost = 1;

        $i=CashTransaction::create([
            'company_id' => $request->company_id,
            'type_transaction_id' => $tp->id,
            'code' => $trx_code,
            'reff' => $request->reff,
            'jenis' => $request->jenis,
            'type' => $request->type,
            'description' => $new_title,
            'total' => 0,
            'account_id' => $request->cash_bank,
            'date_transaction' => dateDB($request->date_transaction),
            'status_cost' => $statusCost,
            'created_by' => auth()->id()
        ]);
        // dd($i);

        if ($request->detail) {
            // if ($request->jenis==1) {
            //     $j=Journal::create([
            //         'company_id' => $request->company_id,
            //         'type_transaction_id' => $tp->id,
            //         'date_transaction' => dateDB($request->date_transaction),
            //         'created_by' => auth()->id(),
            //         'code' => $trx_code,
            //         'description' => $request->description,
            //         'debet' => 0,
            //         'credit' => 0,
            //     ]);
            // }
            // CashTransaction::find($i->id)->update(['journal_id' => ($j->id??null)]);
            CashTransactionDetail::where('header_id', $i->id)->delete();

            $amount=0;
            foreach ($request->detail as $value) {
                if (empty($value)) continue;
                $name= NULL;
                if(isset($value['kasbon_id'])){
                    if($request->kasbon_id != null){
                        $name= $value['name'];
                    }
                    
                    if(is_null($value['description'])){
                        $value['description'] = "Pengembalian atas Cash Advance : ".$getkasbon->code;
                    }
                }
                

                $cost_idnya=(array_key_exists('cost_id',$value))?$value['cost_id']:null;

                $ct_detail= CashTransactionDetail::create([
                    'header_id' => $i->id,
                    'account_id' => $value['account_id'],
                    'amount' => $value['amount'],
                    'job_order_cost_id' => $value['job_order_cost_id'] ?? null,
                    'manifest_cost_id' => $value['manifest_cost_id'] ?? null,
                    'cost_id' => $cost_idnya,
                    'uploaded_file' => $value['file'],
                    'description' => @$value['description'],
                    'jenis' => $value['jenis'],
                    'kasbon_cost_title' => $name
                ]);
                // dd($ct_detail);

                if(!is_null($value['account_id'])) {
                    $cekCC=cekCashCount($request->company_id,$value['account_id']);

                    if ($cekCC)
                        return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
                }

                // if ($request->jenis==1) {
                //     JournalDetail::create([
                //     'header_id' => $j->id,
                //     'account_id' => $value['account_id'],
                //     // 'cash_category_id' => $cid,
                //     'debet' => ($request->jenis==2?$value['amount']:0),
                //     'credit' => ($request->jenis==1?$value['amount']:0),
                //     ]);
                // }

                $amount+=$value['amount'];

                //jika jenis kasbon  add cash advance transaction
                // if($value['jenis'] == 2){
                if($request->kasbon_id != null && $request->kasbon_id != 0){
                    CashAdvanceTransaction::create([
                        'cash_advance_id' => $request->kasbon_id,
                        'cash_transaction_id' => $i->id,
                        'is_in' => $in
                    ]);
                }

                //cash advance in
                $ca_in_out=CashAdvanceTransaction::where('cash_advance_transactions.cash_advance_id',$value['kasbon_id'])
                ->leftJoin('cash_transactions','cash_transactions.id','cash_advance_transactions.cash_transaction_id')
                ->select('cash_transactions.*','cash_advance_transactions.is_in')
                ->get();
                $amount_in= 0;
                $amount_out= 0;

                foreach($ca_in_out as $ci){
                    if($ci->is_in == 1){
                        $amount_in+=$ci->total;
                    }else{
                        $amount_out+=$ci->total;
                    }
                }

                // if($amount_in+$value['amount'] > $amount_out){
                //     return Response::json(['message' => 'Total yang diinputkan melebihi kasbon'],500);
                // }

                if(!is_null($request->cash_bank)) {
                    $cekCC=cekCashCount($request->company_id,$request->cash_bank);

                    if ($cekCC)
                        return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
                }

                // if ($request->jenis==1) {
                // JournalDetail::create([
                //     'header_id' => $j->id,
                //     'account_id' => $request->cash_bank,
                //     // 'cash_category_id' => $cid,
                //     'debet' => ($request->jenis==1?$amount:0),
                //     'credit' => ($request->jenis==2?$amount:0),
                // ]);
                // }

                $i->update([ 'total' => $amount ]);

                // create cost
                if(isset($value['job_order_cost_id']) || isset($value['manifest_cost_id'])){

                    $cost_idnya=(array_key_exists('cost_id',$value))?$value['cost_id']:null; //jika bukan dari jo maupun manifest
                    if($value['job_order_cost_id'] != null || $value['manifest_cost_id'] != null ){
                        /*
                            Date : 25-11-2022
                            Description : Cek apakah value membawa cost_id atau tidak
                            Developer : Sendy
                            Status : Improve
                        */
                        if (@$value['cost_id'] != null) {
                            //cost type
                            $cost_type= DB::table('cost_types')->where('id',$value['cost_id'])->first();
                            $price = $value['amount'];
                            if(isset($value['qty'])){
                                $price = $value['amount'] / $value['qty'];
                            }
                            //jo cost create
                            if($value['job_order_cost_id'] != null && $value['manifest_cost_id'] == null){
                                $add_cost= new JobOrderCost;
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['job_order_cost_id'];
                                $add_cost->cost_type_id=$cost_type->id;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=$cost_type->vendor_id;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=$value['qty']??1;
                                $add_cost->price=$price??$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->description=@$value['description'];
                                $add_cost->save();
                            }
                            //create cost manifest
                            else{
                                $add_cost= new ManifestCost();
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['manifest_cost_id'];
                                $add_cost->cost_type_id=$cost_type->id;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=$cost_type->vendor_id;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=$value['qty']??1;
                                $add_cost->price=$price??$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->qty_real=$value['qty']??1;
                                $add_cost->price_real=$price??$value['amount'];
                                $add_cost->total_real= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->is_approve=1;
                                $add_cost->description=@$value['description'];
                                $add_cost->save();

                                // dd($add_cost);
                            }
                        } else {
                            if($value['job_order_cost_id'] != null && $value['manifest_cost_id'] == null){
                                $add_cost= new JobOrderCost;
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['job_order_cost_id'];
                                $add_cost->cost_type_id=0;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=0;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->description=@$value['description'];
                                $add_cost->save();
                            }
                            //create cost manifest
                            else{
                                $add_cost= new ManifestCost();
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['manifest_cost_id'];
                                $add_cost->cost_type_id=0;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=0;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->description=@$value['description'];
                                $add_cost->save();
                            }

                        }
                        if(isset($add_cost))
                            $updateCT= CashTransactionDetail::find($ct_detail->id);
                            $updateCT->relation_cost_id= $add_cost->id;
                            $updateCT->save();
                    }


                    //update id cash advances

                    if($value['job_order_cost_id'] == null && $value['manifest_cost_id'] == null){
                        if(isset($value['kasbon_id'])){
                            if($value['kasbon_id'] != null) {
                                $ca = CashAdvance::find($value['kasbon_id']);
                                // $ca->cash_transaction_id= $i->id;
                                // $ca->save();
                            }
                        }
                    }
                }
            }
        }

        //from kasbon cash transaction
        if(isset($request->kasbon_id)){
            if($request->kasbon_id > 0){
                $kasbon= CashAdvance::find($request->kasbon_id);
                // $kasbon->cash_transaction_id=$i->id;
                // $kasbon->save();
                
                //approve cash transaction if request have kasbon_id > 0
                $cash_transaction = new \App\Http\Controllers\Finance\CashTransactionController();
                $cash_transaction->approve($i->id);
            }
        }
        

        //[Start] Jika kas keluar otomatis membuat kas masuk
        // PENAMBAHAN HANYA UNTUK KAS ADVANCE
        if($request->kasbon_id > 0){
            if ($tyname == "CashOut") {
                $tyname = "CashIn";
                $in=1;
                $getkasbon = DB::table("cash_advances")->where("id", $request->kasbon_id)->first();
                    
                $new_title_ci = "Realisasi Kas Masuk atas Cash Advance : ".$getkasbon->code." - ".$request->description;

                $tp = TypeTransaction::where('slug', $tyname)->first();
                $code = new TransactionCode($request->company_id, $tyname);
                $code->setCode();
                $trx_code = $code->getCode();
                $statusCost = 1;

                if($request->kasbon_id > 0)
                    $statusCost = 1;

                $i=CashTransaction::create([
                    'company_id' => $request->company_id,
                    'type_transaction_id' => $tp->id,
                    'code' => $trx_code,
                    'reff' => $request->reff??$getkasbon->code,
                    'jenis' => '1',
                    'type' => $request->type,
                    'description' => $new_title_ci,
                    'total' => 0,
                    'account_id' => $request->cash_bank,
                    'date_transaction' => dateDB($request->date_transaction),
                    'status_cost' => $statusCost,
                    'created_by' => auth()->id()
                ]);

                if ($request->detail) {
                    CashTransactionDetail::where('header_id', $i->id)->delete();

                    $amount=0;
                    foreach ($request->detail as $value) {
                        if (empty($value)) continue;

                        $name= NULL;
                        if(isset($value['kasbon_id'])){
                            if($value['kasbon_id'] != null){
                                $name= $value['name'];
                            }
                        }
                        
                        $description_detail = $value['description'];

                        if(is_null($value['description'])){
                            $description_detail = "Transaksi Kas Masuk atas Kasbon : ".$getkasbon->code;
                        }
                        

                        $cost_idnya=(array_key_exists('cost_id',$value))?$value['cost_id']:null;

                        $ct_detail= CashTransactionDetail::create([
                            'header_id' => $i->id,
                            'account_id' => $getkasbon->account_advance_id,
                            'amount' => $value['amount'],
                            'job_order_cost_id' => $value['job_order_cost_id'] ?? null,
                            'manifest_cost_id' => $value['manifest_cost_id'] ?? null,
                            'cost_id' => $cost_idnya,
                            'uploaded_file' => $value['file'],
                            'description' => $new_title,
                            'jenis' => $value['jenis'],
                            'kasbon_cost_title' => $name
                        ]);

                        if(!is_null($value['account_id'])) {
                            $cekCC=cekCashCount($request->company_id,$value['account_id']);

                            if ($cekCC)
                                return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
                        }

                        $amount+=$value['amount'];

                        //jika jenis kasbon  add cash advance transaction
                        // if($value['jenis'] == 2){
                        if($request->kasbon_id != null && $request->kasbon_id != 0){
                            CashAdvanceTransaction::create([
                                'cash_advance_id' => $request->kasbon_id,
                                'cash_transaction_id' => $i->id,
                                'is_in' => $in
                            ]);
                        }

                        //cash advance in
                        $ca_in_out=CashAdvanceTransaction::where('cash_advance_transactions.cash_advance_id',$value['kasbon_id'])
                        ->leftJoin('cash_transactions','cash_transactions.id','cash_advance_transactions.cash_transaction_id')
                        ->select('cash_transactions.*','cash_advance_transactions.is_in')
                        ->get();
                        $amount_in= 0;
                        $amount_out= 0;

                        foreach($ca_in_out as $ci){
                            if($ci->is_in == 1){
                                $amount_in+=$ci->total;
                            }else{
                                $amount_out+=$ci->total;
                            }
                        }
                    }

                    if(!is_null($request->cash_bank)) {
                        $cekCC=cekCashCount($request->company_id,$request->cash_bank);

                        if ($cekCC)
                            return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
                    }

                    $i->update(['total' => $amount ]);

                    // create cost
                    if(isset($value['job_order_cost_id']) || isset($value['manifest_cost_id'])){

                    $cost_idnya=(array_key_exists('cost_id',$value))?$value['cost_id']:null; //jika bukan dari jo maupun manifest
                    if($value['job_order_cost_id'] != null || $value['manifest_cost_id'] != null ){
                        /*
                            Date : 25-11-2022
                            Description : Cek apakah value membawa cost_id atau tidak
                            Developer : Sendy
                            Status : Improve
                        */
                        if (@$value['cost_id'] != null) {
                            //cost type
                            $cost_type= DB::table('cost_types')->where('id',$value['cost_id'])->first();
                            //jo cost create
                            if($value['job_order_cost_id'] != null && $value['manifest_cost_id'] == null){
                                $add_cost= new JobOrderCost;
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['job_order_cost_id'];
                                $add_cost->cost_type_id=$cost_type->id;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=$cost_type->vendor_id;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->save();
                            }
                            //create cost manifest
                            else{
                                $add_cost= new ManifestCost();
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['manifest_cost_id'];
                                $add_cost->cost_type_id=$cost_type->id;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=$cost_type->vendor_id;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->save();
                            }
                        } else {
                            if($value['job_order_cost_id'] != null && $value['manifest_cost_id'] == null){
                                $add_cost= new JobOrderCost;
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['job_order_cost_id'];
                                $add_cost->cost_type_id=0;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=0;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->save();
                            }
                            //create cost manifest
                            else{
                                $add_cost= new ManifestCost();
                                $add_cost->company_id= $request->company_id;
                                $add_cost->header_id= $value['manifest_cost_id'];
                                $add_cost->cost_type_id=0;
                                $add_cost->transaction_type_id=21;
                                $add_cost->vendor_id=0;
                                $add_cost->create_by= auth()->user()->id;
                                $add_cost->qty=1;
                                $add_cost->price=$value['amount'];
                                $add_cost->total_price= $value['amount'];
                                $add_cost->status=5;
                                $add_cost->save();
                            }

                        }
                        
                        if(isset($add_cost))
                            $updateCT= CashTransactionDetail::find($ct_detail->id);
                            $updateCT->relation_cost_id= $add_cost->id;
                            $updateCT->save();
                    }


                    //update id cash advances

                    if($value['job_order_cost_id'] == null && $value['manifest_cost_id'] == null){
                        if(isset($value['kasbon_id'])){
                            if($value['kasbon_id'] != null) {
                                $ca = CashAdvance::find($value['kasbon_id']);
                            }
                        }
                    }
                    }
                }

                //from kasbon cash transaction
                if(isset($request->kasbon_id)){
                    if($request->kasbon_id > 0){
                        $kasbon= CashAdvance::find($request->kasbon_id);

                        //approve cash transaction if request have kasbon_id > 0
                        $cash_transaction = new \App\Http\Controllers\Finance\CashTransactionController();
                        $cash_transaction->approve($i->id);
                    }
                }

            }
        //[End] Jika kas keluar otomatis membuat kas masuk
        }

        DB::commit();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    /*
      Date : 04-03-2020
      Description : Menampilkan detail transaksi kas
      Developer : Didin
      Status : Edit
    */
    public function show($id)
    {
      $item = CashTransaction::find($id);

      $data['item'] = CashTransaction::with('company','type_transaction','account')
      ->leftJoin('cash_transaction_cost_statuses', 'cash_transaction_cost_statuses.id', 'cash_transactions.status_cost')
      ->where('cash_transactions.id', $id)
      ->select(
        DB::Raw('IFNULL(cash_transactions.code, cash_transactions.reff) as code_reff'),
        'cash_transactions.*',
        'cash_transaction_cost_statuses.slug AS status_cost_slug',
        'cash_transaction_cost_statuses.name AS status_cost_name'
      )
      ->first();

      $data['can_approve'] = $item->couldBeApproved();
      $data['detail']=CashTransactionDetail::with('account','contact')
      ->leftJoin('cost_types AS C2', 'C2.id', 'cash_transaction_details.cost_id')
      ->leftJoin('manifest_costs', 'manifest_costs.id', 'cash_transaction_details.manifest_cost_id')
      ->leftJoin('manifests', 'manifests.id', 'manifest_costs.header_id')
      ->leftJoin('job_orders', 'job_orders.id', 'cash_transaction_details.job_order_cost_id')
      ->leftJoin('cost_types AS C3', 'C3.id', 'manifest_costs.cost_type_id')
      ->where('cash_transaction_details.header_id', $id)
      ->selectRaw('cash_transaction_details.*, IFNULL(IFNULL(cash_transaction_details.kasbon_cost_title, C2.name), C3.name) AS name, IFNULL(IFNULL(job_orders.code,manifests.code),"") AS code')
      ->get();
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
      $data['account']=Account::with('parent')->where('is_base',0)->orderBy('code')->get();
      $data['type_transaction']=TypeTransaction::all();
      $data['company']=companyAdmin(auth()->id());
      $data['cash_category']=CashCategory::with('category')->where('is_base', 0)->get();
      $data['vendor']=Contact::where('is_vendor', 1)->where('vendor_status_approve', 2)->select('id','name')->get();
      $data['item']=CashTransaction::find($id);
      $data['detail']=CashTransactionDetail::with('account','contact')
      ->leftJoin('manifests', 'manifests.id', 'cash_transaction_details.manifest_cost_id')
      ->leftJoin('job_orders', 'job_orders.id', 'cash_transaction_details.job_order_cost_id')
      ->leftJoin('cost_types AS C3', 'C3.id', 'cash_transaction_details.cost_id')
      ->where('cash_transaction_details.header_id', $id)
      ->selectRaw('cash_transaction_details.*, IFNULL(cash_transaction_details.kasbon_cost_title,C3.name) AS name, IFNULL(IFNULL(manifests.code,job_orders.code),"") AS code')
      ->get();
      // $data['detail']=CashTransactionDetail::where('header_id', $id)->get();
      if ($data['item']->is_cut==1) {
        return Response::json(['message' => 'Data sudah tidak dapat di edit'],500);
      }
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    /*
      Date : 04-03-2020
      Description : Mengedit transaksi kas
      Developer : Didin
      Status : Edit
    */
    public function update(Request $request, $id)
    {
        // dd($request->detail);
        // die();
        $request->validate([
            'company_id' => 'required',
            'date_transaction' => 'required',
            'type' => 'required',
            'jenis' => 'required',
            // 'reff' => 'required',
            // 'cash_bank' => 'required',
            'detail' => 'required',
        ]);

        if ($request->type==1 && $request->jenis==1) {
            $tyname="CashIn";
        } elseif ($request->type==1 && $request->jenis==2) {
            $tyname="CashOut";
        } elseif ($request->type==2 && $request->jenis==1) {
            $tyname="BankIn";
        } else {
            $tyname="BankOut";
        }
        DB::beginTransaction();
        // $asli=CashTransaction::find($id);
        // dd($asli);
        // if (isset($asli->journal_id)) {
        //     Journal::find($asli->journal_id)->delete();
        // }

        $tp = TypeTransaction::where('slug', $tyname)->first();
        $code = new TransactionCode($request->company_id, $tyname);
        $code->setCode();
        $trx_code = $code->getCode();
        $i = CashTransaction::find($id);
        if(empty($request->cash_bank)){
            dd($request->cash_bank);
        }
        $i->update([
            'description' => $request->description,
            'account_id' => $request->cash_bank,//wajib isi
            'date_transaction' => dateDB($request->date_transaction) //wajib isi
        ]);
        //   if ($request->jenis==1) {
        //     $j=Journal::create([
        //       'company_id' => $request->company_id,
        //       'type_transaction_id' => $tp->id,
        //       'date_transaction' => dateDB($request->date_transaction),
        //       'created_by' => auth()->id(),
        //       'code' => $trx_code,
        //       'description' => $request->description,
        //       'debet' => 0,
        //       'credit' => 0,
        //     ]);
        //   }
        // CashTransaction::find($i->id)->update(['journal_id' => $j->id??null]);
        // CashTransactionDetail::where('header_id', $i->id)->delete();

        $amount=0;
        foreach ($request->detail as $value) {
            if (empty($value))
                continue;
                $name= NULL;
                if(isset($value['kasbon_id'])){
                    if($value['kasbon_id'] != null){
                        $name= $value['name'];
                    }
                }

                if(!array_key_exists('id', $value) || $value['id'] == 0 ){
                    $relasi= null;
                }
                else{
                    $dataDetail =CashTransactionDetail::whereId($value['id'])->first();
                    $relasi= $dataDetail->relation_cost_id;
                }

                $data = [
                    'header_id' => $i->id,
                    'account_id' => $value['account_id'],
                    'uploaded_file' => $value['file'],
                    'amount' => $value['amount'],
                    'description' => @$value['description'],
                    'jenis' => $value['jenis'],
                    'job_order_cost_id' => $value['job_order_cost_id'] ?? null,
                    'manifest_cost_id' => $value['manifest_cost_id'] ?? null,
                    'cost_id' => $value['cost_id'] ?? null,
                    'kasbon_cost_title' => $name,
                    'relation_cost_id' => $relasi

                ];

                if(!array_key_exists('id', $value) || $value['id'] == 0 ){
                    $ct_detail= CashTransactionDetail::create([
                        'header_id' => $i->id,
                        'account_id' => $value['account_id'],
                        'amount' => $value['amount'],
                        'job_order_cost_id' => $value['job_order_cost_id'] ?? null,
                        'manifest_cost_id' => $value['manifest_cost_id'] ?? null,
                        'cost_id' => $value['cost_id'] ?? null,
                        'uploaded_file' => $value['file'],
                        'description' => @$value['description'],
                        'jenis' => $value['jenis'],
                        'kasbon_cost_title' => $name,
                        'relation_cost_id' => $relasi

                    ]);

                    $ct_detail_id= $ct_detail->id;
                }
                else{
                    $ct_detail= CashTransactionDetail::whereId($value['id'])->update($data);
                    $ct_detail_id= $value['id'];

                    // dd($ct_detail,'sds');
                    // die();
                }
                // dd($ct_detail_id);
                // die();

            $cekCC = cekCashCount($request->company_id,$value['account_id']);
            if ($cekCC) {
                return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
            }

            // if ($request->jenis==1) {
            // JournalDetail::create([
            //     'header_id' => $j->id,
            //     'account_id' => $value['account_id'],
            //     // 'cash_category_id' => $cid,
            //     'debet' => ($request->jenis==2?$value['amount']:0),
            //     'credit' => ($request->jenis==1?$value['amount']:0),
            // ]);
            // }

             // create cost
             if(isset($value['job_order_cost_id']) || isset($value['manifest_cost_id'])){

             if($value['job_order_cost_id'] != null || $value['manifest_cost_id'] != null){
                //cost type
                $cost_type= DB::table('cost_types')->where('id',$value['cost_id'])->first();
                //jo cost create
                if(!empty($cost_type) || $value['job_order_cost_id'] != null && $value['manifest_cost_id'] == null){
                    $add_cost= new JobOrderCost;
                    $add_cost->header_id= $value['job_order_cost_id'];
                    $add_cost->cost_type_id=$cost_type->id;
                    $add_cost->transaction_type_id=21;
                    $add_cost->vendor_id=$cost_type->vendor_id;
                    $add_cost->create_by= auth()->user()->id;
                    $add_cost->qty=1;
                    $add_cost->price=$value['amount'];
                    $add_cost->total_price= $value['amount'];
                    $add_cost->status=5;
                    $add_cost->save();
                }
                //create cost manifest
                else{
                    $add_cost= new ManifestCost();
                    $add_cost->header_id= $value['manifest_cost_id'];
                    $add_cost->cost_type_id=$cost_type->id??''; // 
                    $add_cost->transaction_type_id=21;
                    $add_cost->vendor_id=$cost_type->vendor_id??'';
                    $add_cost->create_by= auth()->user()->id;
                    $add_cost->qty=1;
                    $add_cost->price=$value['amount'];
                    $add_cost->total_price= $value['amount'];
                    $add_cost->status=5;
                    $add_cost->save();
                }

                $updateCT= CashTransactionDetail::find($ct_detail_id);
                if($updateCT != null && isset($add_cost)){
                    if(!array_key_exists('id', $value) || $value['id'] == 0 ){
                        $updateCT->relation_cost_id= $add_cost->id;
                        $updateCT->save();
                    }
                }
                // dd($updateCT);
                // die();
            }

            //update id cash advances
            if($value['job_order_cost_id'] == null && $value['manifest_cost_id'] == null){
                if(isset($value['kasbon_id'])) {
                if($value['kasbon_id'] != null) {
                    $ca = CashAdvance::find($value['kasbon_id']);
                    $ca->cash_transaction_id= $i->id;
                    $ca->save();
                }
                }
            }
            }

            $amount+=$value['amount'];
        }

        $cekCC=cekCashCount($request->company_id,$request->cash_bank);
        if ($cekCC) {
            return Response::json(['message' => 'Akun yang anda masukkan sementara dibekukan dikarenakan sedang dalam perhitungan fisik kas'],500);
        }

        // if ($request->jenis==1) {
        //     JournalDetail::create([
        //         'header_id' => $j->id,
        //         'account_id' => $request->cash_bank,
        //         // 'cash_category_id' => $cid,
        //         'debet' => ($request->jenis==1?$amount:0),
        //         'credit' => ($request->jenis==2?$amount:0),
        //     ]);
        // }

        $i->update([
            'total' => $amount,
        ]);

        //   $asli->update([
        //     'edit_count' => DB::raw('edit_count+1'),
        //     'status' => 2,
        //     'is_cut' => 1
        //   ]);
        DB::commit();

        return Response::json(null);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
      DB::beginTransaction();
      $i=CashTransaction::find($id);
      Closing::preventByDate($i->date_transaction);
      if (isset($i->journal_id)) {
        //Journal::find($i->journal_id)->delete();//del
        // J::destroy($i->journal_id);
      }
      $i->update([
        'status' => 3,
        'is_cut' => 1
      ]); 

      // merollback 
      $cash_detail= CashTransactionDetail::where('header_id',$id)->first();
      $mid = $cash_detail->manifest_cost_id;
      if($mid != null) {
          ManifestCost::find($mid)->update([
            'approve_by' => null,
            'status' => 1,
            'is_invoice' => 0,
            'qty_real' => 0,
            'price_real' => 0,
            'total_real' => 0,
          ]);
          JobOrderCost::where('manifest_cost_id', $mid)->update([
            'status' => 1,
            'qty_real' => 0,
            'price_real' => 0,
          ]);
      } else {
        JobOrderCost::where('id', $cash_detail->job_order_cost_id)->update([
            'status' => 1,
            'qty_real' => 0,
            'price_real' => 0,
        ]);
      }

      //delete jo/PL relasi
      $cash_detail= CashTransactionDetail::where('header_id',$id)->get();
      foreach($cash_detail as $ct){
          if($ct->relation_cost_id != null){
              $manifest=ManifestCost::find($ct->relation_cost_id);
              if($manifest != null){
                  if($manifest->job_order_cost_id != null){
                      $jo= JobOrderCost::find($manifest->job_order_cost_id);
                      $jo->delete(); //del
                  }
                  $manifest->delete(); //del
              }
          }
      }

        //cash advance delete
          $cad=CashAdvanceTransaction::where('cash_transaction_id',$id)->first();
          if($cad != null){
            $c_ad=CashAdvanceTransaction::find($cad->id);
            $c_ad->delete();//del
          }
      DB::commit();
    }
    public static function validateMCinvoiced($id) { //validasi is_invoice=>manifest_cost.journal_cost_manifest
        $joc = DB::table('manifest_costs')->where('journal_realisasi', $id)->first();
        if(!empty($joc)){
            if(empty($joc->journal_cost_manifest)){// belom di invoice maka boleh update kembali ke status 1 draft manifest
                DB::table('manifest_costs')
                ->where('journal_realisasi',$id)
                ->update(['status' =>1,'journal_realisasi'=>NULL]);
                JobOrderCost::where('manifest_cost_id',$joc->id)->update([
                    'status'=>1
                ]);
            }else{
                throw new Exception('Manifest Cost Invoiced');
            }
        }
    }

    public function approve($id)
    {
        $status_code = 200;
        $msg = 'Data successfully updated';
        DB::beginTransaction();
        try {
            $depositSup= DB::table('type_transactions')->where('slug','depositSupplier')->first();
            $depositCustomer= DB::table('type_transactions')->where('slug','depositCustomer')->first();

            CT::validateWasFinished($id);
            $ct=CashTransaction::find($id);
            Closing::preventByDate($ct->date_transaction);
            $error_akun = false;
            $so = SalesOrder::where('customer_order_id',$ct->customer_order_id)->update(['sales_order_status_id'=>3]);
            if(!$ct->account_id)
                return Response::json(['message' => 'Akun pada data belum lengkap'], 500);

            $details=DB::table('cash_transaction_details')->where('header_id', $id)->selectRaw('id,account_id,amount,description,job_order_cost_id,manifest_cost_id')->get();
            foreach($details as $detail) {
                if($detail->job_order_cost_id) DB::update("UPDATE job_order_costs SET is_invoice = 1 WHERE id = {$detail->job_order_cost_id}");
                if($detail->manifest_cost_id) DB::update("UPDATE manifest_costs SET is_invoice = 1 WHERE id = {$detail->manifest_cost_id}");
                if(!$detail->account_id)
                    return Response::json(['message' => 'Akun pada data belum lengkap'], 500);
            }

            //akun kas branch
            $companies= DB::table('companies')->where('id',$ct->company_id)->first();
            if($companies->cash_account_id != null){
                $cashAccountId = $companies->cash_account_id;
            }
            else{
                $cashAccount = DB::table('accounts')
                ->where('no_cash_bank', '>', 0)
                ->select('id')
                ->first();
                $cashAccountId=$cashAccount->id;
            }

            //add to deposit customer from sales order
            if($ct->type_transaction_id == $depositCustomer->id){
                //customer order
                $co_data= DB::table('customer_orders')->where('id',$ct->customer_order_id)->first();

                //sales order
                $so_data= DB::table('sales_orders')->where('customer_order_id',$co_data->id)->first();

                //contact
                $contact= DB::table('contacts')->where('id',$co_data->customer_id)->first();

                //create deposit customer
                $umCustomer = new \App\Http\Controllers\Finance\UmCustomerController();
                $amountTotal=0;
                    foreach($details as $detail) {
                        $amountTotal += $detail->amount;
                    }
                    $detailArrayData[]= ["type" => 1,
                    "cash_account_id" => $cashAccountId,
                    "amount" => $amountTotal,
                    "description" => 'Sales Order - '. $so_data->code

                ];
                    $paramsDepoCustomer=[
                        "company_id" => $ct->company_id,
                        "date_transaction" => Carbon::now()->format('Y-m-d'),
                        "amount" => $amountTotal,
                        "contact_id" => $co_data->customer_id,
                        "sales_order_id" => $so_data->id,
                        "description" => 'Deposit Customer '. $contact->name,
                        "detail" => $detailArrayData

                    ];

                    $umCustomer->store(new Request($paramsDepoCustomer));

            }

            //check cash transaction from purchase order in warehouse transit
            $codePO= str_replace("Purchase order - ","",$ct->description);
            $searchPO= DB::table('purchase_orders')->where('code',$codePO)->first();


            //if PO is found
            if($searchPO != null){
                $warehouse= DB::table('warehouses')->where('id',$searchPO->warehouse_id)->first();
                $contact= DB::table('contacts')->where('id',$searchPO->supplier_id)->first();

                if($ct->type_transaction_id == $depositSup->id){
                if($warehouse->is_transit == 1){

                    //create deposit vendor
                    $umSupllier = new \App\Http\Controllers\Finance\UmSupplierController();
                    $amountDetail=0;
                    foreach($details as $detail) {
                        $amountDetail += $detail->amount;
                    }
                    $detailArray[]= ["type" => 1,
                    "cash_account_id" => $cashAccountId,
                    "amount" => $amountDetail];
                    $paramsDepoVendor=[
                        "company_id" => $searchPO->company_id,
                        "date_transaction" => Carbon::now()->format('Y-m-d'),
                        "amount" => $amountDetail,
                        "contact_id" => $searchPO->supplier_id,
                        "description_header" => 'Deposit Vendor '. $contact->name,
                        "detail" => $detailArray,
                        "description_detail" => 'Purchase Order - '. $searchPO->code
                    ];

                    $umSupllier->store(new Request($paramsDepoVendor));

                    //create good receipt dan stocklist
                    //vehicle
                    $vehicle=DB::table('vehicle_types')->first();
                    //purchase order detail
                    $poDetail=DB::table('purchase_order_details')->where('header_id',$searchPO->id)->get();
                    foreach($poDetail as $detail){
                        $item= DB::table('items')->where('id',$detail->item_id)->first();


                        $detailsItemWR[]=(object) array(
                            "piece_name" => "Item",
                            "piece_name_2" => "Kubikasi/Bulan",
                            "capacity_volume" => "~",
                            "capacity_tonase" => "~",
                            "piece_id" => 3,
                            "piece_id_2" => 6,
                            "storage_type" => "HANDLING",
                            "rack_name" => "Handling Area",
                            "rack_id" => null,
                            "kemasan" => "BAG",
                            "vehicle" => $vehicle->name,
                            "vehicle_type_id" => $vehicle->type,
                            "imposition_name" => "Kubikasi",
                            "imposition" => 1,
                            "qty_2" => $detail->qty,
                            "qty" => $detail->qty,
                            "weight" => $item->tonase,
                            "long" => $item->long,
                            "wide" => $item->wide,
                            "high" => $item->height,
                            "item_name" => $item->name."(".$item->name.")",
                            "item_id" => $item->id,
                            "purchase_order_detail_id" => $detail->id
                        );
                    }

                    $paramsWR=[
                        "detail" => $detailsItemWR,
                        "is_export" => "1",
                        "is_overtime" => "0",
                        "stripping_type" => "1",
                        "company_id" => "40",
                        "receive_date" => Carbon::now()->format('d-m-Y'),
                        "receive_time" => Carbon::now()->format('H:i'),
                        "stripping_date" => Carbon::now()->format('d-m-Y'),
                        "stripping_time" => Carbon::now()->format('H:i'),
                        "purchase_order_id" => $searchPO->id,
                        "total" => "0",
                        "receipt_type_id" => "11",
                        "warehouse_id" => $searchPO->warehouse_id,
                        "vehicle_type_id" => $vehicle->id,
                        "status" => "1",
                        "is_seen" => "0"
                    ];




                    WR::store($paramsWR);
                    $so_create= 0;
                    //update status costumer order
                    foreach($poDetail as $detail){

                    $purchaseReqDetail= DB::table('purchase_request_details')->where('header_id',$searchPO->purchase_request_id)->get();

                        foreach($purchaseReqDetail as $pr){
                            $customerOrderDetail= CustomerOrderDetail::where('id',$pr->customer_order_details_id)->first();

                            $co_receipt_detail= CustomerOrderDetail::where('header_id',$customerOrderDetail->header_id)->where('is_receipt',1)->get();
                            $all_co_detail= CustomerOrderDetail::where('header_id',$customerOrderDetail->header_id)->get();
                            if(count($co_receipt_detail) == count($all_co_detail)){
                                $statusApproved = DB::table('customer_order_statuses')->where('slug', 'approved')->first();
                                $co =CustomerOrder::find($customerOrderDetail->header_id);
                                $co->customer_order_status_id = $statusApproved->id;
                                $co->save();
                                $so_create= 1;
                                 //create so
                                 $check_so= SalesOrder::where('customer_order_id',$co->id)->first();
                                 if($check_so == null){
                                    CO::generateSo($co->id);

                                 }
                            }

                        }
                    }

                }
                }
            }

            $jurnal=[
                'company_id' => $ct->company_id,
                'date_transaction' => $ct->date_transaction??date('Y-m-d'),// mengikuti tanggal casttransaksi
                'created_at' => $ct->date_transaction??date('Y-m-d'),//ngikuti castransaksi
                'created_by' => auth()->id(),
                'code' => $ct->code,
                'description' => $ct->description,
                'debet' => 0,
                'credit' => 0,
                'type_transaction_id' => $ct->type_transaction_id
            ];

            $j = Journal::create($jurnal);

            $amount=0;
            if($ct->from_kasbon == 1){

                $tp_in = DB::table('type_transactions')->where('slug', 'cashIn')->first();
                $detailFirst= CashTransactionDetail::where('header_id',$ct->id)->first();
                $mCostData= ManifestCost::find($detailFirst->manifest_cost_id);
                $cost_t=CostType::find($mCostData->cost_type_id);
                $m=Manifest::find($mCostData->header_id);
                $total_price_real=$mCostData->qty_real*$mCostData->price_real;

                if($ct->type_transaction_id == $tp_in->id){
                    //kas masuk


                    //credit akun uang muka
                    if(!$cost_t->akun_uang_muka){
                        throw new Exception('Akun uang muka pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_uang_muka,
                        'debet' => 0,
                        'credit' => $mCostData->total_price,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                    if(!$cost_t->akun_biaya){
                        throw new Exception('Akun biaya pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_biaya,
                        'debet' => $total_price_real,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                    if(!$cost_t->akun_kas_hutang){
                        throw new Exception('Akun kas hutang pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_kas_hutang,
                        'debet' => $mCostData->total_price-$total_price_real,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                }
                else{
                    if(!$cost_t->akun_uang_muka){
                        throw new Exception('Akun uang muka pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_uang_muka,
                        'debet' => 0,
                        'credit' => $mCostData->total_price,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                    if(!$cost_t->akun_biaya){
                        throw new Exception('Akun biaya pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_biaya,
                        'debet' => $total_price_real,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                    if(!$cost_t->akun_kas_hutang){
                        throw new Exception('Akun kas hutang pada cost belum dipilih!');
                    }
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $cost_t->akun_kas_hutang,
                        'credit' => $total_price_real-$mCostData->total_price,
                        'description' => "Kasbon Biaya Manifest - ".$m->code." - ".$cost_t->name
                    ]);
                }

            }
            else{
                if($ct->jenis == 2){
                    foreach ($details as $value) {
                        JournalDetail::create([
                            'header_id' => $j->id,
                            'account_id' => $value->account_id,
                            'debet' => $value->amount,
                            'credit' => 0,
                            'description' => $value->description,
                        ]);
                        $amount+=$value->amount;
                    }
    
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $ct->account_id,
                        'debet' => 0,
                        'credit' => $amount,
                        'description' => $ct->description
                    ]);
                } else {
                    foreach ($details as $value) {
                        JournalDetail::create([
                            'header_id' => $j->id,
                            'account_id' => $value->account_id,
                            'debet' => 0,
                            'credit' => $value->amount,
                            'description' => $value->description,
                        ]);
                        $amount+=$value->amount;
                    }
    
                    JournalDetail::create([
                        'header_id' => $j->id,
                        'account_id' => $ct->account_id,
                        'debet' => $amount,
                        'credit' => 0,
                        'description' => $ct->description
                    ]);
                }
            }

            foreach($details as $detail) {
                if($detail->manifest_cost_id != null){
                    $mcost= ManifestCost::find($detail->manifest_cost_id);
                    if(!empty($mcost) && $mcost->is_kasbon == 1){
                        if($mcost->jurnal_submission != null){
                            $mcost->journal_realisasi= $j->id;
                            $mcost->is_invoice= 0;
                            $mcost->save();
                        }
                        else{
                            $mcost->journal_waiting_realisasi= $j->id;
                            $mcost->status= 11;
                            $mcost->is_invoice= 0;
                            $mcost->save();
                        }

                    }
                }
            }

            DB::table('cash_transactions')
              ->whereId($id)
              ->update([
                  'status_cost' => 3,
                  'is_cut' => 1,
                  'journal_id' => $j->id
            ]);

            //update cash advance
            $ca_data=CashAdvanceTransaction::where('cash_transaction_id',$id)->first();
            if($ca_data != null){
                //get data kas masuk kas bon
                $c_in=CashAdvanceTransaction::where('cash_advance_transactions.cash_advance_id',$ca_data->cash_advance_id)
                ->where('cash_transactions.status_cost',3)->where('is_in',1)
                ->leftJoin('cash_transactions','cash_transactions.id','cash_advance_transactions.cash_transaction_id')
                ->sum('cash_transactions.total');
                //get data first kas keluar untuk cash advance
                $c_out=CashAdvanceTransaction::
                leftJoin('cash_transactions','cash_transactions.id','cash_advance_transactions.cash_transaction_id')
                ->where('cash_advance_transactions.cash_advance_id',$ca_data->cash_advance_id)
                ->where('cash_transactions.status_cost',3)->where('is_in',0)
                ->select('cash_transactions.total')
                ->orderBy("cash_advance_transactions.id", "ASC")
                ->first();

                // Cash Advance Out harus sama dengan Cash Advance IN agar status menjadi complete
                if($c_in == $c_out->total){
                    $ca_update=CashAdvance::find($ca_data->cash_advance_id);
                    $ca_update->status=8;
                    $ca_update->save();

                    CashAdvanceStatus::create([
                        'header_id' => $ca_data->cash_advance_id,
                        'status' => 8,
                        'user_id' => auth()->user()->id
                    ]);
                }
            }

            DB::commit();
        } catch(Exception $e) {
            DB::rollback();
            $status_code = 421;
            $msg = $e->getMessage();
        }
        $data['message'] = $msg;
        return Response::json($data, $status_code);
    }

    public function uploadBukti(Request $request)
    {
        $request->validate([
            'file' => 'required|mimetypes:image/jpeg,image/png,application/pdf',
        ],[
            'file.mimetypes' => 'File Harus Berupa Gambar atau PDF!',
            'file.required' => 'File belum ada!'
        ]);

        $file=$request->file('file');
        //var_dump($file);
        $file_name="BTK_".time()."_".$file->getClientOriginalName();

        $file->move(public_path('files'),$file_name);

        return Response::json(['file' => $file_name]);
    }

    public function deleteBukti(Request $request)
    {
      $request->validate([
        'filename' => 'required'
      ]);

      $file = "files/{$request->filename}";

      try{
        unlink($file);
      } catch(Exception $e) {
        return Response::json(['status'=>'error','message'=>$e->getMessage()]);
      }

      return Response::json(['status'=>'ok']);
    }

    public function delete_detail($id)
    {
        DB::beginTransaction();
            CashTransactionDetail::find($id)->delete();
        DB::commit();
        return Response::json(null);
    }

    public function account_data($id){
        $data['account']=Account::with('parent')->where('id',$id)->where('is_base',0)->orderBy('code')->first();
        return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function report_card(){
      $data['company']=companyAdmin(auth()->id());
      $data['account']=Account::with('parent')->where('is_base',0)->orderBy('code')->get();
      return Response::json($data, 200, [], JSON_NUMERIC_CHECK);
    }

    public function export_card_pdf(Request $request){
        $start_date = Carbon::parse($request->start_date);
        $start_date_string = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date_string = Carbon::parse($request->end_date)->format('Y-m-d');
        // Calculate one day before $start_date
        $before_start = $start_date->subDay();
        $before_start = Carbon::parse($before_start)->format('Y-m-d');

        $remark = DB::table('print_remarks')
        ->where('company_id', auth()->user()->company_id)
        ->first();

        if(!isset($request->company_id)){
            $company = DB::table('companies')->select('companies.*')->get();
        } else {
            $company = DB::table('companies')->where('id', $request->company_id)->get();
        }
        
        $companies = [];
        $accounts = [];
        $datas = [];

        foreach ($company as $key => $value) {
            // PILIH BERDASARKAN KAS / BANK TYPE NYA
            if(!isset($request->cash_bank)){
                $account = DB::table('accounts')
                ->where('is_base', 0)
                ->where('no_cash_bank', $request->type)
                ->whereRaw("(company_id = $value->id OR company_id IS NULL)")
                ->orderBy('code')->get();
            } else {
                $account = DB::table('accounts')->where('id', $request->cash_bank)->orderBy('code')->get();
            }
            $companies[] = $value->name;
            $accounts[] = $account;
            foreach ($account as $keyAccount => $valueAccount) {
                $wr = "company_id = $value->id AND type = $request->type AND status_cost = 3 AND account_id = $valueAccount->id";
                $firstTransaction = DB::table('cash_transactions')->whereRaw($wr)->first();
                // bypass first transaction to next company
                if (!$firstTransaction) {
                    $datas[$key][$keyAccount] = null;
                    continue;
                }
                // GET FIRST DATA ON DB
                $firstDateTransaction = Carbon::parse($firstTransaction->date_transaction)->format('Y-m-d');
                $wrDate = "DATE_FORMAT(cash_transactions.date_transaction, '%Y-%m-%d') BETWEEN '$firstDateTransaction' AND '$before_start'";
                
                //  GET KAS TRANSAKSI FROM THE BEGINNING AND COUNT IS SALDO DEBET AND KREDIT
                $saldoDebet = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 1)
                ->sum('cash_transactions.total');
                $saldoKredit = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 2)
                ->sum('cash_transactions.total');
                $saldoAwal = $saldoDebet-$saldoKredit;
                $datas[$key][$keyAccount] = [
                    (object) 
                    [ 
                        'nama' => 'Saldo  Periode '.fullDate($start_date_string), 
                        'debet' =>  ( $saldoAwal < 0 )? 0 : $saldoAwal ,
                        'kredit' =>  ( $saldoAwal < 0 )? abs($saldoAwal) : 0 ,
                        'saldo' => $saldoAwal
                    ]
                ];
                // GET ALL TRANSAKSI FROM THE PERIOD SELECTED
                $wrDateAll = "DATE_FORMAT(ct.date_transaction, '%Y-%m-%d') BETWEEN '$start_date_string' AND '$end_date_string'";
                $allTrx = DB::table('cash_transactions as ct')
                ->leftJoin('type_transactions as type', 'type.id', 'ct.type_transaction_id')
                ->whereRaw("$wr AND $wrDateAll")
                ->select('ct.*', 'type.name as type_name')
                ->orderBy('ct.date_transaction')
                ->get();
                $saldoExisting = $saldoAwal;
                foreach ($allTrx as $valTrx) {
                    $debet = ( $valTrx->jenis == 1 )? $valTrx->total : 0;
                    $kredit = ( $valTrx->jenis == 2 )? $valTrx->total : 0;
                    $saldoExisting += $debet-$kredit;
                    $pushData = (object) 
                    [ 
                        'code' => $valTrx->code??$valTrx->reff,
                        'keterangan' => $valTrx->description,
                        'tgl_trx' => dateView($valTrx->date_transaction),
                        'type_name' => $valTrx->type_name,
                        'debet' =>   $debet,
                        'kredit' =>  $kredit,
                        'saldo' => $saldoExisting
                    ];
                    array_push($datas[$key][$keyAccount], $pushData);
                }
            }
        }

        $prevent_zero = $request->prevent_zero;
        $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';

        // UNTUK MENGHAPUS ACCOUNTS DARI DATA YANG == 0
        if ($prevent_zero == "true") {
            foreach ($datas as $key => $value) {
                if (isset($value)) {
                    foreach ($value as $key1 => $value1) {
                        if(isset($value1)){
                            if (count($value1) == 1) {
                                $saldoLine = $value1[0];
                                if($saldoLine->saldo == 0){
                                    unset($accounts[$key][$key1]);
                                }
                            }
                        } else {
                            unset($accounts[$key][$key1]);
                        }
                    }
                }
            }
        }
        
        $items = [
            'type' => $request->type == 1 ? 'kas' : 'bank',
            'companies' => $companies,
            'accounts' => $accounts,
            'data' => $datas,
            'start' => $start_date_string,
            'end' => $end_date_string,
            'remark' => $remark
        ];

        $pdf = PDF::loadView('finance/kas_bank/kartu', $items)
        ->setPaper('a4', 'landscape');
        // ->setPaper(array(0,0,609.4488,935.433), 'landscape'); // KERTAS F4
        return $pdf->stream('Kartu Kas - Bank.pdf');
        // return view('finance/kas_bank/kartu', $items);
    }

    public function export_card_excel(Request $request){
        $start_date = Carbon::parse($request->start_date);
        $start_date_string = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date_string = Carbon::parse($request->end_date)->format('Y-m-d');
        // Calculate one day before $start_date
        $before_start = $start_date->subDay();
        $before_start = Carbon::parse($before_start)->format('Y-m-d');

        $remark = DB::table('print_remarks')
        ->where('company_id', auth()->user()->company_id)
        ->first();

        if(!isset($request->company_id)){
            $company = DB::table('companies')->select('companies.*')->get();
        } else {
            $company = DB::table('companies')->where('id', $request->company_id)->get();
        }
        
        $companies = [];
        $accounts = [];
        $datas = [];

        foreach ($company as $key => $value) {
            // PILIH BERDASARKAN KAS / BANK TYPE NYA
            if(!isset($request->cash_bank)){
                $account = DB::table('accounts')
                ->where('is_base', 0)
                ->where('no_cash_bank', $request->type)
                ->whereRaw("(company_id = $value->id OR company_id IS NULL)")
                ->orderBy('code')->get();
            } else {
                $account = DB::table('accounts')->where('id', $request->cash_bank)->orderBy('code')->get();
            }
            $companies[] = $value->name;
            $accounts[] = $account;
            foreach ($account as $keyAccount => $valueAccount) {
                $wr = "company_id = $value->id AND type = $request->type AND status_cost = 3 AND account_id = $valueAccount->id";
                $firstTransaction = DB::table('cash_transactions')->whereRaw($wr)->first();
                // bypass first transaction to next company
                if (!$firstTransaction) {
                    $datas[$key][$keyAccount] = null;
                    continue;
                }
                // GET FIRST DATA ON DB
                $firstDateTransaction = Carbon::parse($firstTransaction->date_transaction)->format('Y-m-d');
                $wrDate = "DATE_FORMAT(cash_transactions.date_transaction, '%Y-%m-%d') BETWEEN '$firstDateTransaction' AND '$before_start'";
                
                //  GET KAS TRANSAKSI FROM THE BEGINNING AND COUNT IS SALDO DEBET AND KREDIT
                $saldoDebet = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 1)
                ->sum('cash_transactions.total');
                $saldoKredit = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 2)
                ->sum('cash_transactions.total');
                $saldoAwal = $saldoDebet-$saldoKredit;
                $datas[$key][$keyAccount] = [
                    (object) 
                    [ 
                        'nama' => 'Saldo  Periode '.fullDate($start_date_string), 
                        'debet' =>  ( $saldoAwal < 0 )? 0 : $saldoAwal ,
                        'kredit' =>  ( $saldoAwal < 0 )? abs($saldoAwal) : 0 ,
                        'saldo' => $saldoAwal
                    ]
                ];
                // GET ALL TRANSAKSI FROM THE PERIOD SELECTED
                $wrDateAll = "DATE_FORMAT(ct.date_transaction, '%Y-%m-%d') BETWEEN '$start_date_string' AND '$end_date_string'";
                $allTrx = DB::table('cash_transactions as ct')
                ->leftJoin('type_transactions as type', 'type.id', 'ct.type_transaction_id')
                ->whereRaw("$wr AND $wrDateAll")
                ->select('ct.*', 'type.name as type_name')
                ->orderBy('ct.date_transaction')
                ->get();
                $saldoExisting = $saldoAwal;
                foreach ($allTrx as $valTrx) {
                    $debet = ( $valTrx->jenis == 1 )? $valTrx->total : 0;
                    $kredit = ( $valTrx->jenis == 2 )? $valTrx->total : 0;
                    $saldoExisting += $debet-$kredit;
                    $pushData = (object) 
                    [ 
                        'code' => $valTrx->code??$valTrx->reff,
                        'keterangan' => $valTrx->description,
                        'tgl_trx' => dateView($valTrx->date_transaction),
                        'type_name' => $valTrx->type_name,
                        'debet' =>   $debet,
                        'kredit' =>  $kredit,
                        'saldo' => $saldoExisting
                    ];
                    array_push($datas[$key][$keyAccount], $pushData);
                }
            }
        }

        $prevent_zero = $request->prevent_zero;
        $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';

        // UNTUK MENGHAPUS ACCOUNTS DARI DATA YANG == 0
        if ($prevent_zero == "true") {
            foreach ($datas as $key => $value) {
                if (isset($value)) {
                    foreach ($value as $key1 => $value1) {
                        if(isset($value1)){
                            if (count($value1) == 1) {
                                $saldoLine = $value1[0];
                                if($saldoLine->saldo == 0){
                                    unset($accounts[$key][$key1]);
                                }
                            }
                        } else {
                            unset($accounts[$key][$key1]);
                        }
                    }
                }
            }
        }
        
        $items = [
            'type' => $request->type == 1 ? 'kas' : 'bank',
            'companies' => $companies,
            'accounts' => $accounts,
            'data' => $datas,
            'start' => $start_date_string,
            'end' => $end_date_string,
            'remark' => $remark
        ];
        
        $fileName = "Kartu ".$items['type'];
        $now = date("d_m_Y_H_i_s");
        return Excel::download(New CardCashBank($items), "$fileName$now.xlsx");
    }

    public function export_card_preview(Request $request){
        $start_date = Carbon::parse($request->start_date);
        $start_date_string = Carbon::parse($request->start_date)->format('Y-m-d');
        $end_date_string = Carbon::parse($request->end_date)->format('Y-m-d');
        // Calculate one day before $start_date
        $before_start = $start_date->subDay();
        $before_start = Carbon::parse($before_start)->format('Y-m-d');

        $remark = DB::table('print_remarks')
        ->where('company_id', auth()->user()->company_id)
        ->first();

        if(!isset($request->company_id)){
            $company = DB::table('companies')->select('companies.*')->get();
        } else {
            $company = DB::table('companies')->where('id', $request->company_id)->get();
        }
        
        $companies = [];
        $accounts = [];
        $datas = [];

        foreach ($company as $key => $value) {
            // PILIH BERDASARKAN KAS / BANK TYPE NYA
            if(!isset($request->cash_bank)){
                $account = DB::table('accounts')
                ->where('is_base', 0)
                ->where('no_cash_bank', $request->type)
                ->whereRaw("(company_id = $value->id OR company_id IS NULL)")
                ->orderBy('code')->get();
            } else {
                $account = DB::table('accounts')->where('id', $request->cash_bank)->orderBy('code')->get();
            }
            $companies[] = $value->name;
            $accounts[] = $account;
            foreach ($account as $keyAccount => $valueAccount) {
                $wr = "company_id = $value->id AND type = $request->type AND status_cost = 3 AND account_id = $valueAccount->id";
                $firstTransaction = DB::table('cash_transactions')->whereRaw($wr)->first();
                // bypass first transaction to next company
                if (!$firstTransaction) {
                    $datas[$key][$keyAccount] = null;
                    continue;
                }
                // GET FIRST DATA ON DB
                $firstDateTransaction = Carbon::parse($firstTransaction->date_transaction)->format('Y-m-d');
                $wrDate = "DATE_FORMAT(cash_transactions.date_transaction, '%Y-%m-%d') BETWEEN '$firstDateTransaction' AND '$before_start'";
                
                //  GET KAS TRANSAKSI FROM THE BEGINNING AND COUNT IS SALDO DEBET AND KREDIT
                $saldoDebet = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 1)
                ->sum('cash_transactions.total');
                $saldoKredit = DB::table('cash_transactions')->whereRaw("$wr AND $wrDate")
                ->where('jenis', 2)
                ->sum('cash_transactions.total');
                $saldoAwal = $saldoDebet-$saldoKredit;
                $datas[$key][$keyAccount] = [
                    (object) 
                    [ 
                        'nama' => 'Saldo  Periode '.fullDate($start_date_string), 
                        'debet' =>  ( $saldoAwal < 0 )? 0 : $saldoAwal ,
                        'kredit' =>  ( $saldoAwal < 0 )? abs($saldoAwal) : 0 ,
                        'saldo' => $saldoAwal
                    ]
                ];
                // GET ALL TRANSAKSI FROM THE PERIOD SELECTED
                $wrDateAll = "DATE_FORMAT(ct.date_transaction, '%Y-%m-%d') BETWEEN '$start_date_string' AND '$end_date_string'";
                $allTrx = DB::table('cash_transactions as ct')
                ->leftJoin('type_transactions as type', 'type.id', 'ct.type_transaction_id')
                ->whereRaw("$wr AND $wrDateAll")
                ->select('ct.*', 'type.name as type_name')
                ->orderBy('ct.date_transaction')
                ->get();
                $saldoExisting = $saldoAwal;
                foreach ($allTrx as $valTrx) {
                    $debet = ( $valTrx->jenis == 1 )? $valTrx->total : 0;
                    $kredit = ( $valTrx->jenis == 2 )? $valTrx->total : 0;
                    $saldoExisting += $debet-$kredit;
                    $pushData = (object) 
                    [ 
                        'code' => $valTrx->code??$valTrx->reff,
                        'keterangan' => $valTrx->description,
                        'tgl_trx' => dateView($valTrx->date_transaction),
                        'type_name' => $valTrx->type_name,
                        'debet' =>   $debet,
                        'kredit' =>  $kredit,
                        'saldo' => $saldoExisting
                    ];
                    array_push($datas[$key][$keyAccount], $pushData);
                }
            }
        }

        $prevent_zero = $request->prevent_zero;
        $prevent_zero = $request->prevent_zero != null ?  $prevent_zero : 'false';

        // UNTUK MENGHAPUS ACCOUNTS DARI DATA YANG == 0
        if ($prevent_zero == "true") {
            foreach ($datas as $key => $value) {
                if (isset($value)) {
                    foreach ($value as $key1 => $value1) {
                        if(isset($value1)){
                            if (count($value1) == 1) {
                                $saldoLine = $value1[0];
                                if($saldoLine->saldo == 0){
                                    unset($accounts[$key][$key1]);
                                }
                            }
                        } else {
                            unset($accounts[$key][$key1]);
                        }
                    }
                }
            }
        }
        
        $items = [
            'type' => $request->type == 1 ? 'kas' : 'bank',
            'companies' => $companies,
            'accounts' => $accounts,
            'data' => $datas,
            'start' => $start_date_string,
            'end' => $end_date_string,
            'remark' => $remark
        ];

        return view('finance/kas_bank/kartu_preview', $items);
    }
}
