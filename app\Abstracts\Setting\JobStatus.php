<?php

namespace App\Abstracts\Setting;

use App\Abstracts\Operational\DeliveryOrderDriver;
use App\Abstracts\Operational\ManifestDetail;
use App\Model\JobOrder;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class JobStatus 
{
    protected static $table = 'job_statuses';

    public static function fetchFilter($args = []){
        $params = [];
        $params['is_job_order'] = $args['is_job_order'] ?? null;
        $params['job_order_id'] = $args['job_order_id'] ?? null;
        $params['location_id'] = $args['location_id'] ?? null;
        $params['type'] = $args['type'] ?? null;

        return $params;
    }

    /*
      Date : 05-03-2021
      Description : Mengquery data
      Developer : Didin
      Status : Create
    */
    public static function query($params = []) 
    {
        $request = self::fetchFilter($params);

        $next_statuses = DB::table(self::$table);
        $next_statuses = $next_statuses->where(self::$table . '.is_reject', 0);
        $next_statuses = $next_statuses->where(self::$table . '.is_cancel', 0);
        
        $dt = DB::table(self::$table);
        if($request['is_job_order'] == 1){
            $next_statuses = $next_statuses->where('is_repeat', 1);
            $next_statuses = $next_statuses->orWhere('is_finish', 1);
            // $dt = $dt->where(self::$table .'.is_repeat', 1);
        } else {
            if(empty($request['job_order_id'])){
                $dt = $dt->where(self::$table .'.type', '!=', 'optional');
                $dt = $dt->orWhere(self::$table .'.type', null);
            }
        }
        if(!empty($request['job_order_id'])){
            $jo = JobOrder::find($request['job_order_id']);
            // dd($jo);
            if($jo){
                if($jo->service_type_id == 28){
                    // $next_statuses = $next_statuses->whereIn('type', ['optional', 'pickup', 'delivery', null]);
                    // $dt = $dt->whereIn(self::$table .'.type', ['optional', 'pickup', 'delivery', null]);
                    // dd('pass');
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch9 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch9 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch9 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch9', 'next_statuses.urut');
                    });

                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                    // dd('pass', $request, $dt->get());
                } else if($jo->service_type_id == 23){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch4 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch4 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch4 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch4', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 20){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch1 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch1 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch1 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch1', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 21){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch2 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch2 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch2 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch2', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 22){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch3 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch3 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch3 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch3', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 24){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch5 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch5 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch5 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch5', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 25){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch6 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch6 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch6 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch6', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 26){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch7 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch7 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch7 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch7', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 27){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch8 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch8 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch8 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch8', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else if($jo->service_type_id == 31){
                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut_ch10 - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut_ch10 as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type_ch1 as type',
                    );
                    $dt = $dt->joinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut_ch10', 'next_statuses.urut');
                    });
                    if($request['type']){
                        $dt->where('next_statuses.type', $request['type']);
                    }
                } else {
                    // dd('passs',$jo);
                    $next_statuses = $next_statuses->where('type', '!=','optional');
                    // $next_statuses = $next_statuses->orWhere('type', null);
                    $dt = $dt->where(self::$table .'.type', '!=', 'optional');
                    // $dt = $dt->orWhere(self::$table .'.type', null);

                    $next_statuses = $next_statuses->select(
                        self::$table . '.id',
                        DB::raw(self::$table . '.urut - 1 AS urut'),
                        self::$table . '.name',
                        self::$table . '.slug',
                        self::$table . '.urut as urut_original',
                        self::$table . '.is_repeat',
                        self::$table . '.urut_full',
                        self::$table . '.type',
                    );
                    $dt = $dt->leftJoinSub($next_statuses, 'next_statuses', function($query){
                        $query->on(self::$table . '.urut', 'next_statuses.urut');
                    });
                    if($request['type']){
                        if($request['type'] == 'pickup'){
                            $dt->where('next_statuses.type', $request['type']);
                            $dt->orWhere(self::$table.'.slug', 'jobReceived');
                        } else {
                            $dt->where('next_statuses.type', $request['type']);
                        }
                    }
                    // dd($dt->get(), $request);
                }
            } else {
                // dd('non ch');
                $next_statuses = $next_statuses->select(
                    self::$table . '.id',
                    DB::raw(self::$table . '.urut - 1 AS urut'),
                    self::$table . '.name',
                    self::$table . '.slug',
                    self::$table . '.urut as urut_original',
                    self::$table . '.is_repeat',
                    self::$table . '.urut_full',
                    self::$table . '.type',
                );
                $dt = $dt->leftJoinSub($next_statuses, 'next_statuses', function($query){
                    $query->on(self::$table . '.urut', 'next_statuses.urut');
                });
                if($request['type']){
                    $dt->where(self::$table.'.type', $request['type']);
                }
            }
            // if($jo->service_type_id == 26){
            //     dd($jo);
            // }
        } else {
            // dd('non-ch');
            $next_statuses = $next_statuses->select(
                self::$table . '.id',
                DB::raw(self::$table . '.urut - 1 AS urut'),
                self::$table . '.name',
                self::$table . '.slug',
                self::$table . '.urut as urut_original',
                self::$table . '.is_repeat',
                self::$table . '.urut_full',
                self::$table . '.type',
            );
            $dt = $dt->leftJoinSub($next_statuses, 'next_statuses', function($query){
                $query->on(self::$table . '.urut', 'next_statuses.urut');
            });
            if($request['type']){
                $dt->where(self::$table.'.type', $request['type']);
            }
        }


        $dt->select(
            self::$table . '.*', 
            'next_statuses.name AS next_status_name',
            'next_statuses.slug AS next_status_slug',
            'next_statuses.type AS next_status_type',
            'next_statuses.urut_original AS next_status_urut',
            'next_statuses.urut_full AS next_status_urut_full',
            'next_statuses.id AS next_status_id',
            'next_statuses.is_repeat AS next_status_is_repeat',
        );
        $dt = $dt->orderBy('urut_full');
        // dd($dt->get());
        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Menampilkan daftar nama item condition
      Developer : Didin
      Status : Create
    */
    public static function index() {
        $dt = self::query();
        $dt = $dt->select('pieces.id', 'pieces.name');
        $dt = $dt->get();

        return $dt;
    }

    /*
      Date : 29-08-2020
      Description : Menampilkan Detail
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();

        return $dt;
    }

    /*
      Date : 29-08-2020
      Description : Menampilkan Detail berdasarkan nama
      Developer : Didin
      Status : Create
    */
    public static function showByName($value) {
        $value = strtolower($value);
        $dt = DB::table(self::$table)
        ->whereRaw("LOWER(`name`) = '$value'")
        ->first();

        return $dt;
    }

    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validateByName($value) {
        $res = false;
        $value = strtolower($value);
        $dt = DB::table(self::$table)
        ->whereRaw("LOWER(`name`) = '$value'")
        ->count(self::$table . '.id');

        if($dt > 0) {
            $res = true;
        }

        return $res;
    }
    
    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();

        if(!$dt) {
            throw new Exception('Company / branch not found');
        }
    }

    /*
      Date : 29-08-2021
      Description : Menyimpan data
      Developer : Didin
      Status : Create
    */
    public static function store($params) {
        $insert = self::fetch($params);
        $id = DB::table('pieces')->insertGetId($insert);

        return $id;
    }
    
    /*
      Date : 29-08-2021
      Description : Update data
      Developer : Didin
      Status : Create
    */
    public static function update($params, $id) {
        self::validate($id);
        $update = self::fetch($params);
        DB::table('pieces')
        ->whereId($id)
        ->update($update);
    }

    /*
      Date : 05-03-2021
      Description : Mengambil parameter
      Developer : Didin
      Status : Create
    */
    public static function fetch($args) {
        $params = [];
        $params['name'] = $args['name'] ?? null;
        self::validateInput($params);
        
        return $params;
    }


    /*
      Date : 14-03-2021
      Description : Validasi input
      Developer : Didin
      Status : Create
    */
    public static function validateInput($params) {
        if(!$params['name']) {
            throw new Exception('Piece / unit name is required');
        }
    }

    /*
      Date : 14-03-2021
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validate($id);
        DB::table(self::$table)
        ->whereId($id)
        ->delete();
    }

    public static function getStartedByDriverStatus() {
        $r = null;
        $dt = DB::table(self::$table);
        $dt = $dt->where(self::$table . '.slug', 'startedByDriver');
        $dt = $dt->first();

        if($dt) {
            $r = $dt->id;
        }

        return $r;
    }

    public static function getStartedByVendorStatus() {
        $r = null;
        $dt = DB::table(self::$table);
        $dt = $dt->where(self::$table . '.slug', 'startedByVendor');
        $dt = $dt->first();

        if($dt) {
            $r = $dt->id;
        }

        return $r;
    }

    public static function getAbortedStatus() {
        $r = null;
        $dt = DB::table(self::$table);
        $dt = $dt->where(self::$table . '.slug', 'aborted');
        $dt = $dt->first();

        if($dt) {
            $r = $dt->id;
        }

        return $r;
    }

    public static function showBySlug($slug) {
        $r = null;
        $dt = DB::table(self::$table);
        $dt = $dt->where(self::$table . '.slug', $slug);
        $dt = $dt->first();

        if($dt) {
            $r = $dt;
        }

        return $r;
    }
}
