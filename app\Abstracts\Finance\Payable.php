<?php
namespace App\Abstracts\Finance;
use DB;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use App\Utils\TransactionCode;
use App\Abstracts\Finance\PayableDetail;
use App\Model\Payable AS P;
use App\Model\PayableDetail AS PD;
class Payable
{
    protected static $table = 'payables';
    /*
      Date : 22-06-2021
      Description : Menangkap parameter untuk filter data
      Developer : Didin
      Status : Create
    */
    public static function fetchFilter($args = []) {
        $params = [];
        $params["status"] = $args["status"] ?? null;
        $params["contact_id"] = $args["contact_id"] ?? null;
        $params["company_id"] = $args["company_id"] ?? null;
        $params["start_date"] = $args["start_date"] ?? null;
        $params["end_date"] = $args["end_date"] ?? null;
        $params["year_date"] = $args["year_date"] ?? null;
        $params["start_due_date"] = $args["start_due_date"] ?? null;
        $params["end_due_date"] = $args["end_due_date"] ?? null;
        $params["status"] = $args["status"] ?? null;
        return $params;
    }
    /*
      Date : 22-06-2021
      Description : Menangkap parameter untuk filter data
      Developer : Didin
      Status : Create
    */
    public static function query($request = []) {
        $request = self::fetchFilter($request);
        $hutang = DB::table("payables")
        ->leftJoin('companies', 'companies.id', 'payables.company_id')
        ->leftJoin('contacts', 'contacts.id', 'payables.contact_id')
        ->leftJoin("journals", 'journals.id', 'payables.journal_id')
        // ->where('journals.status', 3)
        ->select(
            'payables.id',
            'payables.code',
            'payables.created_at',
            'payables.credit_note',
            'payables.credit as total_payable',
            'payables.debet as total_paid',
            'contacts.name AS contact_name',
            'companies.name AS company_name',
            'payables.date_transaction',
            'payables.date_tempo',
            DB::raw("DATEDIFF(NOW(), payables.date_tempo) AS umur"),
            DB::raw("(payables.credit-payables.debet) AS sisa"),
            DB::raw("(payables.credit-payables.debet) AS sisa_hutang"),
            DB::raw("CASE WHEN (payables.credit-payables.debet) = 0 THEN 1 WHEN payables.is_cost = 1 THEN 2 WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 2 ELSE 3 END AS status")
        );
        if (auth()->user()->is_admin == 0 && auth()->user()->is_multibranch == 0)
            $hutang->where('payables.company_id', auth()->user()->company_id);
        if($request['contact_id'])
            $hutang->where('payables.contact_id', $request['contact_id']);
        if($request['company_id'])
            $hutang->where('payables.company_id', $request['company_id']);
        if($request['start_date'])
            $hutang->where('payables.date_transaction', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_date']));
        if($request['end_date'])
            $hutang->where('payables.date_transaction', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_date']));
        if ($request['year_date']) {
            $year = Carbon::parse($request['year_date'])->year;
            $hutang->whereYear("payables.date_transaction", '=' , $year);
        }
        if($request['start_due_date'])
            $hutang->where('date_tempo', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_due_date']));
        if($request['end_due_date'])
            $hutang->where('date_tempo', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_due_date']));
        $hutang = $hutang->orderByRaw('payables.date_transaction DESC, payables.id DESC');
        $hutang = DB::query()->fromSub($hutang, 'payables');
        if($request['status']) {
            $hutang = $hutang->where('status', $request['status']);
        }
        return $hutang;
    }

    public static function query2($request = []) {
        $request = self::fetchFilter($request);
        $hutang = DB::table("payables")
        ->leftJoin('companies', 'companies.id', 'payables.company_id')
        ->leftJoin('contacts', 'contacts.id', 'payables.contact_id')
        ->leftJoin('journals', 'journals.id', 'payables.journal_id')
        ->where('status_cr', '!=', 'Pusat')
        ->where('payables.cash_request', '<>', null)
        ->select(
            'payables.id',
            'payables.code',
            'payables.created_at',
            'payables.credit_note',
            'contacts.name AS contact_name',
            'companies.name AS company_name',
            'payables.date_transaction',
            'payables.date_tempo',
            'payables.cash_request',
            'payables.cr_desc',
            'payables.cr_date',
            'payables.status_cr',
            // 'payables.priority',
            DB::raw("DATEDIFF(NOW(), payables.date_tempo) AS umur"),
            DB::raw("(payables.credit-payables.debet) AS sisa"),
            DB::raw("(payables.credit-payables.debet) AS sisa_hutang"),
            DB::raw("CASE WHEN (payables.credit-payables.debet) = 0 THEN 1 WHEN payables.is_cost = 1 THEN 2 WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 2 ELSE 3 END AS status")
        );
        if(auth()->user()->is_admin==0)
            $hutang->where('company_id', auth()->user()->company_id);
        if($request['contact_id'])
            $hutang->where('contact_id', $request['contact_id']);
        if($request['company_id'])
            $hutang->where('payables.company_id', $request['company_id']);
        if($request['start_date'])
            $hutang->where('payables.date_transaction', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_date']));
        if($request['end_date'])
            $hutang->where('payables.date_transaction', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_date']));
        if($request['start_due_date'])
            $hutang->where('date_tempo', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_due_date']));
        if($request['end_due_date'])
            $hutang->where('date_tempo', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_due_date']));
        $hutang = $hutang->orderByRaw('payables.date_transaction DESC, payables.id DESC');
        $hutang = DB::query()->fromSub($hutang, 'payables');
        if($request['status']) {
            $hutang = $hutang->where('status', $request['status']);
        }
        return $hutang;
    }

    public static function query4($request = [],$id) {
        $request = self::fetchFilter($request);
        $hutang = DB::table("payables")
        ->leftJoin('companies', 'companies.id', 'payables.company_id')
        ->leftJoin('contacts', 'contacts.id', 'payables.contact_id')
        ->leftJoin('journals', 'journals.id', 'payables.journal_id')
        ->where('payables.company_id', $id)
        ->where('payables.cash_request', '<>', null)
        ->select(
            'payables.id',
            'payables.code',
            'payables.created_at',
            'payables.credit_note',
            'contacts.name AS contact_name',
            'companies.name AS company_name',
            'payables.date_transaction',
            'payables.date_tempo',
            'payables.cash_request',
            'payables.cr_desc',
            'payables.cr_date',
            'payables.status_cr',
            DB::raw("DATEDIFF(NOW(), payables.date_tempo) AS umur"),
            DB::raw("(payables.credit-payables.debet) AS sisa"),
            DB::raw("(payables.credit-payables.debet) AS sisa_hutang"),
            DB::raw("CASE WHEN (payables.credit-payables.debet) = 0 THEN 1 WHEN payables.is_cost = 1 THEN 2 WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 2 ELSE 3 END AS status")
        );
        if(auth()->user()->is_admin==0)
            $hutang->where('company_id', auth()->user()->company_id);
        if($request['contact_id'])
            $hutang->where('contact_id', $request['contact_id']);
        if($request['company_id'])
            $hutang->where('payables.company_id', $request['company_id']);
        if($request['start_date'])
            $hutang->where('payables.date_transaction', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_date']));
        if($request['end_date'])
            $hutang->where('payables.date_transaction', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_date']));
        if($request['start_due_date'])
            $hutang->where('date_tempo', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_due_date']));
        if($request['end_due_date'])
            $hutang->where('date_tempo', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_due_date']));
        $hutang = $hutang->orderByRaw('payables.date_transaction DESC, payables.id DESC');
        $hutang = DB::query()->fromSub($hutang, 'payables');
        if($request['status']) {
            $hutang = $hutang->where('status', $request['status']);
        }
        return $hutang;
    }

    public static function query3($request = []) {
        $request = self::fetchFilter($request);
        $hutang = DB::table("payables")
        ->leftJoin('companies', 'companies.id', 'payables.company_id')
        ->leftJoin('contacts', 'contacts.id', 'payables.contact_id')
        ->leftJoin('journals', 'journals.id', 'payables.journal_id')
        ->where('status_cr', 'Pusat')
        ->where('payables.cash_request', '<>', null)
        ->select(
            'payables.id',
            'payables.code',
            'payables.created_at',
            'payables.credit_note',
            'contacts.name AS contact_name',
            'companies.name AS company_name',
            'payables.date_transaction',
            'payables.date_tempo',
            'payables.cash_request',
            'payables.cr_desc',
            'payables.cr_date',
            'payables.status_cr',
            DB::raw("DATEDIFF(NOW(), payables.date_tempo) AS umur"),
            DB::raw("(payables.credit-payables.debet) AS sisa"),
            DB::raw("(payables.credit-payables.debet) AS sisa_hutang"),
            DB::raw("CASE WHEN (payables.credit-payables.debet) = 0 THEN 1 WHEN payables.is_cost = 1 THEN 2 WHEN DATEDIFF(NOW(), payables.date_tempo) > 0 THEN 2 ELSE 3 END AS status")
        );
        if(auth()->user()->is_admin==0)
            $hutang->where('payables.company_id', auth()->user()->company_id);
        if($request['contact_id'])
            $hutang->where('payables.contact_id', $request['contact_id']);
        if($request['company_id'])
            $hutang->where('payables.company_id', $request['company_id']);
        if($request['start_date'])
            $hutang->where('payables.date_transaction', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_date']));
        if($request['end_date'])
            $hutang->where('payables.date_transaction', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_date']));
        if($request['start_due_date'])
            $hutang->where('date_tempo', '>=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['start_due_date']));
        if($request['end_due_date'])
            $hutang->where('date_tempo', '<=', preg_replace('/(\d+)-(\d+)-(\d+)/', '$3-$2-$1', $request['end_due_date']));
        $hutang = $hutang->orderByRaw('payables.date_transaction DESC, payables.id DESC');
        $hutang = DB::query()->fromSub($hutang, 'payables');
        if($request['status']) {
            $hutang = $hutang->where('status', $request['status']);
        }
        return $hutang;
    }
    /*
      Date : 05-03-2021
      Description : Memvalidasi data
      Developer : Didin
      Status : Create
    */
    public static function validate($id) {
        $dt = DB::table(self::$table)
        ->whereId($id)
        ->first();
        if(!$dt) {
            throw new Exception('Data not found');
        }
    }
    /*
      Date : 29-08-2021
      Description : Menampilkan detail kategori barang
      Developer : Didin
      Status : Create
    */
    public static function show($id) {
        self::validate($id);
        $dt = DB::table(self::$table);
        $dt = $dt->whereId($id);
        $dt = $dt->first();
        return $dt;
    }
    /*
      Date : 14-03-2021
      Description : Hapus data
      Developer : Didin
      Status : Create
    */
    public static function destroy($id) {
        self::validate($id);
        PayableDetail::clear($id);
        DB::table(self::$table)
        ->whereId($id)
        ->delete();
    }
    /*
      Date : 14-03-2021
      Description : Mendapatkan sisa hutang
      Developer : Didin
      Status : Create
    */
    public static function getSisa($params = []) {
        $raw = self::query($params);
        $r = $raw->sum(DB::raw("sisa"));
        return $r;
    }
      /*
      Date : 24-11-2021
      Description : Store Data Payable
      Developer : Atika
      Status : Create
    */
    public static function store_data($params) {
        // dd($params['detail']);
        // die();
        $payable= new P;
        $payable->company_id= $params['company_id'];
        $payable->contact_id= $params['contact_id'];
        $payable->type_transaction_id = $params['type_transaction_id'];
        $payable->journal_id = $params['journal_id'] ?? NULL;
        $payable->relation_id = $params['relation_id'] ?? NULL;
        $payable->created_by = $params['created_by'] ?? NULL;
        $payable->code = $params['code'];
        $payable->date_transaction = $params['created_at'];
        $payable->date_tempo = $params['date_tempo'] ?? NULL;
        $payable->description = $params['description'] ?? NULL;
        $payable->debet = $params['debet'];
        $payable->credit = $params['credit'];
        $payable->is_temporary = $params['is_temporary'] ?? 0;
        $payable->period = $params['period'] ?? 0;
        $payable->save();
        foreach($params['detail'] as $d){
            $payable_detail= new PD;
            $payable_detail->header_id = $payable->id;
            $payable_detail->journal_id = $d['journal_id'] ?? NULL;
            $payable_detail->type_transaction_id = $d['type_transaction_id'];
            $payable_detail->relation_id = $d['relation_id'] ?? NULL;
            $payable_detail->code = $d['code'] ?? NULL;
            $payable_detail->date_transaction = $d['created_at'];
            $payable_detail->debet = $d['debet'];
            $payable_detail->credit = $d['credit'];
            $payable_detail->description = $d['description'];
            $payable_detail->save();
        }
    }
}
