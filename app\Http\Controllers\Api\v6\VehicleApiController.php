<?php
// <!-- Date : 04-08-2022 By : Felix
// Description : [Bug] menu edit trailer -->
namespace App\Http\Controllers\Api\v6;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Vehicle\VehicleController;
use App\Model\Vehicle;
use App\Model\VehicleDistance;
use App\Model\VehicleChecklistItem;
use App\Model\VehicleMaintenance;
use App\Model\VehicleMaintenanceDetail;
use App\Model\TargetRate;
use App\Model\VehicleChecklistDetailBody;
use App\Model\VehicleChecklistDetailItem;
use App\Model\VehicleContact;
use App\Model\VehicleTire;
use App\Model\VehicleMutationRequest;
use App\Model\VehicleMutationIn;
use App\Model\VehicleMutationOut;
use App\Model\VehicleDocument;
use App\Model\VehicleInsurance;
use App\Model\VehicleTrailer;
use Carbon\Carbon;
use Response;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Mockery\Undefined;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class VehicleApiController extends Controller
{
    public function vehicle_datatable(Request $request)
    {
        // dd($request->is_internal);
        $wr = "1=1";
        if($request->company_id){
          $wr .= " AND vehicles.company_id =". (int)$request->company_id;
        }
        if($request->vehicle_type_id){
          $wr .= " AND vehicle_variants.vehicle_type_id =". (int)$request->vehicle_type_id;
        }
        if($request->is_internal != null){
          $wr .= " AND vehicles.is_internal =". (int)$request->is_internal;
        }
        // if($request->filled('is_trailer') && $request->is_trailer == 1){
        //   $wr.=" AND vehicles.is_trailer = 1";
        // }else{
        //   $wr.=" AND vehicles.is_trailer = 0";
        // }
        $item = Vehicle::with('company','company.area','vehicle_variant','vehicle_variant.vehicle_type','supplier')
        ->whereRaw($wr)
        ->when($request->filled('is_trailer'), function($query) use($request) {
            if ($request->is_trailer == 1) {
                $query->where('vehicles.is_trailer', 1);
            } else {
                $query->where('vehicles.is_trailer', 0);
            }
        })
        ->select('vehicles.*', 'vehicle_variants.vehicle_type_id')
        ->leftJoin('vehicle_variants', 'vehicle_variants.id', 'vehicles.vehicle_variant_id');
        
        
        $item = $item->orderBy('created_at', 'desc');
        $item = $item->paginate($request->length);
        return response()->json($item);

        // return DataTables::of($item)
        // ->addColumn('action', function($item){
        //   $html = "";
        //   if($item->is_trailer){
        //     $html = "";
        //     $html.="<a ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //     // [Bug] menu edit trailer
        //     // if(!$item->nopol){
        //       $html.="<a ui-sref=\"vehicle.vehicle_trailer.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
        //     // }
        //     // end [Bug] menu edit trailer
        //     $html.="<a ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
        //   }else{
        //     $html = "";
        //     $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.detail')\" ui-sref=\"vehicle.vehicle.show({id:$item->id})\"><span class='fa fa-folder-o'  data-toggle='tooltip' title='Detail Data'></span></a>&nbsp;&nbsp;";
        //     $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.edit')\" ui-sref=\"vehicle.vehicle.edit({id:$item->id})\"><span class='fa fa-edit' data-toggle='tooltip' title='Edit Data'></span></a>&nbsp;&nbsp;";
        //     $html.="<a ng-show=\"roleList.includes('vehicle.vehicle.delete')\" ng-click=\"deletes($item->id)\"><span class='fa fa-trash-o'></span></a>";
        //   }
        //     return $html;
        // })
        // ->addColumn('head_trailer', function($item) {
        //     if($item->is_trailer) {
        //         return 'Trailer';
        //     } else {
        //         return 'Head';
        //     }
        // })
        // // ->filter(function($query) use ($request ) {
        // //     if(isset($request->is_internal)) {
        // //         $query->whereIsInternal($request->is_internal ?? 0);
        // //     }

        // //     if(isset($request->search['value'])){
        // //         $query->where(DB::raw('LOWER(vehicles.nopol)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        // //         $query->orWhere(DB::raw('LOWER(vehicles.code)'), 'LIKE', '%'.strtolower($request->search['value']).'%');
        // //     }

        // //     if(isset($request->vehicle_type_id)) {
        // //         $vehicle_type_id = $request->vehicle_type_id;
        // //         $query->whereHas('vehicle_variant', function (Builder $q) use ($vehicle_type_id) {
        // //             $q->where('vehicle_type_id', $vehicle_type_id);
        // //         });
        // //     }
        // // })
        // ->editColumn('last_km', function($item){
        //     return number_format($item->last_km).' km';
        // })
        // ->rawColumns(['action'])
        // ->make(true);
    }

    public function vehicle_detail($id) {
        $data['detail_info'] = Vehicle::with(
            'company',
            'vehicle_variant',
            'supplier',
            'vehicle_owner',
            'vehicle_variant.vehicle_type:id,name',
            'vehicle_variant.vehicle_joint.vehicle_joint_details',
            'trailer:id,code'
        )->where('id', $id)->first();

        $data['driver'] = VehicleContact::with('contact')->whereRaw("vehicle_id = $id")->get();

        $data['insurance'] = VehicleInsurance::with('insurance')->where('vehicle_id', $id)->get();

        $data['body_vehicle'] = VehicleChecklistItem::whereRaw("vehicle_id = $id")->orderBy('date_transaction', 'desc')->first();
        if (isset($data['body_vehicle'])) {
            $data['body_vehicle']['detail_body'] = VehicleChecklistDetailBody::with('vehicle_body')->where('header_id', $data['body_vehicle']->id)->get();
            $data['body_vehicle']['detail_checklist'] = VehicleChecklistDetailItem::with('vehicle_checklist')->where('header_id', $data['body_vehicle']->id)->get();
        } else {
            $data['body_vehicle']['detail_body'] = null;
            $data['body_vehicle']['detail_checklist'] = null;
        }

        $data['document'] = VehicleDocument::where('vehicle_id', $id)->get();

        $data['ritase'] = DB::select("select distinct year(period) as tahun from target_rates where vehicle_id = $id");

        return response()->json($data);

    }

    public function position(Request $request) {
      $position = DB::table('vehicles')
        ->select(
            'vehicles.*',
            'delivery_order_drivers.id as delivery_order_id',
            DB::raw('IFNULL(contacts.name, "Kosong") as name_contact'),
            DB::raw('CASE
                WHEN job_statuses.name IN ("Assigned To Driver", "Terima Job") THEN "Terima Job"
                WHEN job_statuses.name IN ("Done & Checked", "Selesai") THEN "Selesai"
                ELSE "Kosong"
            END AS job_status_name'),
            DB::raw('IFNULL(MAX(manifests.code), "Kosong") as code_manifest'),
            DB::raw('MAX(manifests.id) as id_manifest'),
            'routes.name as route_name',
            'cities.name as city_from',
            'c.name as city_to',
            'delivery_order_drivers.job_status_id as delivery_job_status_id',
            'delivery_order_drivers.updated_at as delivery_order_driver_update_at'
        )
        ->leftJoin('delivery_order_drivers', function ($join) {
            $join->on('delivery_order_drivers.id', '=', 'vehicles.delivery_id')
                ->on('delivery_order_drivers.vehicle_id', '=', 'vehicles.id');
        })
        ->leftJoin('contacts', 'contacts.id', '=', 'delivery_order_drivers.driver_id')
        ->leftJoin('job_statuses', 'job_statuses.id', '=', 'delivery_order_drivers.job_status_id')
        ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
        ->leftJoin('manifests', function ($join) {
            $join->on('manifests.id', '=', 'delivery_manifests.manifest_id')
                ->on('manifests.vehicle_id', '=', 'vehicles.id');
        })
        ->leftJoin('routes', 'routes.id', '=', 'manifests.route_id')
        ->leftJoin('cities', 'cities.id', '=', 'routes.city_from')
        ->leftJoin('cities as c', 'c.id', '=', 'routes.city_to')
        ->where('vehicles.nopol', '!=', '')
        ->where('vehicles.is_trailer', 0)
        ->where('vehicles.last_latitude', '!=', 0)
        ->where('vehicles.last_longitude', '!=', 0)
        ->where('vehicles.no_imei', '!=', '')
        ->where('vehicles.gps_no', '!=', '')
        ->where('vehicles.is_active', 1)
        ->groupBy('vehicles.id')
        ->orderBy('last_update_latlng', 'desc')
        ->get();

      $all = $position->count();
      $gps = $position->where('last_update_latlng', '>=', Carbon::today())->count();
      $kosong = $position->whereIn('delivery_job_status_id', ['11', '12', '13', '14', null])
          ->count();
      $on_working = $position->whereNotIn('delivery_job_status_id', ['11', '12', '13', '14', null])
          ->count();
      $data['position'] = [
        'all' => $all,
        'gps' => $gps,
        'kosong' => $kosong,
        'on_working' => $on_working,
      ];

      $data['data_vehicle'] = DB::table('vehicles')
      ->select(
        'vehicles.id',
        'vehicles.last_longitude',
        'vehicles.last_latitude',
        'vehicles.nopol',
        'delivery_order_drivers.id as delivery_order_id',
        DB::raw('IFNULL(contacts.name, "Kosong") as name_contact'),
        DB::raw('IFNULL(MAX(manifests.code), "Kosong") as code_manifest'),
        DB::raw('MAX(manifests.id) as id_manifest'),
        DB::raw('GROUP_CONCAT(log_status.name) as logs'),
        // DATE_FORMAT(updated_at,'%d-%m-%Y %H:%i') as up_at
        DB::raw('GROUP_CONCAT(delivery_order_status_logs.created_at) as logs_time'),
        'routes.name as route_name',
        'cities.name as city_from',
        'c.name as city_to',
        'delivery_order_drivers.job_status_id as delivery_job_status_id',
        'job_statuses.name as job_status_name',
        'delivery_order_drivers.updated_at as delivery_order_driver_update_at'
      )
      ->leftJoin('delivery_order_drivers', function ($join) {
          $join->on('delivery_order_drivers.id', '=', 'vehicles.delivery_id')
              ->on('delivery_order_drivers.vehicle_id', '=', 'vehicles.id');
      })
      ->leftJoin('contacts', 'contacts.id', '=', 'delivery_order_drivers.driver_id')
      ->leftJoin('job_statuses', 'job_statuses.id', '=', 'delivery_order_drivers.job_status_id')
      ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
      ->leftJoin('manifests', function ($join) {
          $join->on('manifests.id', '=', 'delivery_manifests.manifest_id')
              ->on('manifests.vehicle_id', '=', 'vehicles.id');
      })
      ->leftJoin('delivery_order_status_logs', function ($join) {
        $join->on('delivery_order_drivers.id', '=', 'delivery_order_status_logs.delivery_order_driver_id')
             ->orderBy('delivery_order_status_logs.id', 'desc');
    })
      ->leftJoin('job_statuses as log_status', 'delivery_order_status_logs.job_status_id', 'log_status.id')
      ->leftJoin('routes', 'routes.id', '=', 'manifests.route_id')
      ->leftJoin('cities', 'cities.id', '=', 'routes.city_from')
      ->leftJoin('cities as c', 'c.id', '=', 'routes.city_to')
      ->where('vehicles.is_active', 1)
      ->where('vehicles.nopol', '!=', '')
      ->where('vehicles.is_trailer', 0)
      ->where('vehicles.last_latitude', '!=', 0)
      ->where('vehicles.last_longitude', '!=', 0)
      ->where('vehicles.no_imei', '!=', '')
      ->where('vehicles.gps_no', '!=', '')
      ->groupBy('vehicles.id')
      ->orderBy('last_update_latlng', 'desc')
      ->paginate($request->length);

      return response()->json($data);
    }

    public function standby(Request $request) {
        $data = DB::table('vehicles')
          ->select(
            'vehicles.id',
            'vehicles.last_longitude',
            'vehicles.last_latitude',
            'vehicles.nopol',
            'delivery_order_drivers.id as delivery_order_id',
            DB::raw('IFNULL(contacts.name, "Kosong") as name_contact'),
            DB::raw('CASE
                WHEN job_statuses.name IN ("Assigned To Driver", "Terima Job") THEN "Terima Job"
                WHEN job_statuses.name IN ("Done & Checked", "Selesai") THEN "Selesai"
                ELSE "Kosong"
            END AS job_status_name'),
            DB::raw('IFNULL(MAX(manifests.code), "Kosong") as code_manifest'),
            DB::raw('MAX(manifests.id) as id_manifest'),
            'routes.name as route_name',
            'cities.name as city_from',
            'c.name as city_to',
            'delivery_order_drivers.job_status_id as delivery_job_status_id',
            'delivery_order_drivers.updated_at as delivery_order_driver_update_at'
          )
          ->leftJoin('delivery_order_drivers', function ($join) {
              $join->on('delivery_order_drivers.id', '=', 'vehicles.delivery_id')
                  ->on('delivery_order_drivers.vehicle_id', '=', 'vehicles.id');
          })
          ->leftJoin('contacts', 'contacts.id', '=', 'delivery_order_drivers.driver_id')
          ->leftJoin('job_statuses', 'job_statuses.id', '=', 'delivery_order_drivers.job_status_id')
          ->leftJoin('delivery_manifests', 'delivery_manifests.delivery_order_driver_id', '=', 'delivery_order_drivers.id')
          ->leftJoin('manifests', function ($join) {
              $join->on('manifests.id', '=', 'delivery_manifests.manifest_id')
                  ->on('manifests.vehicle_id', '=', 'vehicles.id');
          })
          ->leftJoin('routes', 'routes.id', '=', 'manifests.route_id')
          ->leftJoin('cities', 'cities.id', '=', 'routes.city_from')
          ->leftJoin('cities as c', 'c.id', '=', 'routes.city_to')
          ->where('vehicles.nopol', '!=', '')
          ->where('vehicles.is_trailer', 0)
          ->where('vehicles.last_latitude', '!=', 0)
          ->where('vehicles.last_longitude', '!=', 0)
          ->where('vehicles.no_imei', '!=', '')
          ->where('vehicles.gps_no', '!=', '')
          ->where('vehicles.is_active', 1)
          ->whereIn('delivery_order_drivers.job_status_id', ['11', '12', '13', '14', null])
          ->groupBy('vehicles.id')
          ->orderBy('last_update_latlng', 'desc')
          ->paginate($request->length ?? 10);
  
        return response()->json($data);
      }
}
